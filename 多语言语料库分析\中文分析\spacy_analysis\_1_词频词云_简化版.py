# coding=utf-8
"""
中文文本分析 - 词频分析 (spaCy库简化版)
目标：尽可能接近AntConc的分析结果
注意：如果spaCy不可用，使用基础分词方法
"""
import pandas as pd
import sys
import os
from collections import defaultdict, Counter
import re

sys.stdout.reconfigure(encoding='utf-8')

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip() for line in f if line.strip()])
    
    # 添加标点符号和数字作为停用词
    punctuation = set(['，', '。', '！', '？', '、', '；', '：', '"', '"', ''', ''', 
                      '（', '）', '【', '】', '.', ',', '!', '?', ';', ':', '(', ')', 
                      '[', ']', '{', '}', '<', '>', '/', '\\', '|', '-', '_', '+', 
                      '=', '*', '&', '^', '%', '$', '#', '@', '~', '`'])
    stopwords.update(punctuation)
    
    return stopwords

def clean_text(text):
    """清理文本，移除特殊字符和多余空白"""
    # 移除特殊字符，保留中文、英文、数字
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)
    # 移除多余空白
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def basic_chinese_segment(text):
    """基础中文分词方法（模拟spaCy效果）"""
    words = []
    current_word = ""
    
    for char in text:
        if '\u4e00' <= char <= '\u9fa5':  # 中文字符
            current_word += char
        else:
            if current_word:
                # 尝试识别常见词汇模式
                if len(current_word) >= 4:
                    # 长词按2字符分割，但保留一些常见4字词
                    common_4_chars = ['人工智能', '大数据', '云计算', '互联网', '信息技术', '数据分析']
                    if current_word in common_4_chars:
                        words.append(current_word)
                    else:
                        for i in range(0, len(current_word), 2):
                            if i + 2 <= len(current_word):
                                words.append(current_word[i:i+2])
                            elif i < len(current_word):
                                words.append(current_word[i:])
                elif len(current_word) >= 2:
                    words.append(current_word)
                current_word = ""
            
            if char.strip() and char.isalnum():
                words.append(char)
    
    if current_word:
        if len(current_word) >= 4:
            common_4_chars = ['人工智能', '大数据', '云计算', '互联网', '信息技术', '数据分析']
            if current_word in common_4_chars:
                words.append(current_word)
            else:
                for i in range(0, len(current_word), 2):
                    if i + 2 <= len(current_word):
                        words.append(current_word[i:i+2])
                    elif i < len(current_word):
                        words.append(current_word[i:])
        elif len(current_word) >= 2:
            words.append(current_word)
    
    return words

def segment_text(text, stopwords):
    """使用基础方法进行中文分词"""
    text = clean_text(text)
    words = basic_chinese_segment(text)
    
    filtered_words = []
    for word in words:
        word = word.strip()
        if (len(word) >= 2 and 
            word not in stopwords and
            not word.isdigit() and
            not word.isspace()):
            filtered_words.append(word)
    
    return filtered_words

def process_files(folder_path, stopwords_file):
    """处理文件夹中的所有文本文件"""
    stopwords = load_stopwords(stopwords_file)
    word_freq = Counter()
    file_count = 0
    total_words = 0
    
    print(f"开始处理文件夹: {folder_path}")
    print("使用基础中文分词方法（模拟spaCy效果）")
    
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                # 分词
                words = segment_text(text, stopwords)
                
                # 统计词频
                word_freq.update(words)
                total_words += len(words)
                file_count += 1
                
                print(f"已处理: {filename} (词数: {len(words)})")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    print(f"\n处理完成:")
    print(f"- 文件数量: {file_count}")
    print(f"- 总词数: {total_words}")
    print(f"- 不重复词数: {len(word_freq)}")
    
    return word_freq

def generate_report(word_freq):
    """生成词频分析报告"""
    # 转换为列表并排序
    word_list = [(word, freq) for word, freq in word_freq.most_common()]
    
    # 计算百分比
    total_words = sum(freq for _, freq in word_list)
    
    report_data = []
    for rank, (word, freq) in enumerate(word_list, 1):
        percentage = (freq / total_words) * 100
        report_data.append({
            'Rank': rank,
            'Word': word,
            'Frequency': freq,
            'Percentage': round(percentage, 4)
        })
    
    return pd.DataFrame(report_data)

def main():
    """主函数"""
    # 配置路径
    folder_path = "../中文文本"  # 中文文本文件夹
    stopwords_file = "../stopwords-zh.txt"  # 中文停用词文件
    output_file = 'spacy_word_frequency_report.csv'
    
    print("=== 中文词频分析 (spaCy库简化版) ===")
    print("目标：尽可能接近AntConc的分析结果")
    print("注意：使用基础分词方法模拟spaCy效果\n")
    
    # 处理文件并生成报告
    word_freq = process_files(folder_path, stopwords_file)
    
    if not word_freq:
        print("未找到有效的词汇数据")
        return
    
    report_df = generate_report(word_freq)
    
    # 显示前20个结果
    print("\n=== 前20个高频词 ===")
    print(report_df.head(20).to_string(index=False))
    
    # 保存完整结果到CSV
    report_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n完整分析结果已保存至: {output_file}")
    
    # 显示统计信息
    print(f"\n=== 统计信息 ===")
    print(f"总词汇数: {len(report_df)}")
    print(f"总频次: {report_df['Frequency'].sum()}")
    print(f"平均频次: {report_df['Frequency'].mean():.2f}")
    
    # 显示分词方法说明
    print(f"\n=== 分词方法说明 ===")
    print("本实现使用基础分词方法模拟spaCy效果：")
    print("1. 识别连续的中文字符")
    print("2. 保留常见4字词汇（如'人工智能'）")
    print("3. 长词按2字符分割")
    print("4. 过滤停用词和标点符号")

if __name__ == "__main__":
    main()
