<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts-wordcloud.min.js"></script>

    
</head>
<body >
            <style>
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
        }

        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 12px 16px;
            transition: 0.3s;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #ccc;
        }

        .chart-container {
            display: block;
        }

        .chart-container:nth-child(n+2) {
            display: none;
        }
    </style>
    <div class="tab">
            <button class="tablinks" onclick="showChart(event, 'bb44e69756764afcae0e526681453e06')">首页</button>
            <button class="tablinks" onclick="showChart(event, '102c0707bfbd43d5bdc116a90ae571e1')">词频分析-词云图</button>
            <button class="tablinks" onclick="showChart(event, 'cc2595966c2048808504e3ff90518feb')">词频分析-柱状图</button>
            <button class="tablinks" onclick="showChart(event, '8c31593b427b48f19b67c05a9a87706e')">KWIC分析</button>
            <button class="tablinks" onclick="showChart(event, 'cc25a4aab25341878fcd2aea2537820c')">词语分布-频率图</button>
            <button class="tablinks" onclick="showChart(event, '2dfd7b36407648d598531e73ec0762bf')">词语分布-详细数据</button>
            <button class="tablinks" onclick="showChart(event, '3372a60396c24c2fa5e534e7012eabbd')">词簇分析-频率图</button>
            <button class="tablinks" onclick="showChart(event, '534304f419394afbbd468669e8f0bc9e')">词簇分析-详细数据</button>
            <button class="tablinks" onclick="showChart(event, '6bc226dbb22c4dffa1ab3f71ccdf5690')">搭配分析-频率图</button>
            <button class="tablinks" onclick="showChart(event, 'a8265cded0c146bfb223310130e2d991')">搭配分析-详细数据</button>
    </div>

    <div class="box">
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="bb44e69756764afcae0e526681453e06" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>分析类型</th>
            <th>说明</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>词频分析</td>
            <td>查看文本中词语出现的频率、词云图等</td>
        </tr>
        <tr>
            <td>KWIC分析</td>
            <td>关键词上下文索引，查看特定词语的上下文环境</td>
        </tr>
        <tr>
            <td>词语分布分析</td>
            <td>分析词语在不同文件中的分布情况</td>
        </tr>
        <tr>
            <td>词簇分析</td>
            <td>分析词语簇的分布和频率</td>
        </tr>
        <tr>
            <td>搭配分析</td>
            <td>分析词语的常见搭配</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="102c0707bfbd43d5bdc116a90ae571e1" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('102c0707bfbd43d5bdc116a90ae571e1').style.width = document.getElementById('102c0707bfbd43d5bdc116a90ae571e1').parentNode.clientWidth + 'px';
        var chart_102c0707bfbd43d5bdc116a90ae571e1 = echarts.init(
            document.getElementById('102c0707bfbd43d5bdc116a90ae571e1'), 'white', {renderer: 'canvas'});
        var option_102c0707bfbd43d5bdc116a90ae571e1 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "wordCloud",
            "shape": "circle",
            "rotationRange": [
                -90,
                90
            ],
            "rotationStep": 45,
            "girdSize": 20,
            "sizeRange": [
                15,
                80
            ],
            "data": [
                {
                    "name": "the",
                    "value": 200,
                    "textStyle": {
                        "color": "rgb(90,11,147)"
                    }
                },
                {
                    "name": "a",
                    "value": 96,
                    "textStyle": {
                        "color": "rgb(81,117,87)"
                    }
                },
                {
                    "name": "of",
                    "value": 89,
                    "textStyle": {
                        "color": "rgb(81,78,115)"
                    }
                },
                {
                    "name": "to",
                    "value": 84,
                    "textStyle": {
                        "color": "rgb(46,69,156)"
                    }
                },
                {
                    "name": "and",
                    "value": 83,
                    "textStyle": {
                        "color": "rgb(68,14,55)"
                    }
                },
                {
                    "name": "\u2019",
                    "value": 77,
                    "textStyle": {
                        "color": "rgb(4,51,19)"
                    }
                },
                {
                    "name": "s",
                    "value": 69,
                    "textStyle": {
                        "color": "rgb(0,63,100)"
                    }
                },
                {
                    "name": "in",
                    "value": 61,
                    "textStyle": {
                        "color": "rgb(65,80,120)"
                    }
                },
                {
                    "name": "us",
                    "value": 49,
                    "textStyle": {
                        "color": "rgb(64,16,125)"
                    }
                },
                {
                    "name": "china",
                    "value": 43,
                    "textStyle": {
                        "color": "rgb(69,127,150)"
                    }
                },
                {
                    "name": "on",
                    "value": 37,
                    "textStyle": {
                        "color": "rgb(93,28,29)"
                    }
                },
                {
                    "name": "is",
                    "value": 36,
                    "textStyle": {
                        "color": "rgb(48,68,150)"
                    }
                },
                {
                    "name": "as",
                    "value": 35,
                    "textStyle": {
                        "color": "rgb(100,113,95)"
                    }
                },
                {
                    "name": "\u201d",
                    "value": 34,
                    "textStyle": {
                        "color": "rgb(139,115,152)"
                    }
                },
                {
                    "name": "\u201c",
                    "value": 34,
                    "textStyle": {
                        "color": "rgb(57,29,141)"
                    }
                },
                {
                    "name": "trump",
                    "value": 33,
                    "textStyle": {
                        "color": "rgb(51,79,148)"
                    }
                },
                {
                    "name": "for",
                    "value": 26,
                    "textStyle": {
                        "color": "rgb(82,35,50)"
                    }
                },
                {
                    "name": "by",
                    "value": 25,
                    "textStyle": {
                        "color": "rgb(61,61,156)"
                    }
                },
                {
                    "name": "it",
                    "value": 23,
                    "textStyle": {
                        "color": "rgb(51,130,47)"
                    }
                },
                {
                    "name": "chinese",
                    "value": 23,
                    "textStyle": {
                        "color": "rgb(91,113,28)"
                    }
                },
                {
                    "name": "\u2013",
                    "value": 22,
                    "textStyle": {
                        "color": "rgb(30,72,29)"
                    }
                },
                {
                    "name": "that",
                    "value": 21,
                    "textStyle": {
                        "color": "rgb(90,102,88)"
                    }
                },
                {
                    "name": "global",
                    "value": 19,
                    "textStyle": {
                        "color": "rgb(129,53,139)"
                    }
                },
                {
                    "name": "has",
                    "value": 18,
                    "textStyle": {
                        "color": "rgb(2,51,120)"
                    }
                },
                {
                    "name": "its",
                    "value": 18,
                    "textStyle": {
                        "color": "rgb(56,38,98)"
                    }
                },
                {
                    "name": "with",
                    "value": 18,
                    "textStyle": {
                        "color": "rgb(62,152,9)"
                    }
                },
                {
                    "name": "are",
                    "value": 16,
                    "textStyle": {
                        "color": "rgb(112,16,21)"
                    }
                },
                {
                    "name": "president",
                    "value": 13,
                    "textStyle": {
                        "color": "rgb(39,23,158)"
                    }
                },
                {
                    "name": "have",
                    "value": 13,
                    "textStyle": {
                        "color": "rgb(74,10,107)"
                    }
                },
                {
                    "name": "from",
                    "value": 13,
                    "textStyle": {
                        "color": "rgb(96,4,135)"
                    }
                },
                {
                    "name": "beijing",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(134,137,156)"
                    }
                },
                {
                    "name": "tiktok",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(145,131,132)"
                    }
                },
                {
                    "name": "new",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(134,97,99)"
                    }
                },
                {
                    "name": "empire",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(3,127,79)"
                    }
                },
                {
                    "name": "world",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(56,112,129)"
                    }
                },
                {
                    "name": "washington",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(32,138,2)"
                    }
                },
                {
                    "name": "an",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(109,4,32)"
                    }
                },
                {
                    "name": "said",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(104,123,50)"
                    }
                },
                {
                    "name": "not",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(36,10,105)"
                    }
                },
                {
                    "name": "per",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(33,69,52)"
                    }
                },
                {
                    "name": "but",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(155,102,49)"
                    }
                },
                {
                    "name": "cent",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(40,27,91)"
                    }
                },
                {
                    "name": "tariffs",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(132,71,52)"
                    }
                },
                {
                    "name": "he",
                    "value": 10,
                    "textStyle": {
                        "color": "rgb(16,102,102)"
                    }
                },
                {
                    "name": "his",
                    "value": 10,
                    "textStyle": {
                        "color": "rgb(146,75,143)"
                    }
                },
                {
                    "name": "over",
                    "value": 10,
                    "textStyle": {
                        "color": "rgb(79,147,14)"
                    }
                },
                {
                    "name": "or",
                    "value": 10,
                    "textStyle": {
                        "color": "rgb(15,148,36)"
                    }
                },
                {
                    "name": "trade",
                    "value": 10,
                    "textStyle": {
                        "color": "rgb(102,32,109)"
                    }
                },
                {
                    "name": "power",
                    "value": 9,
                    "textStyle": {
                        "color": "rgb(133,12,87)"
                    }
                },
                {
                    "name": "at",
                    "value": 9,
                    "textStyle": {
                        "color": "rgb(122,46,150)"
                    }
                },
                {
                    "name": "american",
                    "value": 9,
                    "textStyle": {
                        "color": "rgb(160,113,0)"
                    }
                },
                {
                    "name": "donald",
                    "value": 9,
                    "textStyle": {
                        "color": "rgb(67,102,75)"
                    }
                },
                {
                    "name": "america",
                    "value": 9,
                    "textStyle": {
                        "color": "rgb(0,85,121)"
                    }
                },
                {
                    "name": "was",
                    "value": 8,
                    "textStyle": {
                        "color": "rgb(84,31,66)"
                    }
                },
                {
                    "name": "more",
                    "value": 8,
                    "textStyle": {
                        "color": "rgb(19,115,1)"
                    }
                },
                {
                    "name": "there",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(159,40,46)"
                    }
                },
                {
                    "name": "life",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(51,139,52)"
                    }
                },
                {
                    "name": "one",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(136,76,105)"
                    }
                },
                {
                    "name": "first",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(121,88,65)"
                    }
                },
                {
                    "name": "two",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(98,141,132)"
                    }
                },
                {
                    "name": "war",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(68,50,43)"
                    }
                },
                {
                    "name": "we",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(49,155,158)"
                    }
                },
                {
                    "name": "last",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(81,65,46)"
                    }
                },
                {
                    "name": "big",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(143,38,101)"
                    }
                },
                {
                    "name": "be",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(108,3,157)"
                    }
                },
                {
                    "name": "between",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(36,9,44)"
                    }
                },
                {
                    "name": "house",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(95,87,72)"
                    }
                },
                {
                    "name": "than",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(126,70,100)"
                    }
                },
                {
                    "name": "deal",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(94,109,135)"
                    }
                },
                {
                    "name": "economic",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(131,91,39)"
                    }
                },
                {
                    "name": "month",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(74,113,136)"
                    }
                },
                {
                    "name": "they",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(98,104,30)"
                    }
                },
                {
                    "name": "i",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(120,70,9)"
                    }
                },
                {
                    "name": "most",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(72,44,160)"
                    }
                },
                {
                    "name": "which",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(80,42,136)"
                    }
                },
                {
                    "name": "according",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(153,148,121)"
                    }
                },
                {
                    "name": "their",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(77,7,157)"
                    }
                },
                {
                    "name": "united",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(61,113,38)"
                    }
                },
                {
                    "name": "would",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(32,18,47)"
                    }
                },
                {
                    "name": "wang",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(88,158,158)"
                    }
                },
                {
                    "name": "white",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(130,158,33)"
                    }
                },
                {
                    "name": "administration",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(157,6,20)"
                    }
                },
                {
                    "name": "while",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(146,101,50)"
                    }
                },
                {
                    "name": "since",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(128,156,135)"
                    }
                },
                {
                    "name": "during",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(99,87,72)"
                    }
                },
                {
                    "name": "diplomatic",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(20,89,32)"
                    }
                },
                {
                    "name": "companies",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(29,140,60)"
                    }
                },
                {
                    "name": "been",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(159,75,90)"
                    }
                },
                {
                    "name": "both",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(113,146,137)"
                    }
                },
                {
                    "name": "about",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(124,146,106)"
                    }
                },
                {
                    "name": "military",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(86,50,0)"
                    }
                },
                {
                    "name": "states",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(48,121,112)"
                    }
                },
                {
                    "name": "country",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(156,75,45)"
                    }
                },
                {
                    "name": "foreign",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(72,99,114)"
                    }
                },
                {
                    "name": "if",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(128,9,146)"
                    }
                },
                {
                    "name": "return",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(34,142,127)"
                    }
                },
                {
                    "name": "call",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(152,125,79)"
                    }
                },
                {
                    "name": "minerals",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(66,52,49)"
                    }
                },
                {
                    "name": "expectancy",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(89,125,16)"
                    }
                },
                {
                    "name": "trap",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(54,128,8)"
                    }
                }
            ],
            "drawOutOfBound": false,
            "textStyle": {
                "normal": {
                    "fontFamily": "Microsoft YaHei"
                },
                "emphasis": {}
            }
        }
    ],
    "legend": [
        {
            "data": [],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u8bcd\u9891\u5206\u6790 - \u8bcd\u4e91\u56fe",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_102c0707bfbd43d5bdc116a90ae571e1.setOption(option_102c0707bfbd43d5bdc116a90ae571e1);
    </script>
                <div id="cc2595966c2048808504e3ff90518feb" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('cc2595966c2048808504e3ff90518feb').style.width = document.getElementById('cc2595966c2048808504e3ff90518feb').parentNode.clientWidth + 'px';
        var chart_cc2595966c2048808504e3ff90518feb = echarts.init(
            document.getElementById('cc2595966c2048808504e3ff90518feb'), 'white', {renderer: 'canvas'});
        var option_cc2595966c2048808504e3ff90518feb = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u8bcd\u9891",
            "legendHoverLink": true,
            "data": [
                200,
                96,
                89,
                84,
                83,
                77,
                69,
                61,
                49,
                43,
                37,
                36,
                35,
                34,
                34,
                33,
                26,
                25,
                23,
                23
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u8bcd\u9891"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "the",
                "a",
                "of",
                "to",
                "and",
                "\u2019",
                "s",
                "in",
                "us",
                "china",
                "on",
                "is",
                "as",
                "\u201d",
                "\u201c",
                "trump",
                "for",
                "by",
                "it",
                "chinese"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u8bcd\u9891\u5206\u6790 - \u524d20\u9ad8\u9891\u8bcd",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_cc2595966c2048808504e3ff90518feb.setOption(option_cc2595966c2048808504e3ff90518feb);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="8c31593b427b48f19b67c05a9a87706e" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>左上下文</th>
            <th>关键词</th>
            <th>右上下文</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>tight and many countries including</td>
            <td>china</td>
            <td>and the united states have</td>
        </tr>
        <tr>
            <td>they can agree on is</td>
            <td>china</td>
            <td>and the threat they see</td>
        </tr>
        <tr>
            <td>people ’ s republic of</td>
            <td>china</td>
            <td>and the subsequent material contributions</td>
        </tr>
        <tr>
            <td>zte and other chinese companies</td>
            <td>china</td>
            <td>’ s economy – unlike</td>
        </tr>
        <tr>
            <td>nan</td>
            <td>china</td>
            <td>’ s top diplomat has</td>
        </tr>
        <tr>
            <td>as an irresponsible big power</td>
            <td>china</td>
            <td>’ s top diplomat wang</td>
        </tr>
        <tr>
            <td>to rewrite the pattern that</td>
            <td>china</td>
            <td>’ s economy achieved 5</td>
        </tr>
        <tr>
            <td>surpassed the us dollar in</td>
            <td>china</td>
            <td>’ s cross-border transactions for</td>
        </tr>
        <tr>
            <td>topped the us dollar in</td>
            <td>china</td>
            <td>’ s cross-border transactions with</td>
        </tr>
        <tr>
            <td>media platform a unit of</td>
            <td>china</td>
            <td>’ s bytedance without a</td>
        </tr>
        <tr>
            <td>buying us-origin products to support</td>
            <td>china</td>
            <td>’ s development of quantum</td>
        </tr>
        <tr>
            <td>i explain at length in</td>
            <td>china</td>
            <td>’ s galaxy empire 2024</td>
        </tr>
        <tr>
            <td>expectancy is even higher among</td>
            <td>china</td>
            <td>’ s 400-million strong middle</td>
        </tr>
        <tr>
            <td>orientalist ignorance and denial of</td>
            <td>china</td>
            <td>’ s imperial ambitions are</td>
        </tr>
        <tr>
            <td>ongoing “ two sessions ”</td>
            <td>china</td>
            <td>’ s annual parliamentary gathering</td>
        </tr>
        <tr>
            <td>are actively drawing closer to</td>
            <td>china</td>
            <td>in mid-2023 the renminbi surpassed</td>
        </tr>
        <tr>
            <td>shifts are also happening in</td>
            <td>china</td>
            <td>in matters of everyday life</td>
        </tr>
        <tr>
            <td>2.0 tech export controls against</td>
            <td>china</td>
            <td>us president donald trump could</td>
        </tr>
        <tr>
            <td>south korea and britain combined</td>
            <td>china</td>
            <td>is the european union ’</td>
        </tr>
        <tr>
            <td>trade surplus since 1975 –</td>
            <td>china</td>
            <td>is the largest trading country</td>
        </tr>
        <tr>
            <td>to the us technically speaking</td>
            <td>china</td>
            <td>is no longer merely a</td>
        </tr>
        <tr>
            <td>galaxy empire 2024 is that</td>
            <td>china</td>
            <td>is rapidly becoming an empire</td>
        </tr>
        <tr>
            <td>the asian infrastructure investment bank</td>
            <td>china</td>
            <td>is spearheading the global rebellion</td>
        </tr>
        <tr>
            <td>president donald trump could visit</td>
            <td>china</td>
            <td>as early as next month</td>
        </tr>
        <tr>
            <td>to “ decouple ” from</td>
            <td>china</td>
            <td>by applying tariff penalties boycotting</td>
        </tr>
        <tr>
            <td>now confronted by a resurgent</td>
            <td>china</td>
            <td>– its most significant economic</td>
        </tr>
        <tr>
            <td>in the world are chinese</td>
            <td>china</td>
            <td>has outflanked bodies such as</td>
        </tr>
        <tr>
            <td>strategic minerals have soared in</td>
            <td>china</td>
            <td>over the past year as</td>
        </tr>
        <tr>
            <td>have been around trump visiting</td>
            <td>china</td>
            <td>according to sources it is</td>
        </tr>
        <tr>
            <td>than 21.8 per cent in</td>
            <td>china</td>
            <td>while prices in rotterdam have</td>
        </tr>
        <tr>
            <td>imports and a demand that</td>
            <td>china</td>
            <td>sell the us arm of</td>
        </tr>
        <tr>
            <td>reforms co-produced the return of</td>
            <td>china</td>
            <td>after two centuries of subjugation</td>
        </tr>
        <tr>
            <td>during the past two decades</td>
            <td>china</td>
            <td>now produces more stem graduates</td>
        </tr>
        <tr>
            <td>retreat as xi ’ s</td>
            <td>china</td>
            <td>emerges as a major global</td>
        </tr>
        <tr>
            <td>lithium-ion battery manufacturer durapower holdings</td>
            <td>china</td>
            <td>meanwhile produces one-third of the</td>
        </tr>
        <tr>
            <td>better or worse ” “</td>
            <td>china</td>
            <td>will definitely take countermeasures in</td>
        </tr>
        <tr>
            <td>tariffs ” in return for</td>
            <td>china</td>
            <td>agreeing to a tiktok sale</td>
        </tr>
        <tr>
            <td>per cent of americans view</td>
            <td>china</td>
            <td>unfavourably trump praised chinese president</td>
        </tr>
        <tr>
            <td>war – can america and</td>
            <td>china</td>
            <td>escape thucydides ’ s trap</td>
        </tr>
        <tr>
            <td>going through drastic changes ”</td>
            <td>china</td>
            <td>hits back at trump with</td>
        </tr>
        <tr>
            <td>with prices rising rapidly since</td>
            <td>china</td>
            <td>began restricting exports of the</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="cc25a4aab25341878fcd2aea2537820c" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('cc25a4aab25341878fcd2aea2537820c').style.width = document.getElementById('cc25a4aab25341878fcd2aea2537820c').parentNode.clientWidth + 'px';
        var chart_cc25a4aab25341878fcd2aea2537820c = echarts.init(
            document.getElementById('cc25a4aab25341878fcd2aea2537820c'), 'white', {renderer: 'canvas'});
        var option_cc25a4aab25341878fcd2aea2537820c = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u8bcd\u9891",
            "legendHoverLink": true,
            "data": [
                0,
                3,
                1,
                2,
                23,
                3,
                3,
                4,
                4,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u8bcd\u9891"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "1.txt",
                "10.txt",
                "2.txt",
                "3.txt",
                "4.txt",
                "5.txt",
                "6.txt",
                "7.txt",
                "8.txt",
                "9.txt"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u8bcd\u8bed\u5206\u5e03\u5206\u6790 - \u6587\u4ef6\u9891\u7387\u5206\u5e03",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_cc25a4aab25341878fcd2aea2537820c.setOption(option_cc25a4aab25341878fcd2aea2537820c);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="2dfd7b36407648d598531e73ec0762bf" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>序号</th>
            <th>文件ID</th>
            <th>文件名</th>
            <th>总词数</th>
            <th>频率</th>
            <th>离散度</th>
            <th>分布图</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>1</td>
            <td>0</td>
            <td>1.txt</td>
            <td>181</td>
            <td>0</td>
            <td>0.0000</td>
            <td>nan</td>
        </tr>
        <tr>
            <td>2</td>
            <td>1</td>
            <td>10.txt</td>
            <td>287</td>
            <td>3</td>
            <td>2.1344</td>
            <td>                │ │          │                    </td>
        </tr>
        <tr>
            <td>3</td>
            <td>2</td>
            <td>2.txt</td>
            <td>243</td>
            <td>1</td>
            <td>3.0000</td>
            <td>                                           │      </td>
        </tr>
        <tr>
            <td>4</td>
            <td>3</td>
            <td>3.txt</td>
            <td>184</td>
            <td>2</td>
            <td>2.0000</td>
            <td>          │                     │                 </td>
        </tr>
        <tr>
            <td>5</td>
            <td>4</td>
            <td>4.txt</td>
            <td>1337</td>
            <td>23</td>
            <td>0.7790</td>
            <td>  │            ││   │││ │   │ ││││││ ││  │││ │   │</td>
        </tr>
        <tr>
            <td>6</td>
            <td>5</td>
            <td>5.txt</td>
            <td>335</td>
            <td>3</td>
            <td>1.5275</td>
            <td>                            │ │              │    </td>
        </tr>
        <tr>
            <td>7</td>
            <td>6</td>
            <td>6.txt</td>
            <td>156</td>
            <td>3</td>
            <td>1.5275</td>
            <td> │                  │               │             </td>
        </tr>
        <tr>
            <td>8</td>
            <td>7</td>
            <td>7.txt</td>
            <td>209</td>
            <td>4</td>
            <td>1.2247</td>
            <td>  │                         │      │          │   </td>
        </tr>
        <tr>
            <td>9</td>
            <td>8</td>
            <td>8.txt</td>
            <td>285</td>
            <td>4</td>
            <td>1.2247</td>
            <td>│                 │         │      │              </td>
        </tr>
        <tr>
            <td>10</td>
            <td>9</td>
            <td>9.txt</td>
            <td>155</td>
            <td>0</td>
            <td>0.0000</td>
            <td>nan</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="3372a60396c24c2fa5e534e7012eabbd" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('3372a60396c24c2fa5e534e7012eabbd').style.width = document.getElementById('3372a60396c24c2fa5e534e7012eabbd').parentNode.clientWidth + 'px';
        var chart_3372a60396c24c2fa5e534e7012eabbd = echarts.init(
            document.getElementById('3372a60396c24c2fa5e534e7012eabbd'), 'white', {renderer: 'canvas'});
        var option_3372a60396c24c2fa5e534e7012eabbd = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u9891\u7387",
            "legendHoverLink": true,
            "data": [
                3,
                2,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1,
                1
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u9891\u7387"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "us dollar in china",
                "xi \u2019 s china",
                "2024 is that china",
                "a demand that china",
                "a unit of china",
                "agree on is china",
                "also happening in china",
                "and britain combined china",
                "and denial of china",
                "around trump visiting china",
                "at length in china",
                "by a resurgent china",
                "can america and china",
                "china",
                "decouple \u201d from china",
                "drastic changes \u201d china",
                "drawing closer to china",
                "even higher among china",
                "export controls against china",
                "have soared in china",
                "in return for china",
                "infrastructure investment bank china",
                "irresponsible big power china",
                "manufacturer durapower holdings china",
                "many countries including china",
                "of americans view china",
                "other chinese companies china",
                "past two decades china",
                "per cent in china",
                "products to support china",
                "rising rapidly since china",
                "s republic of china",
                "since 1975 \u2013 china",
                "the pattern that china",
                "the return of china",
                "trump could visit china",
                "two sessions \u201d china",
                "us technically speaking china",
                "world are chinese china",
                "worse \u201d \u201c china"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u8bcd\u7c07\u5206\u6790 - \u9891\u7387\u5206\u5e03",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_3372a60396c24c2fa5e534e7012eabbd.setOption(option_3372a60396c24c2fa5e534e7012eabbd);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="534304f419394afbbd468669e8f0bc9e" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>词簇</th>
            <th>排名</th>
            <th>频率</th>
            <th>范围</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>us dollar in china</td>
            <td>1</td>
            <td>3</td>
            <td>1</td>
        </tr>
        <tr>
            <td>xi ’ s china</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
        </tr>
        <tr>
            <td>2024 is that china</td>
            <td>3</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>a demand that china</td>
            <td>4</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>a unit of china</td>
            <td>5</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>agree on is china</td>
            <td>6</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>also happening in china</td>
            <td>7</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>and britain combined china</td>
            <td>8</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>and denial of china</td>
            <td>9</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>around trump visiting china</td>
            <td>10</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>at length in china</td>
            <td>11</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>by a resurgent china</td>
            <td>12</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>can america and china</td>
            <td>13</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>china</td>
            <td>14</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>decouple ” from china</td>
            <td>15</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>drastic changes ” china</td>
            <td>16</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>drawing closer to china</td>
            <td>17</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>even higher among china</td>
            <td>18</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>export controls against china</td>
            <td>19</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>have soared in china</td>
            <td>20</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>in return for china</td>
            <td>21</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>infrastructure investment bank china</td>
            <td>22</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>irresponsible big power china</td>
            <td>23</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>manufacturer durapower holdings china</td>
            <td>24</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>many countries including china</td>
            <td>25</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>of americans view china</td>
            <td>26</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>other chinese companies china</td>
            <td>27</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>past two decades china</td>
            <td>28</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>per cent in china</td>
            <td>29</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>products to support china</td>
            <td>30</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>rising rapidly since china</td>
            <td>31</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>s republic of china</td>
            <td>32</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>since 1975 – china</td>
            <td>33</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>the pattern that china</td>
            <td>34</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>the return of china</td>
            <td>35</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>trump could visit china</td>
            <td>36</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>two sessions ” china</td>
            <td>37</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>us technically speaking china</td>
            <td>38</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>world are chinese china</td>
            <td>39</td>
            <td>1</td>
            <td>1</td>
        </tr>
        <tr>
            <td>worse ” “ china</td>
            <td>40</td>
            <td>1</td>
            <td>1</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="6bc226dbb22c4dffa1ab3f71ccdf5690" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('6bc226dbb22c4dffa1ab3f71ccdf5690').style.width = document.getElementById('6bc226dbb22c4dffa1ab3f71ccdf5690').parentNode.clientWidth + 'px';
        var chart_6bc226dbb22c4dffa1ab3f71ccdf5690 = echarts.init(
            document.getElementById('6bc226dbb22c4dffa1ab3f71ccdf5690'), 'white', {renderer: 'canvas'});
        var option_6bc226dbb22c4dffa1ab3f71ccdf5690 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u9891\u7387",
            "legendHoverLink": true,
            "data": [
                3,
                3,
                8,
                8,
                3,
                5,
                0,
                0,
                6,
                3,
                2,
                4,
                1,
                0,
                0,
                2,
                2,
                5,
                2,
                2,
                0,
                0,
                0,
                1,
                1,
                0,
                2,
                2,
                2,
                3,
                3,
                2,
                1,
                0,
                1,
                1,
                0,
                0,
                1,
                1,
                0,
                0,
                1,
                1,
                0,
                0,
                0,
                1,
                1,
                1,
                0,
                1,
                0,
                0,
                1,
                0,
                1,
                1,
                0,
                0,
                1,
                0,
                1,
                1,
                0,
                0,
                1,
                1,
                1,
                0,
                0,
                1,
                1,
                0,
                0,
                1,
                1,
                0,
                0,
                0,
                0,
                1,
                1,
                0,
                1,
                1,
                0,
                0,
                1,
                1,
                1,
                1,
                0,
                1,
                1,
                1,
                1,
                1,
                0,
                0,
                1,
                0,
                0,
                1,
                1,
                2,
                1,
                1,
                3,
                1,
                1,
                1,
                0,
                1,
                1,
                1,
                1,
                0,
                0,
                0,
                1,
                0,
                0,
                0,
                1,
                0,
                1,
                0,
                1,
                1,
                0,
                1,
                2,
                2,
                0,
                0,
                1,
                0,
                1,
                1,
                0,
                1,
                1,
                0,
                0,
                0,
                1,
                1,
                0,
                0,
                1,
                1,
                0,
                0,
                0,
                1,
                0,
                0,
                1,
                1,
                0,
                1,
                1,
                0,
                1,
                0,
                0,
                1,
                1,
                1,
                0,
                0,
                0,
                1,
                0,
                0,
                1,
                0,
                0,
                1,
                1,
                0,
                1,
                1,
                0,
                1,
                0,
                1,
                1,
                0,
                0,
                0,
                0,
                1,
                1
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u9891\u7387"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "s",
                "\u2019",
                "the",
                "in",
                "is",
                "of",
                "cross-border",
                "transactions",
                "and",
                "dollar",
                "as",
                "us",
                "rapidly",
                "emerges",
                "produces",
                "two",
                "trump",
                "\u201d",
                "\u2013",
                "can",
                "diplomat",
                "major",
                "economy",
                "past",
                "prices",
                "top",
                "xi",
                "return",
                "since",
                "a",
                "that",
                "chinese",
                "demand",
                "arm",
                "platform",
                "unit",
                "without",
                "agreeing",
                "agree",
                "us-origin",
                "development",
                "escape",
                "rewrite",
                "pattern",
                "unfavourably",
                "praised",
                "early",
                "visiting",
                "drastic",
                "changes",
                "hits",
                "soared",
                "began",
                "restricting",
                "21.8",
                "rotterdam",
                "irresponsible",
                "sessions",
                "annual",
                "parliamentary",
                "worse",
                "definitely",
                "confronted",
                "resurgent",
                "subsequent",
                "material",
                "co-produced",
                "technically",
                "speaking",
                "longer",
                "merely",
                "explain",
                "length",
                "galaxy",
                "becoming",
                "ignorance",
                "denial",
                "imperial",
                "ambitions",
                "outflanked",
                "bodies",
                "asian",
                "infrastructure",
                "spearheading",
                "1975",
                "decouple",
                "applying",
                "penalties",
                "battery",
                "manufacturer",
                "durapower",
                "holdings",
                "one-third",
                "korea",
                "actively",
                "drawing",
                "closer",
                "happening",
                "matters",
                "everyday",
                "among",
                "400-million",
                "stem",
                "donald",
                "at",
                "cent",
                "an",
                "empire",
                "\u201c",
                "export",
                "controls",
                "support",
                "achieved",
                "americans",
                "view",
                "visit",
                "around",
                "sources",
                "back",
                "exports",
                "many",
                "will",
                "countermeasures",
                "significant",
                "republic",
                "centuries",
                "surplus",
                "meanwhile",
                "combined",
                "higher",
                "strong",
                "decades",
                "are",
                "to",
                "sell",
                "bytedance",
                "against",
                "thucydides",
                "could",
                "through",
                "year",
                "rising",
                "countries",
                "take",
                "after",
                "no",
                "2024",
                "investment",
                "largest",
                "trading",
                "other",
                "britain",
                "european",
                "mid-2023",
                "renminbi",
                "even",
                "now",
                "threat",
                "tech",
                "products",
                "next",
                "minerals",
                "including",
                "such",
                "bank",
                "tariff",
                "union",
                "also",
                "by",
                "been",
                "while",
                "united",
                "states",
                "companies",
                "they",
                "according",
                "big",
                "most",
                "more",
                "america",
                "power",
                "over",
                "or",
                "per",
                "tiktok",
                "world",
                "president",
                "have",
                "from",
                "its",
                "has",
                "global",
                "it",
                "for",
                "on"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u642d\u914d\u5206\u6790 - \u9891\u7387\u5206\u5e03",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_6bc226dbb22c4dffa1ab3f71ccdf5690.setOption(option_6bc226dbb22c4dffa1ab3f71ccdf5690);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="a8265cded0c146bfb223310130e2d991" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>搭配词</th>
            <th>排名</th>
            <th>左频率</th>
            <th>右频率</th>
            <th>范围</th>
            <th>似然性</th>
            <th>效应</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>s</td>
            <td>1</td>
            <td>3</td>
            <td>14</td>
            <td>5</td>
            <td>79.83020864644158</td>
            <td>3.443596726828264</td>
        </tr>
        <tr>
            <td>’</td>
            <td>2</td>
            <td>3</td>
            <td>14</td>
            <td>5</td>
            <td>75.70947045975139</td>
            <td>3.331472831117658</td>
        </tr>
        <tr>
            <td>the</td>
            <td>3</td>
            <td>8</td>
            <td>9</td>
            <td>5</td>
            <td>42.66542255846443</td>
            <td>2.338917022241436</td>
        </tr>
        <tr>
            <td>in</td>
            <td>4</td>
            <td>8</td>
            <td>3</td>
            <td>3</td>
            <td>42.41298023664916</td>
            <td>2.926291043372579</td>
        </tr>
        <tr>
            <td>is</td>
            <td>5</td>
            <td>3</td>
            <td>5</td>
            <td>2</td>
            <td>33.813650170312705</td>
            <td>3.0531023052129496</td>
        </tr>
        <tr>
            <td>of</td>
            <td>6</td>
            <td>5</td>
            <td>5</td>
            <td>5</td>
            <td>28.766239516163377</td>
            <td>2.413954079297018</td>
        </tr>
        <tr>
            <td>cross-border</td>
            <td>7</td>
            <td>0</td>
            <td>3</td>
            <td>1</td>
            <td>26.38404446317018</td>
            <td>4.433491789292616</td>
        </tr>
        <tr>
            <td>transactions</td>
            <td>8</td>
            <td>0</td>
            <td>3</td>
            <td>1</td>
            <td>26.38404446317018</td>
            <td>4.433491789292616</td>
        </tr>
        <tr>
            <td>and</td>
            <td>9</td>
            <td>6</td>
            <td>3</td>
            <td>5</td>
            <td>25.068879890988764</td>
            <td>2.350362291112517</td>
        </tr>
        <tr>
            <td>dollar</td>
            <td>10</td>
            <td>3</td>
            <td>0</td>
            <td>1</td>
            <td>21.909254885116997</td>
            <td>4.1455128487965</td>
        </tr>
        <tr>
            <td>as</td>
            <td>11</td>
            <td>2</td>
            <td>4</td>
            <td>2</td>
            <td>21.786951447761066</td>
            <td>2.738320973845791</td>
        </tr>
        <tr>
            <td>us</td>
            <td>12</td>
            <td>4</td>
            <td>2</td>
            <td>3</td>
            <td>17.735831230360603</td>
            <td>2.3976445268277984</td>
        </tr>
        <tr>
            <td>rapidly</td>
            <td>13</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>17.541558322376755</td>
            <td>4.4090959566421</td>
        </tr>
        <tr>
            <td>emerges</td>
            <td>14</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>17.541558322376755</td>
            <td>4.4090959566421</td>
        </tr>
        <tr>
            <td>produces</td>
            <td>15</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>17.541558322376755</td>
            <td>4.4090959566421</td>
        </tr>
        <tr>
            <td>two</td>
            <td>16</td>
            <td>2</td>
            <td>1</td>
            <td>2</td>
            <td>16.918940153885682</td>
            <td>3.585005927577688</td>
        </tr>
        <tr>
            <td>trump</td>
            <td>17</td>
            <td>2</td>
            <td>3</td>
            <td>3</td>
            <td>16.785149360518425</td>
            <td>2.5887708311848887</td>
        </tr>
        <tr>
            <td>”</td>
            <td>18</td>
            <td>5</td>
            <td>0</td>
            <td>4</td>
            <td>16.48473357732334</td>
            <td>2.558618332313334</td>
        </tr>
        <tr>
            <td>–</td>
            <td>19</td>
            <td>2</td>
            <td>2</td>
            <td>2</td>
            <td>14.834240406274931</td>
            <td>2.748405886451041</td>
        </tr>
        <tr>
            <td>can</td>
            <td>20</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>13.746958539480088</td>
            <td>4.00333406859408</td>
        </tr>
        <tr>
            <td>diplomat</td>
            <td>21</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>13.746958539480088</td>
            <td>4.00333406859408</td>
        </tr>
        <tr>
            <td>major</td>
            <td>22</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>13.746958539480088</td>
            <td>4.00333406859408</td>
        </tr>
        <tr>
            <td>economy</td>
            <td>23</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>12.045358645215352</td>
            <td>3.715355128097964</td>
        </tr>
        <tr>
            <td>past</td>
            <td>24</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>12.045358645215352</td>
            <td>3.715355128097964</td>
        </tr>
        <tr>
            <td>prices</td>
            <td>25</td>
            <td>1</td>
            <td>1</td>
            <td>1</td>
            <td>12.045358645215352</td>
            <td>3.715355128097964</td>
        </tr>
        <tr>
            <td>top</td>
            <td>26</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>12.045358645215352</td>
            <td>3.715355128097964</td>
        </tr>
        <tr>
            <td>xi</td>
            <td>27</td>
            <td>2</td>
            <td>0</td>
            <td>1</td>
            <td>12.045358645215352</td>
            <td>3.715355128097964</td>
        </tr>
        <tr>
            <td>return</td>
            <td>28</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>10.884919277989647</td>
            <td>3.4919146205826115</td>
        </tr>
        <tr>
            <td>since</td>
            <td>29</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>10.884919277989647</td>
            <td>3.4919146205826115</td>
        </tr>
        <tr>
            <td>a</td>
            <td>30</td>
            <td>3</td>
            <td>3</td>
            <td>2</td>
            <td>10.341970425962256</td>
            <td>1.7108718100889544</td>
        </tr>
        <tr>
            <td>that</td>
            <td>31</td>
            <td>3</td>
            <td>0</td>
            <td>3</td>
            <td>9.590303016304862</td>
            <td>2.4822244845680608</td>
        </tr>
        <tr>
            <td>chinese</td>
            <td>32</td>
            <td>2</td>
            <td>1</td>
            <td>2</td>
            <td>9.051467753082663</td>
            <td>2.3906556914192283</td>
        </tr>
        <tr>
            <td>demand</td>
            <td>33</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>arm</td>
            <td>34</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>platform</td>
            <td>35</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>unit</td>
            <td>36</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>without</td>
            <td>37</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>agreeing</td>
            <td>38</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>agree</td>
            <td>39</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>us-origin</td>
            <td>40</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>development</td>
            <td>41</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>escape</td>
            <td>42</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>rewrite</td>
            <td>43</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>pattern</td>
            <td>44</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>unfavourably</td>
            <td>45</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>praised</td>
            <td>46</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>early</td>
            <td>47</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>visiting</td>
            <td>48</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>drastic</td>
            <td>49</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>changes</td>
            <td>50</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>hits</td>
            <td>51</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>soared</td>
            <td>52</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>began</td>
            <td>53</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>restricting</td>
            <td>54</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>21.8</td>
            <td>55</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>rotterdam</td>
            <td>56</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>irresponsible</td>
            <td>57</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>sessions</td>
            <td>58</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>annual</td>
            <td>59</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>parliamentary</td>
            <td>60</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>worse</td>
            <td>61</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>definitely</td>
            <td>62</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>confronted</td>
            <td>63</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>resurgent</td>
            <td>64</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>subsequent</td>
            <td>65</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>material</td>
            <td>66</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>co-produced</td>
            <td>67</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>technically</td>
            <td>68</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>speaking</td>
            <td>69</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>longer</td>
            <td>70</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>merely</td>
            <td>71</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>explain</td>
            <td>72</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>length</td>
            <td>73</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>galaxy</td>
            <td>74</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>becoming</td>
            <td>75</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>ignorance</td>
            <td>76</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>denial</td>
            <td>77</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>imperial</td>
            <td>78</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>ambitions</td>
            <td>79</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>outflanked</td>
            <td>80</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>bodies</td>
            <td>81</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>asian</td>
            <td>82</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>infrastructure</td>
            <td>83</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>spearheading</td>
            <td>84</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>1975</td>
            <td>85</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>decouple</td>
            <td>86</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>applying</td>
            <td>87</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>penalties</td>
            <td>88</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>battery</td>
            <td>89</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>manufacturer</td>
            <td>90</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>durapower</td>
            <td>91</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>holdings</td>
            <td>92</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>one-third</td>
            <td>93</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>korea</td>
            <td>94</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>actively</td>
            <td>95</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>drawing</td>
            <td>96</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>closer</td>
            <td>97</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>happening</td>
            <td>98</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>matters</td>
            <td>99</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>everyday</td>
            <td>100</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>among</td>
            <td>101</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>400-million</td>
            <td>102</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>stem</td>
            <td>103</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>8.747264035174656</td>
            <td>4.385295096950692</td>
        </tr>
        <tr>
            <td>donald</td>
            <td>104</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>8.17839706556234</td>
            <td>2.902939248259688</td>
        </tr>
        <tr>
            <td>at</td>
            <td>105</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>8.17839706556234</td>
            <td>2.902939248259688</td>
        </tr>
        <tr>
            <td>cent</td>
            <td>106</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>7.331124133161412</td>
            <td>2.7016736687823726</td>
        </tr>
        <tr>
            <td>an</td>
            <td>107</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>6.973271214327214</td>
            <td>2.614364717024887</td>
        </tr>
        <tr>
            <td>empire</td>
            <td>108</td>
            <td>1</td>
            <td>1</td>
            <td>1</td>
            <td>6.973271214327214</td>
            <td>2.614364717024887</td>
        </tr>
        <tr>
            <td>“</td>
            <td>109</td>
            <td>3</td>
            <td>0</td>
            <td>2</td>
            <td>6.83438629010538</td>
            <td>1.9964994141597925</td>
        </tr>
        <tr>
            <td>export</td>
            <td>110</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>controls</td>
            <td>111</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>support</td>
            <td>112</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>achieved</td>
            <td>113</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>americans</td>
            <td>114</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>view</td>
            <td>115</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>visit</td>
            <td>116</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>around</td>
            <td>117</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>sources</td>
            <td>118</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>back</td>
            <td>119</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>exports</td>
            <td>120</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>many</td>
            <td>121</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>will</td>
            <td>122</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>countermeasures</td>
            <td>123</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>significant</td>
            <td>124</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>republic</td>
            <td>125</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>centuries</td>
            <td>126</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>surplus</td>
            <td>127</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>meanwhile</td>
            <td>128</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>combined</td>
            <td>129</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>higher</td>
            <td>130</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>strong</td>
            <td>131</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>decades</td>
            <td>132</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>5.999754011626783</td>
            <td>3.6918512245030937</td>
        </tr>
        <tr>
            <td>are</td>
            <td>133</td>
            <td>2</td>
            <td>0</td>
            <td>1</td>
            <td>5.828373468199847</td>
            <td>2.325491459202953</td>
        </tr>
        <tr>
            <td>to</td>
            <td>134</td>
            <td>2</td>
            <td>2</td>
            <td>4</td>
            <td>4.999501226083823</td>
            <td>1.389950672323001</td>
        </tr>
        <tr>
            <td>sell</td>
            <td>135</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>bytedance</td>
            <td>136</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>against</td>
            <td>137</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>thucydides</td>
            <td>138</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>could</td>
            <td>139</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>through</td>
            <td>140</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>year</td>
            <td>141</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>rising</td>
            <td>142</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>countries</td>
            <td>143</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>take</td>
            <td>144</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>after</td>
            <td>145</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>no</td>
            <td>146</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>2024</td>
            <td>147</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>investment</td>
            <td>148</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>largest</td>
            <td>149</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>trading</td>
            <td>150</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>other</td>
            <td>151</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>britain</td>
            <td>152</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>european</td>
            <td>153</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>mid-2023</td>
            <td>154</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>renminbi</td>
            <td>155</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>even</td>
            <td>156</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>now</td>
            <td>157</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.978343912517731</td>
            <td>3.2860893364550745</td>
        </tr>
        <tr>
            <td>threat</td>
            <td>158</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.***************</td>
            <td>2.****************</td>
        </tr>
        <tr>
            <td>tech</td>
            <td>159</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>4.***************</td>
            <td>2.****************</td>
        </tr>
        <tr>
            <td>products</td>
            <td>160</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>4.***************</td>
            <td>2.****************</td>
        </tr>
        <tr>
            <td>next</td>
            <td>161</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.***************</td>
            <td>2.****************</td>
        </tr>
        <tr>
            <td>minerals</td>
            <td>162</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>4.***************</td>
            <td>2.****************</td>
        </tr>
        <tr>
            <td>including</td>
            <td>163</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>4.***************</td>
            <td>2.****************</td>
        </tr>
        <tr>
            <td>such</td>
            <td>164</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.***************</td>
            <td>2.****************</td>
        </tr>
        <tr>
            <td>bank</td>
            <td>165</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>4.***************</td>
            <td>2.****************</td>
        </tr>
        <tr>
            <td>tariff</td>
            <td>166</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.***************</td>
            <td>2.****************</td>
        </tr>
        <tr>
            <td>union</td>
            <td>167</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>4.***************</td>
            <td>2.****************</td>
        </tr>
        <tr>
            <td>also</td>
            <td>168</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>4.***************</td>
            <td>2.****************</td>
        </tr>
        <tr>
            <td>by</td>
            <td>169</td>
            <td>1</td>
            <td>1</td>
            <td>1</td>
            <td>4.168108060566988</td>
            <td>1.8765189901987636</td>
        </tr>
        <tr>
            <td>been</td>
            <td>170</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>3.843599550836252</td>
            <td>2.774669888443606</td>
        </tr>
        <tr>
            <td>while</td>
            <td>171</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>3.843599550836252</td>
            <td>2.774669888443606</td>
        </tr>
        <tr>
            <td>united</td>
            <td>172</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>3.843599550836252</td>
            <td>2.774669888443606</td>
        </tr>
        <tr>
            <td>states</td>
            <td>173</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>3.843599550836252</td>
            <td>2.774669888443606</td>
        </tr>
        <tr>
            <td>companies</td>
            <td>174</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>3.843599550836252</td>
            <td>2.774669888443606</td>
        </tr>
        <tr>
            <td>they</td>
            <td>175</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>3.465997964294485</td>
            <td>2.592051287239328</td>
        </tr>
        <tr>
            <td>according</td>
            <td>176</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>3.465997964294485</td>
            <td>2.592051287239328</td>
        </tr>
        <tr>
            <td>big</td>
            <td>177</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>3.465997964294485</td>
            <td>2.592051287239328</td>
        </tr>
        <tr>
            <td>most</td>
            <td>178</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>3.465997964294485</td>
            <td>2.592051287239328</td>
        </tr>
        <tr>
            <td>more</td>
            <td>179</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>2.894649786982832</td>
            <td>2.3037748611296345</td>
        </tr>
        <tr>
            <td>america</td>
            <td>180</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>2.6691258628178773</td>
            <td>2.185694516120682</td>
        </tr>
        <tr>
            <td>power</td>
            <td>181</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>2.6691258628178773</td>
            <td>2.185694516120682</td>
        </tr>
        <tr>
            <td>over</td>
            <td>182</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>2.471582862210216</td>
            <td>2.080036602691147</td>
        </tr>
        <tr>
            <td>or</td>
            <td>183</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>2.471582862210216</td>
            <td>2.080036602691147</td>
        </tr>
        <tr>
            <td>per</td>
            <td>184</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>2.296394456043875</td>
            <td>1.9844289366433667</td>
        </tr>
        <tr>
            <td>tiktok</td>
            <td>185</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>2.139478802216069</td>
            <td>1.8971199848858813</td>
        </tr>
        <tr>
            <td>world</td>
            <td>186</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>2.139478802216069</td>
            <td>1.8971199848858813</td>
        </tr>
        <tr>
            <td>president</td>
            <td>187</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>1.9977800289715155</td>
            <td>1.816779613867388</td>
        </tr>
        <tr>
            <td>have</td>
            <td>188</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>1.9977800289715155</td>
            <td>1.816779613867388</td>
        </tr>
        <tr>
            <td>from</td>
            <td>189</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>1.9977800289715155</td>
            <td>1.816779613867388</td>
        </tr>
        <tr>
            <td>its</td>
            <td>190</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>1.4504939822625813</td>
            <td>1.489867566203621</td>
        </tr>
        <tr>
            <td>has</td>
            <td>191</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>1.4504939822625813</td>
            <td>1.489867566203621</td>
        </tr>
        <tr>
            <td>global</td>
            <td>192</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>1.3645322799695478</td>
            <td>1.4355021490164197</td>
        </tr>
        <tr>
            <td>it</td>
            <td>193</td>
            <td>0</td>
            <td>1</td>
            <td>1</td>
            <td>1.073866469716554</td>
            <td>1.2432532385816866</td>
        </tr>
        <tr>
            <td>for</td>
            <td>194</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>0.8992600308692922</td>
            <td>1.119754725241919</td>
        </tr>
        <tr>
            <td>on</td>
            <td>195</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>0.4602859399384227</td>
            <td>0.7636404274221315</td>
        </tr>
    </tbody>
</table>
        </div>

    </div>

    <script>
    </script>
    <script>
        (function() {
            containers = document.getElementsByClassName("chart-container");
            if(containers.length > 0) {
                containers[0].style.display = "block";
            }
        })()

        function showChart(evt, chartID) {
            let containers = document.getElementsByClassName("chart-container");
            for (let i = 0; i < containers.length; i++) {
                containers[i].style.display = "none";
            }

            let tablinks = document.getElementsByClassName("tablinks");
            for (let i = 0; i < tablinks.length; i++) {
                tablinks[i].className = "tablinks";
            }

            document.getElementById(chartID).style.display = "block";
            evt.currentTarget.className += " active";
        }
    </script>
</body>
</html>
