# coding=utf-8
"""
测试词簇分析 - 非交互式版本
"""
import jieba
import os
import sys
import csv
import re
from collections import defaultdict

sys.stdout.reconfigure(encoding='utf-8')

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip() for line in f if line.strip()])
    return stopwords

def clean_text(text):
    """清理文本"""
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def segment_text(text, stopwords):
    """使用jieba进行分词"""
    text = clean_text(text)
    words = jieba.cut(text, cut_all=False)
    
    filtered_words = []
    for word in words:
        word = word.strip()
        if (len(word) >= 2 and 
            word not in stopwords and
            not word.isdigit() and
            not word.isspace()):
            filtered_words.append(word)
    
    return filtered_words

class ClusterProcessor:
    def __init__(self, folder_path, stopwords_file):
        self.folder_path = folder_path
        self.files = sorted([f for f in os.listdir(folder_path) if f.endswith('.txt')])
        self.stopwords = load_stopwords(stopwords_file)
        
    def extract_clusters(self, target_word, position='L', length=2):
        """提取词簇"""
        print(f"提取词簇: 目标词='{target_word}', 位置={position}, 长度={length}")
        
        cluster_stats = defaultdict(lambda: {'count': 0, 'files': set()})
        
        for filename in self.files:
            filepath = os.path.join(self.folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                words = segment_text(text, self.stopwords)
                
                for i, word in enumerate(words):
                    if word == target_word:
                        cluster = self._extract_cluster_at_position(words, i, position, length)
                        if cluster:
                            cluster_stats[cluster]['count'] += 1
                            cluster_stats[cluster]['files'].add(filename)
                            
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
        
        return cluster_stats
    
    def _extract_cluster_at_position(self, words, target_index, position, length):
        """在指定位置提取词簇"""
        if position == 'L':  # 左侧词簇
            start_idx = max(0, target_index - length + 1)
            end_idx = target_index + 1
        elif position == 'R':  # 右侧词簇
            start_idx = target_index
            end_idx = min(len(words), target_index + length)
        elif position == 'C':  # 中心词簇
            half_length = length // 2
            start_idx = max(0, target_index - half_length)
            end_idx = min(len(words), target_index + half_length + 1)
        else:
            return None
        
        if end_idx - start_idx >= length:
            cluster_words = words[start_idx:end_idx]
            return ' '.join(cluster_words)
        
        return None
    
    def generate_report(self, cluster_stats, min_frequency=2):
        """生成词簇分析报告"""
        filtered_clusters = {
            cluster: stats for cluster, stats in cluster_stats.items()
            if stats['count'] >= min_frequency
        }
        
        sorted_clusters = sorted(
            filtered_clusters.items(),
            key=lambda x: x[1]['count'],
            reverse=True
        )
        
        report = []
        for rank, (cluster, stats) in enumerate(sorted_clusters, 1):
            report.append({
                'Cluster': cluster,
                'Rank': rank,
                'Frequency': stats['count'],
                'Range': len(stats['files']),
                'Files': '; '.join(sorted(stats['files']))
            })
        
        return report

def main():
    """主函数"""
    folder_path = "../中文文本"
    stopwords_file = "../stopwords-zh.txt"
    target_word = "人工智能"  # 固定目标词
    position = 'L'
    length = 2
    min_freq = 2
    
    print("=== 中文词簇分析测试 (jieba库) ===")
    
    processor = ClusterProcessor(folder_path, stopwords_file)
    
    # 执行分析
    cluster_stats = processor.extract_clusters(target_word, position, length)
    
    if not cluster_stats:
        print(f"未找到包含词语 '{target_word}' 的词簇")
        return
    
    # 生成报告
    report = processor.generate_report(cluster_stats, min_freq)
    
    if report:
        print(f"\n=== 词簇分析结果 (显示前20个) ===")
        print(f"{'排名':<6} {'词簇':<40} {'频率':<8} {'范围':<8}")
        print("-" * 70)
        
        for item in report[:20]:
            cluster = item['Cluster'][:38] + '..' if len(item['Cluster']) > 40 else item['Cluster']
            print(f"{item['Rank']:<6} {cluster:<40} {item['Frequency']:<8} {item['Range']:<8}")
        
        # 保存结果
        filename = f"jieba_cluster_{target_word}_{position}_{length}.csv"
        with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.DictWriter(f, fieldnames=['Cluster', 'Rank', 'Frequency', 'Range', 'Files'])
            writer.writeheader()
            writer.writerows(report)
        
        print(f"\n结果已保存至: {filename}")
        print(f"总词簇数: {len(report)}")
        print(f"总频次: {sum(item['Frequency'] for item in report)}")
    else:
        print(f"未找到频率 >= {min_freq} 的词簇")

if __name__ == "__main__":
    main()
