# coding=utf-8
import spacy
import os
import sys
from collections import defaultdict
import pandas as pd

sys.stdout.reconfigure(encoding='utf-8')

# 加载模型
nlp = spacy.load("de_core_news_sm")
nlp.max_length = 5000000

# 全局词频统计
word_freq = defaultdict(int)


def preprocess_folder(folder_path):
    """
    预处理文件夹并统计全局词频
    """
    all_docs = {}
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            with open(filepath, 'r', encoding='utf-8') as f:
                text = f.read()
            doc = nlp(text)
            all_docs[filename] = doc

            # 统计全局词频（使用词元）
            for token in doc:
                if not token.is_stop and not token.is_punct and token.is_alpha:
                    lemma = token.lemma_.lower()
                    word_freq[lemma] += 1

    return all_docs


def get_context_words(tokens, center_index, window_size):
    """
    获取指定窗口大小的上下文词列表
    """
    left_start = max(0, center_index - window_size)
    left = [t.text for t in tokens[left_start:center_index]]

    right_end = min(len(tokens), center_index + window_size + 1)
    right = [t.text for t in tokens[center_index + 1:right_end]]

    return left, right


def generate_sort_key(right_context):
    """
    生成排序键：根据右上下文的词频生成优先级元组
    """
    return tuple(word_freq.get(word.lower(), 0) for word in right_context)


def kwic_search(all_docs, keyword, window_size=5):
    """
    执行KWIC搜索
    """
    results = []
    target_lemma = keyword.lower()

    for filename, doc in all_docs.items():
        # 在句子级别遍历
        for sent in doc.sents:
            sent_tokens = [t for t in sent]
            for i, token in enumerate(sent_tokens):
                # 匹配词元（小写）
                if token.lemma_.lower() == target_lemma:
                    left, right = get_context_words(sent_tokens, i, window_size)

                    # 构建结果条目
                    entry = {
                        'file': filename,
                        'left': ' '.join(left),
                        'keyword': token.text,
                        'right': ' '.join(right),
                        'sort_key': generate_sort_key(right)
                    }
                    results.append(entry)

    # 按右上下文的词频排序（降序）
    results.sort(key=lambda x: x['sort_key'], reverse=True)
    return results


def print_results(results, max_lines=50):
    """
    格式化输出结果
    """
    print("\n{:<20} | {:<40} | {:<20} | {}".format(
        'File', 'Left Context', 'Keyword', 'Right Context'))
    print("-" * 120)

    for item in results[:max_lines]:
        print("{:<20} | {:<40} | {:<20} | {}".format(
            item['file'],
            item['left'][-40:],  # 截取最后40字符保持对齐
            item['keyword'],
            item['right'][:40]))  # 截取前40字符


def main():
    folder_path = r"D:\桌面\data"
    all_docs = preprocess_folder(folder_path)

    # 交互式输入
    keyword = input("Which word:").strip()
    window_size = int(input("Windows size:").strip())

    # 执行搜索
    results = kwic_search(all_docs, keyword, window_size)

    # 显示结果
    if results:
        print(f"\n找到 {len(results)} 处匹配，显示前50条：")
        print_results(results)

        # 保存完整结果
        save = input("\n是否保存完整结果到CSV？(y/n): ").lower()
        if save == 'y':
            df = pd.DataFrame(results)
            df.to_csv(f"KWIC_{keyword}.csv", index=False, columns=['file', 'left', 'keyword', 'right'])
            print("已保存结果文件")
    else:
        print("未找到匹配结果")


if __name__ == "__main__":
    main()