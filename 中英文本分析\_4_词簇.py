# coding=utf-8
import os
import sys
import csv
import jieba
from collections import defaultdict
from concurrent.futures import ProcessPoolExecutor
from tqdm import tqdm
from functools import partial
import nltk
from nltk.tokenize import word_tokenize

sys.stdout.reconfigure(encoding='utf-8')
nltk.download('punkt')

def load_stopwords(stopwords_file=None):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip() for line in f])
    stopwords.update(['，', '。', '！', '？', '、', '；', '：', '"', '"', ''', ''', '（', '）', '【', '】', '.', ',', '!', '?', ';', ':', '(', ')', '[', ']'])
    return stopwords

def is_chinese(text):
    """判断文本是否主要为中文"""
    for char in text:
        if '\u4e00' <= char <= '\u9fa5':
            return True
    return False

def default_cluster():
    return {'count': 0, 'files': set()}

class ClusterProcessor:
    def __init__(self, folder_path, stopwords_file=None):
        self.folder_path = folder_path
        self.files = sorted([f for f in os.listdir(folder_path) if f.endswith('.txt')],
                            key=lambda x: x.lower())
        self.file_index = {filename: idx for idx, filename in enumerate(self.files)}
        self.cluster_cache = defaultdict(default_cluster)
        self.stopwords = load_stopwords(stopwords_file)

    @staticmethod
    def process_file(args):
        """处理单个文件（静态方法）"""
        folder_path, target, position_params, filename, stopwords = args
        filepath = os.path.join(folder_path, filename)

        with open(filepath, 'r', encoding='utf-8') as f:
            text = f.read()

        # 根据文本语言类型选择分词方法
        if is_chinese(text):
            tokens = [
                word for word in jieba.cut(text)
                if word not in stopwords and len(word.strip()) > 0
            ]
        else:
            tokens = [word.lower() for word in word_tokenize(text)
                    if word.lower() not in [sw.lower() for sw in stopwords]
                    and len(word.strip()) > 0]

        clusters = []
        for i, token in enumerate(tokens):
            if token == target:
                start = max(0, i - position_params['left'])
                end = min(len(tokens), i + position_params['right'] + 1)
                cluster = ' '.join(tokens[start:end])
                clusters.append(cluster)

        return filename, clusters

    def find_clusters(self, target_word, position_params):
        """主分析函数"""
        # 准备参数
        args = [(self.folder_path, target_word, position_params, f, self.stopwords)
                for f in self.files]

        with ProcessPoolExecutor() as executor:
            results = list(tqdm(
                executor.map(self.process_file, args),
                total=len(self.files),
                desc="分析文件中"
            ))

        # 合并结果
        for filename, clusters in results:
            for cluster in clusters:
                entry = self.cluster_cache[cluster]
                entry['count'] += 1
                entry['files'].add(filename)

        return self._generate_report()

    def _generate_report(self):
        """生成排序后的报告"""
        sorted_clusters = sorted(
            self.cluster_cache.items(),
            key=lambda x: (-x[1]['count'], -len(x[1]['files']), x[0])
        )

        report = []
        for rank, (cluster, data) in enumerate(sorted_clusters, 1):
            report.append({
                'Cluster': cluster,
                'Rank': rank,
                'Frequency': data['count'],
                'Range': len(data['files']),
                'Files': '; '.join(sorted(data['files']))
            })
        return report

    def run_interactive(self):
        """交互模式"""
        target = input("请输入目标词：").strip()
        position = input("请选择位置 (L-左/R-右/B-双向)：").strip().upper()
        length = int(input("请输入长度：").strip())

        # 解析位置参数
        position_map = {
            'L': (length, 0),
            'R': (0, length),
            'B': (length, length)
        }
        position_params = {
            'left': position_map[position][0],
            'right': position_map[position][1]
        }

        # 执行分析
        report = self.find_clusters(target, position_params)

        # 打印前20结果
        print("\n{:<40} {:<6} {:<10} {:<6} {}".format(
            '词簇', '排名', '频率', '范围', '文件'))
        print("-" * 100)
        for item in report[:20]:
            print("{:<40} {:<6} {:<10} {:<6} {}".format(
                item['Cluster'][:38],
                item['Rank'],
                item['Frequency'],
                item['Range'],
                item['Files'][:30] + '...' if len(item['Files']) > 30 else item['Files']
            ))

        # 保存结果
        save = input("\n是否保存结果？(y/n): ").lower()
        if save == 'y':
            filename = f"cluster_{target}_{position}_{length}.csv"
            with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=['Cluster', 'Rank', 'Frequency', 'Range', 'Files'])
                writer.writeheader()
                writer.writerows(report)
            print(f"结果已保存至 {filename}")

if __name__ == "__main__":
    folder_path = r"英文新闻"
    stopwords_file = r"stopwords-zh.txt"  # 可选：指定停用词文件路径
    processor = ClusterProcessor(folder_path, stopwords_file)
    processor.run_interactive()