# coding=utf-8
"""
中文文本分析 - KWIC分析 (使用jieba库)
目标：尽可能接近AntConc的分析结果
"""
import jieba
import os
import sys
import pandas as pd
import re
from collections import defaultdict

sys.stdout.reconfigure(encoding='utf-8')

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip() for line in f if line.strip()])
    return stopwords

def clean_text(text):
    """清理文本，保留原始结构用于KWIC"""
    # 移除过多的空白，但保留基本结构
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def preprocess_folder(folder_path, stopwords_file):
    """
    预处理文件夹中的所有文件
    Args:
        folder_path: 文件夹路径
        stopwords_file: 停用词文件路径
    Returns:
        处理后的文档字典
    """
    stopwords = load_stopwords(stopwords_file)
    all_docs = {}
    word_freq = defaultdict(int)
    
    print(f"开始预处理文件夹: {folder_path}")
    
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                # 清理文本
                text = clean_text(text)
                
                # 分词用于词频统计
                words = jieba.cut(text, cut_all=False)
                for word in words:
                    word = word.strip()
                    if (len(word) >= 2 and 
                        word not in stopwords and
                        not word.isdigit() and
                        not word.isspace()):
                        word_freq[word] += 1
                
                # 保存原始文本用于KWIC搜索
                all_docs[filename] = text
                print(f"已处理: {filename}")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    print(f"预处理完成，共处理 {len(all_docs)} 个文件")
    return all_docs, word_freq

def kwic_search(all_docs, keyword, window_size=5):
    """
    执行KWIC搜索
    Args:
        all_docs: 文档字典
        keyword: 搜索关键词
        window_size: 上下文窗口大小
    Returns:
        KWIC结果列表
    """
    results = []
    
    print(f"搜索关键词: '{keyword}'，窗口大小: {window_size}")
    
    for filename, text in all_docs.items():
        # 使用jieba分词
        words = list(jieba.cut(text, cut_all=False))
        
        # 查找关键词位置
        for i, word in enumerate(words):
            if keyword in word or word == keyword:
                # 计算上下文范围
                start_idx = max(0, i - window_size)
                end_idx = min(len(words), i + window_size + 1)
                
                # 提取左上下文
                left_context = ''.join(words[start_idx:i])
                
                # 提取右上下文
                right_context = ''.join(words[i+1:end_idx])
                
                # 清理上下文
                left_context = left_context.strip()
                right_context = right_context.strip()
                
                results.append({
                    'file': filename,
                    'left': left_context,
                    'keyword': word,
                    'right': right_context,
                    'position': i
                })
    
    return results

def print_results(results, max_display=50):
    """打印KWIC结果"""
    print(f"\n=== KWIC分析结果 ===")
    print(f"{'文件':<30} {'左上下文':<30} {'关键词':<15} {'右上下文':<30}")
    print("-" * 105)
    
    for i, result in enumerate(results[:max_display]):
        filename = result['file'][:28] + '..' if len(result['file']) > 30 else result['file']
        left = result['left'][-28:] if len(result['left']) > 30 else result['left']
        keyword = result['keyword']
        right = result['right'][:28] if len(result['right']) > 30 else result['right']
        
        print(f"{filename:<30} {left:<30} {keyword:<15} {right:<30}")

def save_results(results, keyword):
    """保存KWIC结果到CSV文件"""
    if not results:
        print("没有结果可保存")
        return
    
    df = pd.DataFrame(results)
    filename = f"jieba_KWIC_{keyword}.csv"
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"结果已保存至: {filename}")

def main():
    """主函数"""
    # 配置路径
    folder_path = "../中文文本"
    stopwords_file = "../stopwords-zh.txt"
    
    print("=== 中文KWIC分析 (jieba库) ===")
    print("目标：尽可能接近AntConc的分析结果\n")
    
    # 预处理文件
    all_docs, word_freq = preprocess_folder(folder_path, stopwords_file)
    
    if not all_docs:
        print("未找到有效的文档")
        return
    
    # 显示高频词供参考
    print("\n=== 高频词参考 (前20个) ===")
    top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:20]
    for word, freq in top_words:
        print(f"{word}: {freq}")
    
    # 交互式输入
    print("\n" + "="*50)
    keyword = input("请输入要搜索的关键词: ").strip()
    
    if not keyword:
        print("关键词不能为空")
        return
    
    try:
        window_size = int(input("请输入上下文窗口大小 (默认5): ").strip() or "5")
    except ValueError:
        window_size = 5
    
    # 执行搜索
    results = kwic_search(all_docs, keyword, window_size)
    
    # 显示结果
    if results:
        print(f"\n找到 {len(results)} 处匹配")
        print_results(results)
        
        # 询问是否保存
        save_choice = input(f"\n是否保存完整结果到CSV文件？(y/n): ").lower()
        if save_choice == 'y':
            save_results(results, keyword)
    else:
        print(f"未找到关键词 '{keyword}' 的匹配结果")

if __name__ == "__main__":
    main()
