# coding=utf-8
import os
import sys
import csv
import spacy
from collections import defaultdict
from concurrent.futures import ProcessPoolExecutor
from tqdm import tqdm
from functools import partial

sys.stdout.reconfigure(encoding='utf-8')

nlp = spacy.load("de_core_news_sm")
nlp.max_length = 5000000


def default_cluster():
    return {'count': 0, 'files': set()}


class ClusterProcessor:
    def __init__(self, folder_path):
        self.folder_path = folder_path
        self.files = sorted([f for f in os.listdir(folder_path) if f.endswith('.txt')],
                            key=lambda x: x.lower())
        self.file_index = {filename: idx for idx, filename in enumerate(self.files)}
        self.cluster_cache = defaultdict(default_cluster)  # 修改这里

    @staticmethod
    def process_file(args):
        """处理单个文件（静态方法）"""
        folder_path, target, position_params, filename = args
        filepath = os.path.join(folder_path, filename)

        with open(filepath, 'r', encoding='utf-8') as f:
            text = f.read()

        doc = nlp(text)
        tokens = [
            token.lemma_.lower()
            for token in doc
            if token.is_alpha
               and not token.is_stop
               and not token.is_punct
        ]

        clusters = []
        for i, token in enumerate(tokens):
            if token == target:
                start = max(0, i - position_params['left'])
                end = min(len(tokens), i + position_params['right'] + 1)
                cluster = ' '.join(tokens[start:end])
                clusters.append(cluster)

        return filename, clusters

    def find_clusters(self, target_word, position_params):
        """主分析函数"""
        target = target_word.lower()

        # 准备参数
        args = [(self.folder_path, target, position_params, f)
                for f in self.files]

        with ProcessPoolExecutor() as executor:
            results = list(tqdm(
                executor.map(self.process_file, args),
                total=len(self.files),
                desc="分析文件中"
            ))

        # 合并结果
        for filename, clusters in results:
            for cluster in clusters:
                entry = self.cluster_cache[cluster]
                entry['count'] += 1
                entry['files'].add(filename)

        return self._generate_report()

    def _generate_report(self):
        """生成排序后的报告"""
        sorted_clusters = sorted(
            self.cluster_cache.items(),
            key=lambda x: (-x[1]['count'], -len(x[1]['files']), x[0])
        )

        report = []
        for rank, (cluster, data) in enumerate(sorted_clusters, 1):
            report.append({
                'Cluster': cluster,
                'Rank': rank,
                'Frequency': data['count'],
                'Range': len(data['files']),
                'Files': '; '.join(sorted(data['files']))
            })
        return report

    def run_interactive(self):
        """交互模式"""
        target = input("target word:").strip().lower()
        position = input("location (L/R/B):").strip().upper()
        length = int(input("length:").strip())

        # 解析位置参数
        position_map = {
            'L': (length, 0),
            'R': (0, length),
            'B': (length, length)
        }
        position_params = {
            'left': position_map[position][0],
            'right': position_map[position][1]
        }

        # 执行分析
        report = self.find_clusters(target, position_params)

        # 打印前20结果
        print("\n{:<40} {:<6} {:<10} {:<6} {}".format(
            'Cluster', 'Rank', 'Frequency', 'Range', 'Files'))
        print("-" * 100)
        for item in report[:20]:
            print("{:<40} {:<6} {:<10} {:<6} {}".format(
                item['Cluster'][:38],
                item['Rank'],
                item['Frequency'],
                item['Range'],
                item['Files'][:30] + '...' if len(item['Files']) > 30 else item['Files']
            ))

        # 保存结果
        save = input("\nwhether save?:(y/n): ").lower()
        if save == 'y':
            filename = f"cluster_{target}_{position}_{length}.csv"
            with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=['Cluster', 'Rank', 'Frequency', 'Range', 'Files'])
                writer.writeheader()
                writer.writerows(report)
            print(f"结果已保存至 {filename}")


if __name__ == "__main__":
    folder_path = r"D:\桌面\data"
    processor = ClusterProcessor(folder_path)
    processor.run_interactive()