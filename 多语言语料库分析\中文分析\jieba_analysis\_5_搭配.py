# coding=utf-8
"""
中文文本分析 - 搭配分析 (使用jieba库)
目标：尽可能接近AntConc的分析结果
"""
import jieba
import os
import sys
import csv
import math
import re
from collections import defaultdict
from tqdm import tqdm

sys.stdout.reconfigure(encoding='utf-8')

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip() for line in f if line.strip()])
    return stopwords

def clean_text(text):
    """清理文本"""
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def segment_text(text, stopwords):
    """使用jieba进行分词"""
    text = clean_text(text)
    words = jieba.cut(text, cut_all=False)
    
    filtered_words = []
    for word in words:
        word = word.strip()
        if (len(word) >= 2 and 
            word not in stopwords and
            not word.isdigit() and
            not word.isspace()):
            filtered_words.append(word)
    
    return filtered_words

class CollocationAnalyzer:
    def __init__(self, folder_path, stopwords_file):
        self.folder_path = folder_path
        self.files = sorted([f for f in os.listdir(folder_path) if f.endswith('.txt')])
        self.stopwords = load_stopwords(stopwords_file)
        self.word_freq = defaultdict(int)
        self.total_words = 0
        
    def build_corpus_stats(self):
        """构建语料库统计信息"""
        print("构建语料库统计信息...")
        
        for filename in tqdm(self.files, desc="处理文件"):
            filepath = os.path.join(self.folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                words = segment_text(text, self.stopwords)
                for word in words:
                    self.word_freq[word] += 1
                    self.total_words += 1
                    
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
        
        print(f"语料库统计完成: 总词数={self.total_words}, 不重复词数={len(self.word_freq)}")
    
    def extract_collocations(self, target_word, left_span=5, right_span=5):
        """
        提取搭配词
        Args:
            target_word: 目标词
            left_span: 左侧窗口大小
            right_span: 右侧窗口大小
        Returns:
            搭配统计数据
        """
        print(f"提取搭配词: 目标词='{target_word}', 左窗口={left_span}, 右窗口={right_span}")
        
        collocate_stats = defaultdict(lambda: {
            'left_freq': 0, 'right_freq': 0, 'files': set(), 'total_freq': 0
        })
        
        target_freq = 0
        
        for filename in tqdm(self.files, desc="分析搭配"):
            filepath = os.path.join(self.folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                words = segment_text(text, self.stopwords)
                
                # 查找目标词并提取搭配
                for i, word in enumerate(words):
                    if word == target_word:
                        target_freq += 1
                        
                        # 左侧搭配
                        left_start = max(0, i - left_span)
                        for j in range(left_start, i):
                            collocate = words[j]
                            if collocate != target_word:
                                collocate_stats[collocate]['left_freq'] += 1
                                collocate_stats[collocate]['total_freq'] += 1
                                collocate_stats[collocate]['files'].add(filename)
                        
                        # 右侧搭配
                        right_end = min(len(words), i + right_span + 1)
                        for j in range(i + 1, right_end):
                            collocate = words[j]
                            if collocate != target_word:
                                collocate_stats[collocate]['right_freq'] += 1
                                collocate_stats[collocate]['total_freq'] += 1
                                collocate_stats[collocate]['files'].add(filename)
                                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
        
        print(f"目标词 '{target_word}' 出现次数: {target_freq}")
        return collocate_stats, target_freq
    
    def calculate_statistics(self, collocate_stats, target_freq):
        """计算统计指标"""
        results = []
        
        for collocate, stats in collocate_stats.items():
            if stats['total_freq'] >= 2:  # 最小频率阈值
                # 计算互信息 (MI)
                collocate_freq = self.word_freq[collocate]
                expected = (target_freq * collocate_freq) / self.total_words
                
                if expected > 0:
                    mi_score = math.log2(stats['total_freq'] / expected)
                else:
                    mi_score = 0
                
                # 计算似然比 (Log-likelihood)
                observed = stats['total_freq']
                ll_score = 2 * (observed * math.log(observed / expected) if observed > 0 and expected > 0 else 0)
                
                results.append({
                    'collocate': collocate,
                    'freqL': stats['left_freq'],
                    'freqR': stats['right_freq'],
                    'total_freq': stats['total_freq'],
                    'range': len(stats['files']),
                    'mi_score': mi_score,
                    'likelihood': ll_score
                })
        
        # 按总频率排序
        results.sort(key=lambda x: x['total_freq'], reverse=True)
        
        # 添加排名
        for rank, result in enumerate(results, 1):
            result['rank'] = rank
        
        return results
    
    def print_results(self, results, max_display=20):
        """打印搭配分析结果"""
        if not results:
            print("没有找到符合条件的搭配词")
            return
        
        print(f"\n=== 搭配分析结果 (显示前{min(max_display, len(results))}个) ===")
        print(f"{'排名':<6} {'搭配词':<20} {'左频':<6} {'右频':<6} {'总频':<6} {'范围':<6} {'MI':<8} {'LL':<8}")
        print("-" * 80)
        
        for result in results[:max_display]:
            print(f"{result['rank']:<6} {result['collocate']:<20} "
                  f"{result['freqL']:<6} {result['freqR']:<6} {result['total_freq']:<6} "
                  f"{result['range']:<6} {result['mi_score']:<8.3f} {result['likelihood']:<8.3f}")
    
    def save_results(self, results, target_word, left_span, right_span):
        """保存搭配分析结果"""
        if not results:
            print("没有结果可保存")
            return
        
        filename = f"jieba_collocate_{target_word}_{left_span}-{right_span}.csv"
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
            fieldnames = ['collocate', 'rank', 'freqL', 'freqR', 'total_freq', 'range', 'mi_score', 'likelihood']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for result in results:
                writer.writerow({
                    'collocate': result['collocate'],
                    'rank': result['rank'],
                    'freqL': result['freqL'],
                    'freqR': result['freqR'],
                    'total_freq': result['total_freq'],
                    'range': result['range'],
                    'mi_score': round(result['mi_score'], 4),
                    'likelihood': round(result['likelihood'], 4)
                })
        
        print(f"结果已保存至: {filename}")
    
    def run_interactive(self):
        """运行交互式搭配分析"""
        print("=== 中文搭配分析 (jieba库) ===")
        print("目标：尽可能接近AntConc的分析结果\n")
        
        # 构建语料库统计
        self.build_corpus_stats()
        
        # 输入参数
        target_word = input("\n请输入目标词: ").strip()
        if not target_word:
            print("目标词不能为空")
            return
        
        if target_word not in self.word_freq:
            print(f"目标词 '{target_word}' 在语料库中未找到")
            return
        
        try:
            left_span = int(input("请输入左侧窗口大小 (默认5): ").strip() or "5")
            right_span = int(input("请输入右侧窗口大小 (默认5): ").strip() or "5")
        except ValueError:
            left_span, right_span = 5, 5
        
        # 执行分析
        print(f"\n开始搭配分析...")
        collocate_stats, target_freq = self.extract_collocations(target_word, left_span, right_span)
        
        if not collocate_stats:
            print(f"未找到词语 '{target_word}' 的搭配")
            return
        
        # 计算统计指标
        results = self.calculate_statistics(collocate_stats, target_freq)
        
        if results:
            self.print_results(results)
            
            print(f"\n=== 统计信息 ===")
            print(f"搭配词数量: {len(results)}")
            print(f"目标词频率: {target_freq}")
            
            # 询问是否保存
            save_choice = input(f"\n是否保存结果到CSV文件？(y/n): ").lower()
            if save_choice == 'y':
                self.save_results(results, target_word, left_span, right_span)
        else:
            print("未找到符合条件的搭配词")

def main():
    """主函数"""
    folder_path = "../中文文本"
    stopwords_file = "../stopwords-zh.txt"
    
    analyzer = CollocationAnalyzer(folder_path, stopwords_file)
    analyzer.run_interactive()

if __name__ == "__main__":
    main()
