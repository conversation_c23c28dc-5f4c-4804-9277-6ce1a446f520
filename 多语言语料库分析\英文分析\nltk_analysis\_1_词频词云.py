# coding=utf-8
"""
英文文本分析 - 词频分析 (使用NLTK库)
目标：尽可能接近AntConc的分析结果
"""
import nltk
import pandas as pd
import sys
import os
from collections import defaultdict, Counter
import re
from nltk.tokenize import word_tokenize
from nltk.stem import WordNetLemmatizer

sys.stdout.reconfigure(encoding='utf-8')

# 下载必要的NLTK数据
required_data = ['punkt', 'wordnet', 'averaged_perceptron_tagger']
for data in required_data:
    try:
        if data == 'punkt':
            nltk.data.find('tokenizers/punkt')
        elif data == 'wordnet':
            nltk.data.find('corpora/wordnet')
        elif data == 'averaged_perceptron_tagger':
            nltk.data.find('taggers/averaged_perceptron_tagger')
    except LookupError:
        print(f"下载 {data}...")
        nltk.download(data)

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip().lower() for line in f if line.strip()])
    return stopwords

def clean_text(text):
    """清理文本"""
    # 移除非字母字符，保留空格
    text = re.sub(r'[^a-zA-Z\s]', ' ', text)
    # 移除多余空白
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def segment_text(text, stopwords):
    """使用NLTK进行英文分词和词形还原"""
    text = clean_text(text)
    tokens = word_tokenize(text.lower())
    
    lemmatizer = WordNetLemmatizer()
    filtered_words = []
    
    for token in tokens:
        if (len(token) >= 2 and 
            token not in stopwords and
            token.isalpha()):
            lemma = lemmatizer.lemmatize(token)
            filtered_words.append(lemma)
    
    return filtered_words

def process_files(folder_path, stopwords_file):
    """处理文件夹中的所有文本文件"""
    stopwords = load_stopwords(stopwords_file)
    word_freq = Counter()
    file_count = 0
    total_words = 0
    
    print(f"开始处理文件夹: {folder_path}")
    
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                words = segment_text(text, stopwords)
                word_freq.update(words)
                total_words += len(words)
                file_count += 1
                
                print(f"已处理: {filename} (词数: {len(words)})")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    print(f"\n处理完成:")
    print(f"- 文件数量: {file_count}")
    print(f"- 总词数: {total_words}")
    print(f"- 不重复词数: {len(word_freq)}")
    
    return word_freq

def generate_report(word_freq):
    """生成词频分析报告"""
    word_list = [(word, freq) for word, freq in word_freq.most_common()]
    total_words = sum(freq for _, freq in word_list)
    
    report_data = []
    for rank, (word, freq) in enumerate(word_list, 1):
        percentage = (freq / total_words) * 100
        report_data.append({
            'Rank': rank,
            'Word': word,
            'Frequency': freq,
            'Percentage': round(percentage, 4)
        })
    
    return pd.DataFrame(report_data)

def main():
    """主函数"""
    folder_path = "../英文文本"
    stopwords_file = "../stopwords-en.txt"
    output_file = 'nltk_word_frequency_report.csv'
    
    print("=== 英文词频分析 (NLTK库) ===")
    print("目标：尽可能接近AntConc的分析结果\n")
    
    word_freq = process_files(folder_path, stopwords_file)
    
    if not word_freq:
        print("未找到有效的词汇数据")
        return
    
    report_df = generate_report(word_freq)
    
    print("\n=== 前20个高频词 ===")
    print(report_df.head(20).to_string(index=False))
    
    report_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n完整分析结果已保存至: {output_file}")
    
    # 显示统计信息
    print(f"\n=== 统计信息 ===")
    print(f"总词汇数: {len(report_df)}")
    print(f"总频次: {report_df['Frequency'].sum()}")
    print(f"平均频次: {report_df['Frequency'].mean():.2f}")

if __name__ == "__main__":
    main()
