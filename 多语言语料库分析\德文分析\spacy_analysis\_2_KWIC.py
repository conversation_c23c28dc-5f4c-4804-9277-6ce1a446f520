# coding=utf-8
"""
德文文本分析 - KWIC分析 (使用spaCy库)
"""
import spacy
import os
import sys
import pandas as pd
import re
from collections import defaultdict

sys.stdout.reconfigure(encoding='utf-8')

try:
    nlp = spacy.load("de_core_news_sm")
    nlp.max_length = 5000000
except OSError:
    print("请先安装德文模型: python -m spacy download de_core_news_sm")
    sys.exit(1)

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip().lower() for line in f if line.strip()])
    return stopwords

def kwic_search(all_docs, keyword, window_size=5):
    """执行KWIC搜索"""
    results = []
    
    for filename, text in all_docs.items():
        doc = nlp(text)
        tokens = [token.text for token in doc]
        
        for i, token_text in enumerate(tokens):
            if keyword.lower() in token_text.lower() or token_text.lower() == keyword.lower():
                start_idx = max(0, i - window_size)
                end_idx = min(len(tokens), i + window_size + 1)
                
                left_context = ''.join(tokens[start_idx:i])
                right_context = ''.join(tokens[i+1:end_idx])
                
                results.append({
                    'file': filename,
                    'left': left_context.strip(),
                    'keyword': token_text,
                    'right': right_context.strip(),
                    'position': i
                })
    
    return results

def main():
    """主函数"""
    folder_path = "../德语文本"
    stopwords_file = "../stopwords-de.txt"
    keyword = "china"  # 固定关键词
    
    print("=== 德文KWIC分析 (spaCy库) ===")
    
    all_docs = {}
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    all_docs[filename] = f.read()
            except Exception as e:
                print(f"读取文件 {filename} 时出错: {e}")
    
    results = kwic_search(all_docs, keyword)
    
    if results:
        df = pd.DataFrame(results)
        filename = f"spacy_KWIC_{keyword}.csv"
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"找到 {len(results)} 处匹配，结果已保存至: {filename}")
        
        # 显示前10个结果
        print(f"\n前10个匹配结果:")
        for i, result in enumerate(results[:10]):
            print(f"{i+1}. {result['left']} [{result['keyword']}] {result['right']}")
    else:
        print(f"未找到关键词 '{keyword}' 的匹配结果")

if __name__ == "__main__":
    main()
