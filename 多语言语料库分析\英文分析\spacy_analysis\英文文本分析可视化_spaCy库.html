<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts-wordcloud.min.js"></script>

    
</head>
<body >
            <style>
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
        }

        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 12px 16px;
            transition: 0.3s;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #ccc;
        }

        .chart-container {
            display: block;
        }

        .chart-container:nth-child(n+2) {
            display: none;
        }
    </style>
    <div class="tab">
            <button class="tablinks" onclick="showChart(event, '43eaf78453744cd187fbbaa044e7cfea')">首页</button>
            <button class="tablinks" onclick="showChart(event, 'd10d4bdb2c884014a1beeb018294d22f')">词频分析-词云图</button>
            <button class="tablinks" onclick="showChart(event, '75efa51aee7e47aa8ea9a7f8a52b0400')">词频分析-柱状图</button>
            <button class="tablinks" onclick="showChart(event, 'b92afc99737849cf86f82e20b52e273d')">KWIC分析</button>
            <button class="tablinks" onclick="showChart(event, 'ca60afa8911a491c889e606ef043127f')">词语分布-频率图</button>
            <button class="tablinks" onclick="showChart(event, '828cf8456b43428490884f09c81b2c08')">词语分布-离散度图</button>
            <button class="tablinks" onclick="showChart(event, '8b4522b4b0dd4297b2c0d5b1af2205b9')">词语分布-详细数据</button>
            <button class="tablinks" onclick="showChart(event, '61b92200f8b540558460a485f4697da6')">词簇分析-频率图</button>
            <button class="tablinks" onclick="showChart(event, 'a268624960ff44a2a282a29447ff9a24')">词簇分析-详细数据</button>
            <button class="tablinks" onclick="showChart(event, '90ed66c30de049e1b8979ba4b6be327f')">搭配分析-频率图</button>
            <button class="tablinks" onclick="showChart(event, '44adbcdfcd2a43399bc92722c0a7dbd2')">搭配分析-互信息图</button>
            <button class="tablinks" onclick="showChart(event, '747f092063f943a99dda875f77f8d424')">搭配分析-详细数据</button>
    </div>

    <div class="box">
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="43eaf78453744cd187fbbaa044e7cfea" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>分析类型</th>
            <th>说明 (spaCy库实现)</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>词频分析</td>
            <td>使用spaCy库进行英文分词和词性标注</td>
        </tr>
        <tr>
            <td>KWIC分析</td>
            <td>关键词上下文索引，基于spaCy神经网络分词</td>
        </tr>
        <tr>
            <td>词语分布分析</td>
            <td>分析词语在不同文件中的分布情况</td>
        </tr>
        <tr>
            <td>词簇分析</td>
            <td>分析词语簇的分布和频率</td>
        </tr>
        <tr>
            <td>搭配分析</td>
            <td>分析词语的常见搭配，计算互信息和似然比</td>
        </tr>
        <tr>
            <td>技术特点</td>
            <td>基于神经网络的现代NLP库，支持词形还原</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="d10d4bdb2c884014a1beeb018294d22f" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('d10d4bdb2c884014a1beeb018294d22f').style.width = document.getElementById('d10d4bdb2c884014a1beeb018294d22f').parentNode.clientWidth + 'px';
        var chart_d10d4bdb2c884014a1beeb018294d22f = echarts.init(
            document.getElementById('d10d4bdb2c884014a1beeb018294d22f'), 'white', {renderer: 'canvas'});
        var option_d10d4bdb2c884014a1beeb018294d22f = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "wordCloud",
            "shape": "circle",
            "rotationRange": [
                -90,
                90
            ],
            "rotationStep": 45,
            "girdSize": 20,
            "sizeRange": [
                15,
                80
            ],
            "data": [
                {
                    "name": "china",
                    "value": 45,
                    "textStyle": {
                        "color": "rgb(11,70,6)"
                    }
                },
                {
                    "name": "trump",
                    "value": 33,
                    "textStyle": {
                        "color": "rgb(56,94,117)"
                    }
                },
                {
                    "name": "chinese",
                    "value": 25,
                    "textStyle": {
                        "color": "rgb(122,50,107)"
                    }
                },
                {
                    "name": "global",
                    "value": 19,
                    "textStyle": {
                        "color": "rgb(36,113,3)"
                    }
                },
                {
                    "name": "president",
                    "value": 13,
                    "textStyle": {
                        "color": "rgb(40,94,89)"
                    }
                },
                {
                    "name": "tiktok",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(118,38,22)"
                    }
                },
                {
                    "name": "beijing",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(88,50,18)"
                    }
                },
                {
                    "name": "washington",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(104,106,153)"
                    }
                },
                {
                    "name": "empire",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(74,53,2)"
                    }
                },
                {
                    "name": "trade",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(154,63,92)"
                    }
                },
                {
                    "name": "tariffs",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(51,154,44)"
                    }
                },
                {
                    "name": "cent",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(92,147,129)"
                    }
                },
                {
                    "name": "donald",
                    "value": 9,
                    "textStyle": {
                        "color": "rgb(5,14,21)"
                    }
                },
                {
                    "name": "american",
                    "value": 9,
                    "textStyle": {
                        "color": "rgb(117,154,12)"
                    }
                },
                {
                    "name": "america",
                    "value": 9,
                    "textStyle": {
                        "color": "rgb(39,115,46)"
                    }
                },
                {
                    "name": "power",
                    "value": 9,
                    "textStyle": {
                        "color": "rgb(40,83,110)"
                    }
                },
                {
                    "name": "deal",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(149,21,102)"
                    }
                },
                {
                    "name": "war",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(11,60,150)"
                    }
                },
                {
                    "name": "life",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(99,138,16)"
                    }
                },
                {
                    "name": "month",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(69,61,57)"
                    }
                },
                {
                    "name": "house",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(64,51,159)"
                    }
                },
                {
                    "name": "economic",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(15,128,142)"
                    }
                },
                {
                    "name": "call",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(72,96,35)"
                    }
                },
                {
                    "name": "return",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(125,104,68)"
                    }
                },
                {
                    "name": "administration",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(112,17,8)"
                    }
                },
                {
                    "name": "white",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(16,37,99)"
                    }
                },
                {
                    "name": "united",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(109,76,140)"
                    }
                },
                {
                    "name": "country",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(60,65,138)"
                    }
                },
                {
                    "name": "foreign",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(133,107,111)"
                    }
                },
                {
                    "name": "tech",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(69,103,108)"
                    }
                },
                {
                    "name": "companies",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(57,72,147)"
                    }
                },
                {
                    "name": "military",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(3,126,24)"
                    }
                },
                {
                    "name": "diplomatic",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(0,132,29)"
                    }
                },
                {
                    "name": "wang",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(6,52,61)"
                    }
                },
                {
                    "name": "security",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(44,12,25)"
                    }
                },
                {
                    "name": "told",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(150,55,28)"
                    }
                },
                {
                    "name": "people",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(107,41,108)"
                    }
                },
                {
                    "name": "international",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(103,13,44)"
                    }
                },
                {
                    "name": "top",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(94,123,18)"
                    }
                },
                {
                    "name": "range",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(47,39,31)"
                    }
                },
                {
                    "name": "canada",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(95,125,8)"
                    }
                },
                {
                    "name": "union",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(157,34,115)"
                    }
                },
                {
                    "name": "threat",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(99,144,21)"
                    }
                },
                {
                    "name": "including",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(10,83,31)"
                    }
                },
                {
                    "name": "products",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(1,160,21)"
                    }
                },
                {
                    "name": "maga",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(136,74,146)"
                    }
                },
                {
                    "name": "retreat",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(40,88,84)"
                    }
                },
                {
                    "name": "xi",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(43,55,0)"
                    }
                },
                {
                    "name": "photo",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(85,20,131)"
                    }
                },
                {
                    "name": "bank",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(152,118,96)"
                    }
                },
                {
                    "name": "dollar",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(116,145,126)"
                    }
                },
                {
                    "name": "economy",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(41,59,70)"
                    }
                },
                {
                    "name": "time",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(63,141,125)"
                    }
                },
                {
                    "name": "tariff",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(72,81,32)"
                    }
                },
                {
                    "name": "expectancy",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(120,9,104)"
                    }
                },
                {
                    "name": "trap",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(158,150,147)"
                    }
                },
                {
                    "name": "prices",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(142,7,137)"
                    }
                },
                {
                    "name": "minerals",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(153,47,153)"
                    }
                },
                {
                    "name": "bytedance",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(36,97,143)"
                    }
                },
                {
                    "name": "sell",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(84,4,40)"
                    }
                },
                {
                    "name": "reporters",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(49,137,57)"
                    }
                },
                {
                    "name": "late",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(138,88,153)"
                    }
                },
                {
                    "name": "remain",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(46,67,73)"
                    }
                },
                {
                    "name": "firm",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(155,95,32)"
                    }
                },
                {
                    "name": "investment",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(151,26,129)"
                    }
                },
                {
                    "name": "operations",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(52,57,122)"
                    }
                },
                {
                    "name": "held",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(46,159,28)"
                    }
                },
                {
                    "name": "wednesday",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(4,16,63)"
                    }
                },
                {
                    "name": "appeared",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(83,133,45)"
                    }
                },
                {
                    "name": "largest",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(129,105,153)"
                    }
                },
                {
                    "name": "sale",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(125,134,125)"
                    }
                },
                {
                    "name": "week",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(91,21,3)"
                    }
                },
                {
                    "name": "day",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(69,83,59)"
                    }
                },
                {
                    "name": "policies",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(51,106,12)"
                    }
                },
                {
                    "name": "common",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(108,15,149)"
                    }
                },
                {
                    "name": "wars",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(131,99,60)"
                    }
                },
                {
                    "name": "european",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(51,106,84)"
                    }
                },
                {
                    "name": "talks",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(159,26,34)"
                    }
                },
                {
                    "name": "tuesday",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(143,9,127)"
                    }
                },
                {
                    "name": "list",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(1,158,129)"
                    }
                },
                {
                    "name": "trading",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(123,61,72)"
                    }
                },
                {
                    "name": "data",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(46,157,117)"
                    }
                },
                {
                    "name": "services",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(10,98,51)"
                    }
                },
                {
                    "name": "century",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(150,20,144)"
                    }
                },
                {
                    "name": "windmills",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(31,29,119)"
                    }
                },
                {
                    "name": "history",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(114,56,150)"
                    }
                },
                {
                    "name": "decline",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(73,76,57)"
                    }
                },
                {
                    "name": "major",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(117,121,47)"
                    }
                },
                {
                    "name": "financial",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(42,16,107)"
                    }
                },
                {
                    "name": "mid",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(133,91,33)"
                    }
                },
                {
                    "name": "renminbi",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(75,48,99)"
                    }
                },
                {
                    "name": "cross",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(64,8,92)"
                    }
                },
                {
                    "name": "border",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(78,19,82)"
                    }
                },
                {
                    "name": "transactions",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(66,119,24)"
                    }
                },
                {
                    "name": "germany",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(48,11,143)"
                    }
                },
                {
                    "name": "britain",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(47,60,0)"
                    }
                },
                {
                    "name": "countries",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(14,117,156)"
                    }
                },
                {
                    "name": "thucydides",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(49,15,19)"
                    }
                },
                {
                    "name": "rising",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(42,72,37)"
                    }
                },
                {
                    "name": "allison",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(39,127,149)"
                    }
                }
            ],
            "drawOutOfBound": false,
            "textStyle": {
                "normal": {
                    "fontFamily": "Arial"
                },
                "emphasis": {}
            }
        }
    ],
    "legend": [
        {
            "data": [],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u8bcd\u9891\u5206\u6790 - \u8bcd\u4e91\u56fe (spaCy\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_d10d4bdb2c884014a1beeb018294d22f.setOption(option_d10d4bdb2c884014a1beeb018294d22f);
    </script>
                <div id="75efa51aee7e47aa8ea9a7f8a52b0400" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('75efa51aee7e47aa8ea9a7f8a52b0400').style.width = document.getElementById('75efa51aee7e47aa8ea9a7f8a52b0400').parentNode.clientWidth + 'px';
        var chart_75efa51aee7e47aa8ea9a7f8a52b0400 = echarts.init(
            document.getElementById('75efa51aee7e47aa8ea9a7f8a52b0400'), 'white', {renderer: 'canvas'});
        var option_75efa51aee7e47aa8ea9a7f8a52b0400 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u8bcd\u9891",
            "legendHoverLink": true,
            "data": [
                45,
                33,
                25,
                19,
                13,
                12,
                12,
                12,
                12,
                11,
                11,
                11,
                9,
                9,
                9,
                9,
                7,
                7,
                7,
                6
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u8bcd\u9891"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "china",
                "trump",
                "chinese",
                "global",
                "president",
                "tiktok",
                "beijing",
                "washington",
                "empire",
                "trade",
                "tariffs",
                "cent",
                "donald",
                "american",
                "america",
                "power",
                "deal",
                "war",
                "life",
                "month"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u8bcd\u9891\u5206\u6790 - \u524d20\u9ad8\u9891\u8bcd (spaCy\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_75efa51aee7e47aa8ea9a7f8a52b0400.setOption(option_75efa51aee7e47aa8ea9a7f8a52b0400);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="b92afc99737849cf86f82e20b52e273d" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>左上下文</th>
            <th>关键词</th>
            <th>右上下文</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>chinese imports and demand that</td>
            <td>china</td>
            <td>sell the us arm of</td>
        </tr>
        <tr>
            <td>social media platform unit of</td>
            <td>chinas</td>
            <td>bytedance without sale tiktok faces</td>
        </tr>
        <tr>
            <td>in tariffs in return for</td>
            <td>china</td>
            <td>agreeing to tiktok sale trump</td>
        </tr>
        <tr>
            <td>they can agree on is</td>
            <td>china</td>
            <td>and the threat they see</td>
        </tr>
        <tr>
            <td>trump tech export controls against</td>
            <td>china</td>
            <td>us president donald trump could</td>
        </tr>
        <tr>
            <td>buying usorigin products to support</td>
            <td>chinas</td>
            <td>development of quantum and hypersonic</td>
        </tr>
        <tr>
            <td>retreat now confronted by resurgent</td>
            <td>china</td>
            <td>its most significant economic diplomatic</td>
        </tr>
        <tr>
            <td>power or retreat as xis</td>
            <td>china</td>
            <td>emerges as major global player</td>
        </tr>
        <tr>
            <td>of the peoples republic of</td>
            <td>china</td>
            <td>and the subsequent material contributions</td>
        </tr>
        <tr>
            <td>reforms coproduced the return of</td>
            <td>china</td>
            <td>after two centuries of subjugation</td>
        </tr>
        <tr>
            <td>to the us technically speaking</td>
            <td>china</td>
            <td>is no longer merely country</td>
        </tr>
        <tr>
            <td>as explain at length in</td>
            <td>chinas</td>
            <td>galaxy empire is that china</td>
        </tr>
        <tr>
            <td>chinas galaxy empire is that</td>
            <td>china</td>
            <td>is rapidly becoming an empire</td>
        </tr>
        <tr>
            <td>orientalist ignorance and denial of</td>
            <td>chinas</td>
            <td>imperial ambitions are the underbelly</td>
        </tr>
        <tr>
            <td>in the world are chinese</td>
            <td>china</td>
            <td>has outflanked bodies such as</td>
        </tr>
        <tr>
            <td>the asian infrastructure investment bank</td>
            <td>china</td>
            <td>is spearheading the global rebellion</td>
        </tr>
        <tr>
            <td>topped the us dollar in</td>
            <td>chinas</td>
            <td>crossborder transactions with nearly us</td>
        </tr>
        <tr>
            <td>hasnt enjoyed trade surplus since</td>
            <td>china</td>
            <td>is the largest trading country</td>
        </tr>
        <tr>
            <td>usled efforts to decouple from</td>
            <td>china</td>
            <td>by applying tariff penalties boycotting</td>
        </tr>
        <tr>
            <td>zte and other chinese companies</td>
            <td>chinas</td>
            <td>economy unlike the former soviet</td>
        </tr>
        <tr>
            <td>lithiumion battery manufacturer durapower holdings</td>
            <td>china</td>
            <td>meanwhile produces onethird of the</td>
        </tr>
        <tr>
            <td>south korea and britain combined</td>
            <td>china</td>
            <td>is the european unions and</td>
        </tr>
        <tr>
            <td>are actively drawing closer to</td>
            <td>china</td>
            <td>in the renminbi surpassed the</td>
        </tr>
        <tr>
            <td>surpassed the us dollar in</td>
            <td>chinas</td>
            <td>crossborder transactions for the first</td>
        </tr>
        <tr>
            <td>shifts are also happening in</td>
            <td>china</td>
            <td>in matters of everyday life</td>
        </tr>
        <tr>
            <td>expectancy is even higher among</td>
            <td>chinas</td>
            <td>million strong middle classes how</td>
        </tr>
        <tr>
            <td>during the past two decades</td>
            <td>china</td>
            <td>now produces more stem graduates</td>
        </tr>
        <tr>
            <td>destined for war often frames</td>
            <td>chinaus</td>
            <td>tensions and conflict as inevitable</td>
        </tr>
        <tr>
            <td>dialogue with graham allison on</td>
            <td>chinaus</td>
            <td>relations held last month on</td>
        </tr>
        <tr>
            <td>for war can america and</td>
            <td>china</td>
            <td>escape thucydidess does not predict</td>
        </tr>
        <tr>
            <td>to rewrite the pattern that</td>
            <td>chinas</td>
            <td>economy achieved per cent growth</td>
        </tr>
        <tr>
            <td>per cent of americans view</td>
            <td>china</td>
            <td>unfavourably trump praised chinese president</td>
        </tr>
        <tr>
            <td>president donald trump could visit</td>
            <td>china</td>
            <td>as early as next month</td>
        </tr>
        <tr>
            <td>have been around trump visiting</td>
            <td>china</td>
            <td>according to sources it is</td>
        </tr>
        <tr>
            <td>washington is going through drastic</td>
            <td>china</td>
            <td>hits back at trump with</td>
        </tr>
        <tr>
            <td>strategic minerals have soared in</td>
            <td>china</td>
            <td>over the past year as</td>
        </tr>
        <tr>
            <td>with prices rising rapidly since</td>
            <td>china</td>
            <td>began restricting exports of the</td>
        </tr>
        <tr>
            <td>more than per cent in</td>
            <td>china</td>
            <td>while prices in rotterdam have</td>
        </tr>
        <tr>
            <td>tight and many countries including</td>
            <td>china</td>
            <td>and the united states have</td>
        </tr>
        <tr>
            <td>nan</td>
            <td>chinas</td>
            <td>top diplomat has launched direct</td>
        </tr>
        <tr>
            <td>as an irresponsible big power</td>
            <td>chinas</td>
            <td>top diplomat wang yi lays</td>
        </tr>
        <tr>
            <td>conference during the ongoing two</td>
            <td>chinas</td>
            <td>annual parliamentary gathering has its</td>
        </tr>
        <tr>
            <td>its people got better or</td>
            <td>china</td>
            <td>will definitely take countermeasures in</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="ca60afa8911a491c889e606ef043127f" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('ca60afa8911a491c889e606ef043127f').style.width = document.getElementById('ca60afa8911a491c889e606ef043127f').parentNode.clientWidth + 'px';
        var chart_ca60afa8911a491c889e606ef043127f = echarts.init(
            document.getElementById('ca60afa8911a491c889e606ef043127f'), 'white', {renderer: 'canvas'});
        var option_ca60afa8911a491c889e606ef043127f = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u8bcd\u9891",
            "legendHoverLink": true,
            "data": [
                3,
                1,
                2,
                23,
                5,
                3,
                4,
                4
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u8bcd\u9891"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "10.txt",
                "2.txt",
                "3.txt",
                "4.txt",
                "5.txt",
                "6.txt",
                "7.txt",
                "8.txt"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u8bcd\u8bed\u5206\u5e03\u5206\u6790 - \u6587\u4ef6\u9891\u7387\u5206\u5e03 (spaCy\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_ca60afa8911a491c889e606ef043127f.setOption(option_ca60afa8911a491c889e606ef043127f);
    </script>
                <div id="828cf8456b43428490884f09c81b2c08" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('828cf8456b43428490884f09c81b2c08').style.width = document.getElementById('828cf8456b43428490884f09c81b2c08').parentNode.clientWidth + 'px';
        var chart_828cf8456b43428490884f09c81b2c08 = echarts.init(
            document.getElementById('828cf8456b43428490884f09c81b2c08'), 'white', {renderer: 'canvas'});
        var option_828cf8456b43428490884f09c81b2c08 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "line",
            "name": "\u79bb\u6563\u5ea6",
            "connectNulls": false,
            "xAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "10.txt",
                    0.7407407407407408
                ],
                [
                    "2.txt",
                    0.0
                ],
                [
                    "3.txt",
                    0.5555555555555555
                ],
                [
                    "4.txt",
                    0.9619827767275784
                ],
                [
                    "5.txt",
                    0.8888888888888888
                ],
                [
                    "6.txt",
                    0.7407407407407408
                ],
                [
                    "7.txt",
                    0.8333333333333334
                ],
                [
                    "8.txt",
                    0.8333333333333334
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        }
    ],
    "legend": [
        {
            "data": [
                "\u79bb\u6563\u5ea6"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "10.txt",
                "2.txt",
                "3.txt",
                "4.txt",
                "5.txt",
                "6.txt",
                "7.txt",
                "8.txt"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "min": 0,
            "max": 1,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u8bcd\u8bed\u5206\u5e03\u5206\u6790 - \u79bb\u6563\u5ea6\u5206\u5e03 (spaCy\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_828cf8456b43428490884f09c81b2c08.setOption(option_828cf8456b43428490884f09c81b2c08);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="8b4522b4b0dd4297b2c0d5b1af2205b9" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>序号</th>
            <th>文件ID</th>
            <th>文件名</th>
            <th>总词数</th>
            <th>频率</th>
            <th>离散度</th>
            <th>分布图</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>1</td>
            <td>2</td>
            <td>10.txt</td>
            <td>114</td>
            <td>3</td>
            <td>0.7407</td>
            <td>-----------------|---|----------|-----------------</td>
        </tr>
        <tr>
            <td>2</td>
            <td>3</td>
            <td>2.txt</td>
            <td>102</td>
            <td>1</td>
            <td>0.0000</td>
            <td>-------------------------------------------|------</td>
        </tr>
        <tr>
            <td>3</td>
            <td>4</td>
            <td>3.txt</td>
            <td>87</td>
            <td>2</td>
            <td>0.5556</td>
            <td>-------|-----------------------|------------------</td>
        </tr>
        <tr>
            <td>4</td>
            <td>5</td>
            <td>4.txt</td>
            <td>598</td>
            <td>23</td>
            <td>0.9620</td>
            <td>--|----------|-|---|||||--|-||||||--||--|||--|--|-</td>
        </tr>
        <tr>
            <td>5</td>
            <td>6</td>
            <td>5.txt</td>
            <td>166</td>
            <td>5</td>
            <td>0.8889</td>
            <td>---|--------|--------------|--|-------------|-----</td>
        </tr>
        <tr>
            <td>6</td>
            <td>7</td>
            <td>6.txt</td>
            <td>72</td>
            <td>3</td>
            <td>0.7407</td>
            <td>--|----------------|------------|-----------------</td>
        </tr>
        <tr>
            <td>7</td>
            <td>8</td>
            <td>7.txt</td>
            <td>99</td>
            <td>4</td>
            <td>0.8333</td>
            <td>--|--------------------------|-----|-----------|--</td>
        </tr>
        <tr>
            <td>8</td>
            <td>9</td>
            <td>8.txt</td>
            <td>122</td>
            <td>4</td>
            <td>0.8333</td>
            <td>|-------------------|--------|-----|--------------</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="61b92200f8b540558460a485f4697da6" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('61b92200f8b540558460a485f4697da6').style.width = document.getElementById('61b92200f8b540558460a485f4697da6').parentNode.clientWidth + 'px';
        var chart_61b92200f8b540558460a485f4697da6 = echarts.init(
            document.getElementById('61b92200f8b540558460a485f4697da6'), 'white', {renderer: 'canvas'});
        var option_61b92200f8b540558460a485f4697da6 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u9891\u7387",
            "legendHoverLink": true,
            "data": [
                3,
                2,
                2
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u9891\u7387"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "dollar china",
                "return china",
                "xi china"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u8bcd\u7c07\u5206\u6790 - \u9891\u7387\u5206\u5e03 (spaCy\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_61b92200f8b540558460a485f4697da6.setOption(option_61b92200f8b540558460a485f4697da6);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="a268624960ff44a2a282a29447ff9a24" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>词簇</th>
            <th>排名</th>
            <th>频率</th>
            <th>范围</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>dollar china</td>
            <td>1</td>
            <td>3</td>
            <td>1</td>
        </tr>
        <tr>
            <td>return china</td>
            <td>2</td>
            <td>2</td>
            <td>2</td>
        </tr>
        <tr>
            <td>xi china</td>
            <td>3</td>
            <td>2</td>
            <td>1</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="90ed66c30de049e1b8979ba4b6be327f" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('90ed66c30de049e1b8979ba4b6be327f').style.width = document.getElementById('90ed66c30de049e1b8979ba4b6be327f').parentNode.clientWidth + 'px';
        var chart_90ed66c30de049e1b8979ba4b6be327f = echarts.init(
            document.getElementById('90ed66c30de049e1b8979ba4b6be327f'), 'white', {renderer: 'canvas'});
        var option_90ed66c30de049e1b8979ba4b6be327f = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u603b\u9891\u7387",
            "legendHoverLink": true,
            "data": [
                8,
                7,
                6,
                4,
                4,
                4,
                4,
                4,
                4,
                4,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u603b\u9891\u7387"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "trump",
                "empire",
                "global",
                "chinese",
                "power",
                "photo",
                "mid",
                "renminbi",
                "dollar",
                "cent",
                "tariffs",
                "president",
                "retreat",
                "diplomatic",
                "maga",
                "rapidly",
                "time",
                "cross",
                "border",
                "transactions",
                "surplus",
                "economy",
                "surpassed",
                "life",
                "prices",
                "popular",
                "social",
                "sale",
                "tiktok",
                "return",
                "threat",
                "international",
                "controls",
                "donald",
                "products",
                "restoration",
                "xi",
                "emerges",
                "major",
                "player",
                "people",
                "contributions",
                "sweeping",
                "reforms",
                "prominence",
                "country",
                "explain",
                "length",
                "galaxy",
                "trade",
                "trading",
                "half",
                "union",
                "produces",
                "japan",
                "germany",
                "south",
                "india",
                "images",
                "expectancy",
                "destined",
                "war",
                "thucydides",
                "trap",
                "allison",
                "month",
                "america",
                "predict",
                "doom",
                "sources",
                "american",
                "discussions",
                "washington",
                "strategic",
                "minerals",
                "metal",
                "top",
                "diplomat",
                "pressure"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u642d\u914d\u5206\u6790 - \u9891\u7387\u5206\u5e03 (spaCy\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_90ed66c30de049e1b8979ba4b6be327f.setOption(option_90ed66c30de049e1b8979ba4b6be327f);
    </script>
                <div id="44adbcdfcd2a43399bc92722c0a7dbd2" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('44adbcdfcd2a43399bc92722c0a7dbd2').style.width = document.getElementById('44adbcdfcd2a43399bc92722c0a7dbd2').parentNode.clientWidth + 'px';
        var chart_44adbcdfcd2a43399bc92722c0a7dbd2 = echarts.init(
            document.getElementById('44adbcdfcd2a43399bc92722c0a7dbd2'), 'white', {renderer: 'canvas'});
        var option_44adbcdfcd2a43399bc92722c0a7dbd2 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "line",
            "name": "\u4e92\u4fe1\u606f",
            "connectNulls": false,
            "xAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "trump",
                    3.0269
                ],
                [
                    "empire",
                    4.2937
                ],
                [
                    "global",
                    3.4084
                ],
                [
                    "chinese",
                    2.4275
                ],
                [
                    "power",
                    3.9014
                ],
                [
                    "photo",
                    5.0713
                ],
                [
                    "mid",
                    5.4864
                ],
                [
                    "renminbi",
                    5.4864
                ],
                [
                    "dollar",
                    5.0713
                ],
                [
                    "cent",
                    3.6119
                ],
                [
                    "tariffs",
                    3.1969
                ],
                [
                    "president",
                    2.9559
                ],
                [
                    "retreat",
                    4.6563
                ],
                [
                    "diplomatic",
                    4.3344
                ],
                [
                    "maga",
                    4.6563
                ],
                [
                    "rapidly",
                    5.6563
                ],
                [
                    "time",
                    4.6563
                ],
                [
                    "cross",
                    5.0713
                ],
                [
                    "border",
                    5.0713
                ],
                [
                    "transactions",
                    5.0713
                ],
                [
                    "surplus",
                    5.6563
                ],
                [
                    "economy",
                    4.6563
                ],
                [
                    "surpassed",
                    5.6563
                ],
                [
                    "life",
                    3.849
                ],
                [
                    "prices",
                    4.6563
                ],
                [
                    "popular",
                    6.0713
                ],
                [
                    "social",
                    5.0713
                ],
                [
                    "sale",
                    4.4864
                ],
                [
                    "tiktok",
                    2.4864
                ],
                [
                    "return",
                    3.7494
                ],
                [
                    "threat",
                    4.0713
                ],
                [
                    "international",
                    4.0713
                ],
                [
                    "controls",
                    5.0713
                ],
                [
                    "donald",
                    2.9014
                ],
                [
                    "products",
                    4.0713
                ],
                [
                    "restoration",
                    5.0713
                ],
                [
                    "xi",
                    4.0713
                ],
                [
                    "emerges",
                    5.0713
                ],
                [
                    "major",
                    4.4864
                ],
                [
                    "player",
                    5.0713
                ],
                [
                    "people",
                    4.0713
                ],
                [
                    "contributions",
                    6.0713
                ],
                [
                    "sweeping",
                    6.0713
                ],
                [
                    "reforms",
                    6.0713
                ],
                [
                    "prominence",
                    6.0713
                ],
                [
                    "country",
                    3.7494
                ],
                [
                    "explain",
                    6.0713
                ],
                [
                    "length",
                    6.0713
                ],
                [
                    "galaxy",
                    6.0713
                ],
                [
                    "trade",
                    2.6119
                ],
                [
                    "trading",
                    4.4864
                ],
                [
                    "half",
                    6.0713
                ],
                [
                    "union",
                    4.0713
                ],
                [
                    "produces",
                    5.0713
                ],
                [
                    "japan",
                    5.0713
                ],
                [
                    "germany",
                    4.4864
                ],
                [
                    "south",
                    6.0713
                ],
                [
                    "india",
                    5.0713
                ],
                [
                    "images",
                    5.0713
                ],
                [
                    "expectancy",
                    4.0713
                ],
                [
                    "destined",
                    5.0713
                ],
                [
                    "war",
                    3.264
                ],
                [
                    "thucydides",
                    4.4864
                ],
                [
                    "trap",
                    4.0713
                ],
                [
                    "allison",
                    4.4864
                ],
                [
                    "month",
                    3.4864
                ],
                [
                    "america",
                    2.9014
                ],
                [
                    "predict",
                    6.0713
                ],
                [
                    "doom",
                    6.0713
                ],
                [
                    "sources",
                    5.0713
                ],
                [
                    "american",
                    2.9014
                ],
                [
                    "discussions",
                    5.0713
                ],
                [
                    "washington",
                    2.4864
                ],
                [
                    "strategic",
                    5.0713
                ],
                [
                    "minerals",
                    4.0713
                ],
                [
                    "metal",
                    4.4864
                ],
                [
                    "top",
                    4.0713
                ],
                [
                    "diplomat",
                    4.4864
                ],
                [
                    "pressure",
                    5.0713
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        }
    ],
    "legend": [
        {
            "data": [
                "\u4e92\u4fe1\u606f"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "trump",
                "empire",
                "global",
                "chinese",
                "power",
                "photo",
                "mid",
                "renminbi",
                "dollar",
                "cent",
                "tariffs",
                "president",
                "retreat",
                "diplomatic",
                "maga",
                "rapidly",
                "time",
                "cross",
                "border",
                "transactions",
                "surplus",
                "economy",
                "surpassed",
                "life",
                "prices",
                "popular",
                "social",
                "sale",
                "tiktok",
                "return",
                "threat",
                "international",
                "controls",
                "donald",
                "products",
                "restoration",
                "xi",
                "emerges",
                "major",
                "player",
                "people",
                "contributions",
                "sweeping",
                "reforms",
                "prominence",
                "country",
                "explain",
                "length",
                "galaxy",
                "trade",
                "trading",
                "half",
                "union",
                "produces",
                "japan",
                "germany",
                "south",
                "india",
                "images",
                "expectancy",
                "destined",
                "war",
                "thucydides",
                "trap",
                "allison",
                "month",
                "america",
                "predict",
                "doom",
                "sources",
                "american",
                "discussions",
                "washington",
                "strategic",
                "minerals",
                "metal",
                "top",
                "diplomat",
                "pressure"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u642d\u914d\u5206\u6790 - \u4e92\u4fe1\u606f\u5206\u5e03 (spaCy\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_44adbcdfcd2a43399bc92722c0a7dbd2.setOption(option_44adbcdfcd2a43399bc92722c0a7dbd2);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="747f092063f943a99dda875f77f8d424" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>搭配词</th>
            <th>排名</th>
            <th>左频率</th>
            <th>右频率</th>
            <th>总频率</th>
            <th>范围</th>
            <th>互信息</th>
            <th>似然比</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>trump</td>
            <td>1</td>
            <td>4</td>
            <td>4</td>
            <td>8</td>
            <td>4</td>
            <td>3.0269</td>
            <td>33.5699</td>
        </tr>
        <tr>
            <td>empire</td>
            <td>2</td>
            <td>2</td>
            <td>5</td>
            <td>7</td>
            <td>1</td>
            <td>4.2937</td>
            <td>41.6667</td>
        </tr>
        <tr>
            <td>global</td>
            <td>3</td>
            <td>1</td>
            <td>5</td>
            <td>6</td>
            <td>1</td>
            <td>3.4084</td>
            <td>28.3501</td>
        </tr>
        <tr>
            <td>chinese</td>
            <td>4</td>
            <td>3</td>
            <td>1</td>
            <td>4</td>
            <td>3</td>
            <td>2.4275</td>
            <td>13.4608</td>
        </tr>
        <tr>
            <td>power</td>
            <td>5</td>
            <td>3</td>
            <td>1</td>
            <td>4</td>
            <td>2</td>
            <td>3.9014</td>
            <td>21.6341</td>
        </tr>
        <tr>
            <td>photo</td>
            <td>6</td>
            <td>0</td>
            <td>4</td>
            <td>4</td>
            <td>1</td>
            <td>5.0713</td>
            <td>28.1215</td>
        </tr>
        <tr>
            <td>mid</td>
            <td>7</td>
            <td>3</td>
            <td>1</td>
            <td>4</td>
            <td>1</td>
            <td>5.4864</td>
            <td>30.4230</td>
        </tr>
        <tr>
            <td>renminbi</td>
            <td>8</td>
            <td>3</td>
            <td>1</td>
            <td>4</td>
            <td>1</td>
            <td>5.4864</td>
            <td>30.4230</td>
        </tr>
        <tr>
            <td>dollar</td>
            <td>9</td>
            <td>3</td>
            <td>1</td>
            <td>4</td>
            <td>1</td>
            <td>5.0713</td>
            <td>28.1215</td>
        </tr>
        <tr>
            <td>cent</td>
            <td>10</td>
            <td>2</td>
            <td>2</td>
            <td>4</td>
            <td>2</td>
            <td>3.6119</td>
            <td>20.0287</td>
        </tr>
        <tr>
            <td>tariffs</td>
            <td>11</td>
            <td>1</td>
            <td>2</td>
            <td>3</td>
            <td>2</td>
            <td>3.1969</td>
            <td>13.2954</td>
        </tr>
        <tr>
            <td>president</td>
            <td>12</td>
            <td>1</td>
            <td>2</td>
            <td>3</td>
            <td>3</td>
            <td>2.9559</td>
            <td>12.2931</td>
        </tr>
        <tr>
            <td>retreat</td>
            <td>13</td>
            <td>3</td>
            <td>0</td>
            <td>3</td>
            <td>1</td>
            <td>4.6563</td>
            <td>19.3650</td>
        </tr>
        <tr>
            <td>diplomatic</td>
            <td>14</td>
            <td>1</td>
            <td>2</td>
            <td>3</td>
            <td>2</td>
            <td>4.3344</td>
            <td>18.0262</td>
        </tr>
        <tr>
            <td>maga</td>
            <td>15</td>
            <td>3</td>
            <td>0</td>
            <td>3</td>
            <td>1</td>
            <td>4.6563</td>
            <td>19.3650</td>
        </tr>
        <tr>
            <td>rapidly</td>
            <td>16</td>
            <td>1</td>
            <td>2</td>
            <td>3</td>
            <td>2</td>
            <td>5.6563</td>
            <td>23.5239</td>
        </tr>
        <tr>
            <td>time</td>
            <td>17</td>
            <td>1</td>
            <td>2</td>
            <td>3</td>
            <td>1</td>
            <td>4.6563</td>
            <td>19.3650</td>
        </tr>
        <tr>
            <td>cross</td>
            <td>18</td>
            <td>0</td>
            <td>3</td>
            <td>3</td>
            <td>1</td>
            <td>5.0713</td>
            <td>21.0911</td>
        </tr>
        <tr>
            <td>border</td>
            <td>19</td>
            <td>0</td>
            <td>3</td>
            <td>3</td>
            <td>1</td>
            <td>5.0713</td>
            <td>21.0911</td>
        </tr>
        <tr>
            <td>transactions</td>
            <td>20</td>
            <td>0</td>
            <td>3</td>
            <td>3</td>
            <td>1</td>
            <td>5.0713</td>
            <td>21.0911</td>
        </tr>
        <tr>
            <td>surplus</td>
            <td>21</td>
            <td>2</td>
            <td>1</td>
            <td>3</td>
            <td>1</td>
            <td>5.6563</td>
            <td>23.5239</td>
        </tr>
        <tr>
            <td>economy</td>
            <td>22</td>
            <td>0</td>
            <td>3</td>
            <td>3</td>
            <td>2</td>
            <td>4.6563</td>
            <td>19.3650</td>
        </tr>
        <tr>
            <td>surpassed</td>
            <td>23</td>
            <td>2</td>
            <td>1</td>
            <td>3</td>
            <td>1</td>
            <td>5.6563</td>
            <td>23.5239</td>
        </tr>
        <tr>
            <td>life</td>
            <td>24</td>
            <td>2</td>
            <td>1</td>
            <td>3</td>
            <td>2</td>
            <td>3.8490</td>
            <td>16.0073</td>
        </tr>
        <tr>
            <td>prices</td>
            <td>25</td>
            <td>2</td>
            <td>1</td>
            <td>3</td>
            <td>1</td>
            <td>4.6563</td>
            <td>19.3650</td>
        </tr>
        <tr>
            <td>popular</td>
            <td>26</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>social</td>
            <td>27</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>sale</td>
            <td>28</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.4864</td>
            <td>12.4389</td>
        </tr>
        <tr>
            <td>tiktok</td>
            <td>29</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>2.4864</td>
            <td>6.8937</td>
        </tr>
        <tr>
            <td>return</td>
            <td>30</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>3.7494</td>
            <td>10.3956</td>
        </tr>
        <tr>
            <td>threat</td>
            <td>31</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>2</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>international</td>
            <td>32</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>2</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>controls</td>
            <td>33</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>donald</td>
            <td>34</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>2</td>
            <td>2.9014</td>
            <td>8.0444</td>
        </tr>
        <tr>
            <td>products</td>
            <td>35</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>2</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>restoration</td>
            <td>36</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>xi</td>
            <td>37</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>emerges</td>
            <td>38</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>major</td>
            <td>39</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.4864</td>
            <td>12.4389</td>
        </tr>
        <tr>
            <td>player</td>
            <td>40</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>people</td>
            <td>41</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>contributions</td>
            <td>42</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>sweeping</td>
            <td>43</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>reforms</td>
            <td>44</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>prominence</td>
            <td>45</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>country</td>
            <td>46</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>3.7494</td>
            <td>10.3956</td>
        </tr>
        <tr>
            <td>explain</td>
            <td>47</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>length</td>
            <td>48</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>galaxy</td>
            <td>49</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>trade</td>
            <td>50</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>2</td>
            <td>2.6119</td>
            <td>7.2418</td>
        </tr>
        <tr>
            <td>trading</td>
            <td>51</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.4864</td>
            <td>12.4389</td>
        </tr>
        <tr>
            <td>half</td>
            <td>52</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>union</td>
            <td>53</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>produces</td>
            <td>54</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>japan</td>
            <td>55</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>germany</td>
            <td>56</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>4.4864</td>
            <td>12.4389</td>
        </tr>
        <tr>
            <td>south</td>
            <td>57</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>india</td>
            <td>58</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>images</td>
            <td>59</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>expectancy</td>
            <td>60</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>destined</td>
            <td>61</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>war</td>
            <td>62</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>3.2640</td>
            <td>9.0497</td>
        </tr>
        <tr>
            <td>thucydides</td>
            <td>63</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>4.4864</td>
            <td>12.4389</td>
        </tr>
        <tr>
            <td>trap</td>
            <td>64</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>allison</td>
            <td>65</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>4.4864</td>
            <td>12.4389</td>
        </tr>
        <tr>
            <td>month</td>
            <td>66</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>2</td>
            <td>3.4864</td>
            <td>9.6663</td>
        </tr>
        <tr>
            <td>america</td>
            <td>67</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>2.9014</td>
            <td>8.0444</td>
        </tr>
        <tr>
            <td>predict</td>
            <td>68</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>doom</td>
            <td>69</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>sources</td>
            <td>70</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>american</td>
            <td>71</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>2</td>
            <td>2.9014</td>
            <td>8.0444</td>
        </tr>
        <tr>
            <td>discussions</td>
            <td>72</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>washington</td>
            <td>73</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>2.4864</td>
            <td>6.8937</td>
        </tr>
        <tr>
            <td>strategic</td>
            <td>74</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>minerals</td>
            <td>75</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>metal</td>
            <td>76</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.4864</td>
            <td>12.4389</td>
        </tr>
        <tr>
            <td>top</td>
            <td>77</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>diplomat</td>
            <td>78</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.4864</td>
            <td>12.4389</td>
        </tr>
        <tr>
            <td>pressure</td>
            <td>79</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
    </tbody>
</table>
        </div>

    </div>

    <script>
    </script>
    <script>
        (function() {
            containers = document.getElementsByClassName("chart-container");
            if(containers.length > 0) {
                containers[0].style.display = "block";
            }
        })()

        function showChart(evt, chartID) {
            let containers = document.getElementsByClassName("chart-container");
            for (let i = 0; i < containers.length; i++) {
                containers[i].style.display = "none";
            }

            let tablinks = document.getElementsByClassName("tablinks");
            for (let i = 0; i < tablinks.length; i++) {
                tablinks[i].className = "tablinks";
            }

            document.getElementById(chartID).style.display = "block";
            evt.currentTarget.className += " active";
        }
    </script>
</body>
</html>
