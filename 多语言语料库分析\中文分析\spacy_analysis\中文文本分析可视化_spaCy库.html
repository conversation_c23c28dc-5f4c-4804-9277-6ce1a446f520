<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts-wordcloud.min.js"></script>

    
</head>
<body >
            <style>
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
        }

        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 12px 16px;
            transition: 0.3s;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #ccc;
        }

        .chart-container {
            display: block;
        }

        .chart-container:nth-child(n+2) {
            display: none;
        }
    </style>
    <div class="tab">
            <button class="tablinks" onclick="showChart(event, '344d408f54ec4c39a0140483d025fbc9')">首页</button>
            <button class="tablinks" onclick="showChart(event, '1d4f79b343524cdeb313b7c429f11a10')">词频分析-词云图</button>
            <button class="tablinks" onclick="showChart(event, 'a98f25cab3f84670926e69a542cb77e3')">词频分析-柱状图</button>
            <button class="tablinks" onclick="showChart(event, 'b7765b00516246ba9a07b69751828b7f')">KWIC分析</button>
            <button class="tablinks" onclick="showChart(event, 'a1aec2b68c25492bb70b5e6143457efb')">词语分布-频率图</button>
            <button class="tablinks" onclick="showChart(event, 'a9315ebca287413bbccdd7bd8c1d8217')">词语分布-离散度图</button>
            <button class="tablinks" onclick="showChart(event, '5c5915b6c023484e9f08adcf1ff6d800')">词语分布-详细数据</button>
            <button class="tablinks" onclick="showChart(event, '8fbf96e49eb54e38b13172d9a9daa2ef')">词簇分析-频率图</button>
            <button class="tablinks" onclick="showChart(event, '76e4288fe1a5434097953a18b89ac4e9')">词簇分析-详细数据</button>
            <button class="tablinks" onclick="showChart(event, '59f7f1855ef9454aa4d6c738a1b401ac')">搭配分析-频率图</button>
            <button class="tablinks" onclick="showChart(event, '74cbd3657c5e43aeb3a38e0ee5892dff')">搭配分析-互信息图</button>
            <button class="tablinks" onclick="showChart(event, '3cfbcaa101484bc3909747f8f8e9b531')">搭配分析-详细数据</button>
    </div>

    <div class="box">
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="344d408f54ec4c39a0140483d025fbc9" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>分析类型</th>
            <th>说明 (spaCy库实现)</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>词频分析</td>
            <td>使用spaCy库进行中文分词和词性标注</td>
        </tr>
        <tr>
            <td>KWIC分析</td>
            <td>关键词上下文索引，基于spaCy神经网络分词</td>
        </tr>
        <tr>
            <td>词语分布分析</td>
            <td>分析词语在不同文件中的分布情况</td>
        </tr>
        <tr>
            <td>词簇分析</td>
            <td>分析词语簇的分布和频率</td>
        </tr>
        <tr>
            <td>搭配分析</td>
            <td>分析词语的常见搭配，计算互信息和似然比</td>
        </tr>
        <tr>
            <td>技术特点</td>
            <td>基于神经网络的现代NLP库，支持词性标注</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="1d4f79b343524cdeb313b7c429f11a10" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('1d4f79b343524cdeb313b7c429f11a10').style.width = document.getElementById('1d4f79b343524cdeb313b7c429f11a10').parentNode.clientWidth + 'px';
        var chart_1d4f79b343524cdeb313b7c429f11a10 = echarts.init(
            document.getElementById('1d4f79b343524cdeb313b7c429f11a10'), 'white', {renderer: 'canvas'});
        var option_1d4f79b343524cdeb313b7c429f11a10 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "wordCloud",
            "shape": "circle",
            "rotationRange": [
                -90,
                90
            ],
            "rotationStep": 45,
            "girdSize": 20,
            "sizeRange": [
                15,
                80
            ],
            "data": [
                {
                    "name": "\u6570\u636e",
                    "value": 3100,
                    "textStyle": {
                        "color": "rgb(143,84,116)"
                    }
                },
                {
                    "name": "\u667a\u80fd",
                    "value": 2818,
                    "textStyle": {
                        "color": "rgb(99,128,10)"
                    }
                },
                {
                    "name": "\u4eba\u5de5",
                    "value": 1800,
                    "textStyle": {
                        "color": "rgb(142,41,129)"
                    }
                },
                {
                    "name": "\u6280\u672f",
                    "value": 1536,
                    "textStyle": {
                        "color": "rgb(108,118,59)"
                    }
                },
                {
                    "name": "\u5e94\u7528",
                    "value": 1499,
                    "textStyle": {
                        "color": "rgb(2,156,144)"
                    }
                },
                {
                    "name": "\u4fe1\u606f",
                    "value": 1458,
                    "textStyle": {
                        "color": "rgb(75,133,37)"
                    }
                },
                {
                    "name": "\u53d1\u5c55",
                    "value": 1242,
                    "textStyle": {
                        "color": "rgb(127,33,67)"
                    }
                },
                {
                    "name": "\u5e73\u53f0",
                    "value": 1224,
                    "textStyle": {
                        "color": "rgb(41,122,115)"
                    }
                },
                {
                    "name": "\u5b89\u5168",
                    "value": 1146,
                    "textStyle": {
                        "color": "rgb(92,92,77)"
                    }
                },
                {
                    "name": "\u670d\u52a1",
                    "value": 1143,
                    "textStyle": {
                        "color": "rgb(104,154,159)"
                    }
                },
                {
                    "name": "\u4e2d\u56fd",
                    "value": 1124,
                    "textStyle": {
                        "color": "rgb(137,36,96)"
                    }
                },
                {
                    "name": "\u7814\u7a76",
                    "value": 1090,
                    "textStyle": {
                        "color": "rgb(100,97,114)"
                    }
                },
                {
                    "name": "\u8ba1\u7b97",
                    "value": 1051,
                    "textStyle": {
                        "color": "rgb(38,132,112)"
                    }
                },
                {
                    "name": "\u901a\u4fe1",
                    "value": 913,
                    "textStyle": {
                        "color": "rgb(157,22,143)"
                    }
                },
                {
                    "name": "\u4f01\u4e1a",
                    "value": 891,
                    "textStyle": {
                        "color": "rgb(145,45,66)"
                    }
                },
                {
                    "name": "\u5927\u6570",
                    "value": 871,
                    "textStyle": {
                        "color": "rgb(159,88,14)"
                    }
                },
                {
                    "name": "\u4ea7\u4e1a",
                    "value": 857,
                    "textStyle": {
                        "color": "rgb(61,154,101)"
                    }
                },
                {
                    "name": "\u4e92\u8054",
                    "value": 836,
                    "textStyle": {
                        "color": "rgb(66,5,69)"
                    }
                },
                {
                    "name": "\u4e91\u8ba1",
                    "value": 754,
                    "textStyle": {
                        "color": "rgb(143,121,106)"
                    }
                },
                {
                    "name": "\u7ba1\u7406",
                    "value": 749,
                    "textStyle": {
                        "color": "rgb(97,51,158)"
                    }
                },
                {
                    "name": "\u5de5\u667a",
                    "value": 737,
                    "textStyle": {
                        "color": "rgb(43,48,120)"
                    }
                },
                {
                    "name": "\u5de5\u4e1a",
                    "value": 701,
                    "textStyle": {
                        "color": "rgb(18,155,133)"
                    }
                },
                {
                    "name": "\u7f51\u7edc",
                    "value": 646,
                    "textStyle": {
                        "color": "rgb(156,25,20)"
                    }
                },
                {
                    "name": "\u5b9e\u73b0",
                    "value": 644,
                    "textStyle": {
                        "color": "rgb(25,11,125)"
                    }
                },
                {
                    "name": "\u7cfb\u7edf",
                    "value": 621,
                    "textStyle": {
                        "color": "rgb(22,133,112)"
                    }
                },
                {
                    "name": "\u80fd\u529b",
                    "value": 507,
                    "textStyle": {
                        "color": "rgb(112,118,80)"
                    }
                },
                {
                    "name": "\u76ae\u4e66",
                    "value": 506,
                    "textStyle": {
                        "color": "rgb(86,90,50)"
                    }
                },
                {
                    "name": "\u5e02\u573a",
                    "value": 498,
                    "textStyle": {
                        "color": "rgb(159,50,72)"
                    }
                },
                {
                    "name": "\u4ea7\u54c1",
                    "value": 494,
                    "textStyle": {
                        "color": "rgb(155,102,21)"
                    }
                },
                {
                    "name": "\u63d0\u5347",
                    "value": 468,
                    "textStyle": {
                        "color": "rgb(157,5,14)"
                    }
                },
                {
                    "name": "\u8d44\u6e90",
                    "value": 464,
                    "textStyle": {
                        "color": "rgb(37,49,107)"
                    }
                },
                {
                    "name": "\u79fb\u52a8",
                    "value": 456,
                    "textStyle": {
                        "color": "rgb(64,43,27)"
                    }
                },
                {
                    "name": "\u653f\u5e9c",
                    "value": 456,
                    "textStyle": {
                        "color": "rgb(152,6,91)"
                    }
                },
                {
                    "name": "\u57fa\u7840",
                    "value": 452,
                    "textStyle": {
                        "color": "rgb(69,65,20)"
                    }
                },
                {
                    "name": "\u6846\u67b6",
                    "value": 450,
                    "textStyle": {
                        "color": "rgb(131,154,98)"
                    }
                },
                {
                    "name": "\u4e1a\u52a1",
                    "value": 450,
                    "textStyle": {
                        "color": "rgb(149,105,101)"
                    }
                },
                {
                    "name": "\u6211\u56fd",
                    "value": 449,
                    "textStyle": {
                        "color": "rgb(79,62,126)"
                    }
                },
                {
                    "name": "\u5206\u6790",
                    "value": 446,
                    "textStyle": {
                        "color": "rgb(57,123,137)"
                    }
                },
                {
                    "name": "\u63d0\u4f9b",
                    "value": 425,
                    "textStyle": {
                        "color": "rgb(54,12,110)"
                    }
                },
                {
                    "name": "\u884c\u4e1a",
                    "value": 425,
                    "textStyle": {
                        "color": "rgb(111,83,16)"
                    }
                },
                {
                    "name": "\u9886\u57df",
                    "value": 420,
                    "textStyle": {
                        "color": "rgb(16,16,49)"
                    }
                },
                {
                    "name": "\u8fdb\u884c",
                    "value": 419,
                    "textStyle": {
                        "color": "rgb(55,32,0)"
                    }
                },
                {
                    "name": "\u521b\u65b0",
                    "value": 404,
                    "textStyle": {
                        "color": "rgb(66,17,109)"
                    }
                },
                {
                    "name": "\u7ecf\u6d4e",
                    "value": 392,
                    "textStyle": {
                        "color": "rgb(95,85,71)"
                    }
                },
                {
                    "name": "\u4e3b\u8981",
                    "value": 387,
                    "textStyle": {
                        "color": "rgb(62,65,83)"
                    }
                },
                {
                    "name": "\u7528\u6237",
                    "value": 380,
                    "textStyle": {
                        "color": "rgb(16,113,118)"
                    }
                },
                {
                    "name": "\u5f00\u53d1",
                    "value": 374,
                    "textStyle": {
                        "color": "rgb(13,91,92)"
                    }
                },
                {
                    "name": "\u6210\u4e3a",
                    "value": 359,
                    "textStyle": {
                        "color": "rgb(40,34,16)"
                    }
                },
                {
                    "name": "\u65b9\u9762",
                    "value": 351,
                    "textStyle": {
                        "color": "rgb(74,55,37)"
                    }
                },
                {
                    "name": "\u4e91\u539f",
                    "value": 350,
                    "textStyle": {
                        "color": "rgb(94,77,79)"
                    }
                },
                {
                    "name": "\u82af\u7247",
                    "value": 342,
                    "textStyle": {
                        "color": "rgb(62,95,27)"
                    }
                },
                {
                    "name": "\u6570\u5b57",
                    "value": 337,
                    "textStyle": {
                        "color": "rgb(4,0,109)"
                    }
                },
                {
                    "name": "\u5168\u7403",
                    "value": 335,
                    "textStyle": {
                        "color": "rgb(138,87,69)"
                    }
                },
                {
                    "name": "\u7684\u6570",
                    "value": 331,
                    "textStyle": {
                        "color": "rgb(147,36,100)"
                    }
                },
                {
                    "name": "\u76ee\u524d",
                    "value": 331,
                    "textStyle": {
                        "color": "rgb(152,109,85)"
                    }
                },
                {
                    "name": "\u534f\u540c",
                    "value": 328,
                    "textStyle": {
                        "color": "rgb(143,105,129)"
                    }
                },
                {
                    "name": "\u8054\u7f51",
                    "value": 323,
                    "textStyle": {
                        "color": "rgb(145,115,49)"
                    }
                },
                {
                    "name": "\u8bbe\u5907",
                    "value": 320,
                    "textStyle": {
                        "color": "rgb(39,26,59)"
                    }
                },
                {
                    "name": "\u4f20\u7edf",
                    "value": 320,
                    "textStyle": {
                        "color": "rgb(25,125,69)"
                    }
                },
                {
                    "name": "\u5904\u7406",
                    "value": 315,
                    "textStyle": {
                        "color": "rgb(28,147,147)"
                    }
                },
                {
                    "name": "\u67b6\u6784",
                    "value": 307,
                    "textStyle": {
                        "color": "rgb(89,106,38)"
                    }
                },
                {
                    "name": "\u56fd\u5bb6",
                    "value": 302,
                    "textStyle": {
                        "color": "rgb(9,73,89)"
                    }
                },
                {
                    "name": "\u6807\u51c6",
                    "value": 300,
                    "textStyle": {
                        "color": "rgb(80,89,83)"
                    }
                },
                {
                    "name": "\u8f6f\u4ef6",
                    "value": 300,
                    "textStyle": {
                        "color": "rgb(59,158,5)"
                    }
                },
                {
                    "name": "\u89c4\u6a21",
                    "value": 295,
                    "textStyle": {
                        "color": "rgb(42,36,158)"
                    }
                },
                {
                    "name": "\u6a21\u578b",
                    "value": 293,
                    "textStyle": {
                        "color": "rgb(128,64,135)"
                    }
                },
                {
                    "name": "\u5b66\u4e60",
                    "value": 289,
                    "textStyle": {
                        "color": "rgb(49,148,133)"
                    }
                },
                {
                    "name": "\u786c\u4ef6",
                    "value": 288,
                    "textStyle": {
                        "color": "rgb(37,76,22)"
                    }
                },
                {
                    "name": "\u4f7f\u7528",
                    "value": 285,
                    "textStyle": {
                        "color": "rgb(142,138,60)"
                    }
                },
                {
                    "name": "\u8fb9\u7f18",
                    "value": 284,
                    "textStyle": {
                        "color": "rgb(152,3,12)"
                    }
                },
                {
                    "name": "\u7ec8\u7aef",
                    "value": 284,
                    "textStyle": {
                        "color": "rgb(21,156,64)"
                    }
                },
                {
                    "name": "\u9700\u6c42",
                    "value": 283,
                    "textStyle": {
                        "color": "rgb(129,145,148)"
                    }
                },
                {
                    "name": "\u63a8\u52a8",
                    "value": 280,
                    "textStyle": {
                        "color": "rgb(35,122,111)"
                    }
                },
                {
                    "name": "\u95ee\u9898",
                    "value": 276,
                    "textStyle": {
                        "color": "rgb(82,139,57)"
                    }
                },
                {
                    "name": "\u4f53\u7cfb",
                    "value": 274,
                    "textStyle": {
                        "color": "rgb(130,118,37)"
                    }
                },
                {
                    "name": "\u5229\u7528",
                    "value": 273,
                    "textStyle": {
                        "color": "rgb(82,21,18)"
                    }
                },
                {
                    "name": "\u6df1\u5ea6",
                    "value": 271,
                    "textStyle": {
                        "color": "rgb(62,77,10)"
                    }
                },
                {
                    "name": "\u9700\u8981",
                    "value": 271,
                    "textStyle": {
                        "color": "rgb(136,98,21)"
                    }
                },
                {
                    "name": "\u6218\u7565",
                    "value": 271,
                    "textStyle": {
                        "color": "rgb(115,10,85)"
                    }
                },
                {
                    "name": "\u767d\u76ae",
                    "value": 270,
                    "textStyle": {
                        "color": "rgb(145,116,22)"
                    }
                },
                {
                    "name": "\u751f\u4ea7",
                    "value": 270,
                    "textStyle": {
                        "color": "rgb(143,106,35)"
                    }
                },
                {
                    "name": "\u673a\u5668",
                    "value": 264,
                    "textStyle": {
                        "color": "rgb(68,151,131)"
                    }
                },
                {
                    "name": "\u76f8\u5173",
                    "value": 262,
                    "textStyle": {
                        "color": "rgb(19,99,106)"
                    }
                },
                {
                    "name": "\u7b97\u6cd5",
                    "value": 261,
                    "textStyle": {
                        "color": "rgb(128,101,22)"
                    }
                },
                {
                    "name": "\u8bc6\u522b",
                    "value": 259,
                    "textStyle": {
                        "color": "rgb(72,67,67)"
                    }
                },
                {
                    "name": "\u573a\u666f",
                    "value": 253,
                    "textStyle": {
                        "color": "rgb(62,121,125)"
                    }
                },
                {
                    "name": "\u6a21\u5f0f",
                    "value": 250,
                    "textStyle": {
                        "color": "rgb(36,113,155)"
                    }
                },
                {
                    "name": "\u5f00\u653e",
                    "value": 242,
                    "textStyle": {
                        "color": "rgb(154,61,105)"
                    }
                },
                {
                    "name": "\u751f\u6001",
                    "value": 241,
                    "textStyle": {
                        "color": "rgb(43,21,78)"
                    }
                },
                {
                    "name": "\u652f\u6301",
                    "value": 239,
                    "textStyle": {
                        "color": "rgb(56,146,84)"
                    }
                },
                {
                    "name": "\u5f62\u6210",
                    "value": 232,
                    "textStyle": {
                        "color": "rgb(88,130,19)"
                    }
                },
                {
                    "name": "\u81ea\u52a8",
                    "value": 231,
                    "textStyle": {
                        "color": "rgb(31,83,0)"
                    }
                },
                {
                    "name": "\u7f8e\u56fd",
                    "value": 231,
                    "textStyle": {
                        "color": "rgb(109,5,147)"
                    }
                },
                {
                    "name": "\u4e0d\u65ad",
                    "value": 230,
                    "textStyle": {
                        "color": "rgb(36,29,69)"
                    }
                },
                {
                    "name": "\u6cbb\u7406",
                    "value": 229,
                    "textStyle": {
                        "color": "rgb(104,131,138)"
                    }
                },
                {
                    "name": "\u6784\u5efa",
                    "value": 226,
                    "textStyle": {
                        "color": "rgb(25,118,37)"
                    }
                },
                {
                    "name": "\u4fdd\u62a4",
                    "value": 226,
                    "textStyle": {
                        "color": "rgb(108,25,147)"
                    }
                },
                {
                    "name": "\u4e8c\u662f",
                    "value": 222,
                    "textStyle": {
                        "color": "rgb(80,104,131)"
                    }
                },
                {
                    "name": "\u6838\u5fc3",
                    "value": 220,
                    "textStyle": {
                        "color": "rgb(82,131,18)"
                    }
                },
                {
                    "name": "\u4f18\u5316",
                    "value": 220,
                    "textStyle": {
                        "color": "rgb(59,149,84)"
                    }
                }
            ],
            "drawOutOfBound": false,
            "textStyle": {
                "normal": {
                    "fontFamily": "Microsoft YaHei"
                },
                "emphasis": {}
            }
        }
    ],
    "legend": [
        {
            "data": [],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u4e2d\u6587\u8bcd\u9891\u5206\u6790 - \u8bcd\u4e91\u56fe (spaCy\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_1d4f79b343524cdeb313b7c429f11a10.setOption(option_1d4f79b343524cdeb313b7c429f11a10);
    </script>
                <div id="a98f25cab3f84670926e69a542cb77e3" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('a98f25cab3f84670926e69a542cb77e3').style.width = document.getElementById('a98f25cab3f84670926e69a542cb77e3').parentNode.clientWidth + 'px';
        var chart_a98f25cab3f84670926e69a542cb77e3 = echarts.init(
            document.getElementById('a98f25cab3f84670926e69a542cb77e3'), 'white', {renderer: 'canvas'});
        var option_a98f25cab3f84670926e69a542cb77e3 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u8bcd\u9891",
            "legendHoverLink": true,
            "data": [
                3100,
                2818,
                1800,
                1536,
                1499,
                1458,
                1242,
                1224,
                1146,
                1143,
                1124,
                1090,
                1051,
                913,
                891,
                871,
                857,
                836,
                754,
                749
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u8bcd\u9891"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "\u6570\u636e",
                "\u667a\u80fd",
                "\u4eba\u5de5",
                "\u6280\u672f",
                "\u5e94\u7528",
                "\u4fe1\u606f",
                "\u53d1\u5c55",
                "\u5e73\u53f0",
                "\u5b89\u5168",
                "\u670d\u52a1",
                "\u4e2d\u56fd",
                "\u7814\u7a76",
                "\u8ba1\u7b97",
                "\u901a\u4fe1",
                "\u4f01\u4e1a",
                "\u5927\u6570",
                "\u4ea7\u4e1a",
                "\u4e92\u8054",
                "\u4e91\u8ba1",
                "\u7ba1\u7406"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u4e2d\u6587\u8bcd\u9891\u5206\u6790 - \u524d20\u9ad8\u9891\u8bcd (spaCy\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_a98f25cab3f84670926e69a542cb77e3.setOption(option_a98f25cab3f84670926e69a542cb77e3);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="b7765b00516246ba9a07b69751828b7f" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>左上下文</th>
            <th>关键词</th>
            <th>右上下文</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>框架 发展 白皮 书 中国 信息 通信 研究院</td>
            <td>人工智能</td>
            <td>框架 是 支撑 人工 智能 应用 开发</td>
        </tr>
        <tr>
            <td>支撑 人工 智能 应用 开发 的 重要</td>
            <td>人工智能</td>
            <td>基础 软件 平台 为 人工 智能</td>
        </tr>
        <tr>
            <td>基础 软件 平台 为 人工 智能</td>
            <td>人工智能</td>
            <td>算法 模型 的 训练 和 推理</td>
        </tr>
        <tr>
            <td>安全 白皮 书 中国 信息 通信</td>
            <td>人工智能</td>
            <td>研究院 安全 研究所 年 月</td>
        </tr>
        <tr>
            <td>研究院 安全 研究所 年 月</td>
            <td>人工智能</td>
            <td>安全 白皮 书 目录 一</td>
        </tr>
        <tr>
            <td>安全 白皮 书 目录 一</td>
            <td>人工智能</td>
            <td>发展 现状 二 人工 智能</td>
        </tr>
        <tr>
            <td>发展 现状 二 人工 智能</td>
            <td>人工智能</td>
            <td>安全 风险 分析 三 人工</td>
        </tr>
        <tr>
            <td>安全 风险 分析 三 人工</td>
            <td>人工智能</td>
            <td>安全 治理 思路 四 人工</td>
        </tr>
        <tr>
            <td>安全 治理 思路 四 人工</td>
            <td>人工智能</td>
            <td>安全 发展 建议 一 人工</td>
        </tr>
        <tr>
            <td>安全 发展 建议 一 人工</td>
            <td>人工智能</td>
            <td>发展 现状 人工 智能</td>
        </tr>
        <tr>
            <td>发展 现状 人工 智能</td>
            <td>人工智能</td>
            <td>是 引领 未来 的 战略性</td>
        </tr>
        <tr>
            <td>中国 信息 通信 研究院 中国</td>
            <td>人工智能</td>
            <td>产业 发展 联盟 年 月</td>
        </tr>
        <tr>
            <td>产业 发展 联盟 年 月</td>
            <td>人工智能</td>
            <td>发展 白皮 书 产业 应用</td>
        </tr>
        <tr>
            <td>发展 白皮 书 产业 应用</td>
            <td>人工智能</td>
            <td>发展 白皮 书 产业 应用</td>
        </tr>
        <tr>
            <td>发展 白皮 书 产业 应用</td>
            <td>人工智能</td>
            <td>产业 发展 联盟 编制 说明</td>
        </tr>
        <tr>
            <td>产业 发展 联盟 年 月</td>
            <td>人工智能</td>
            <td>发展 白皮 书 技术 架构</td>
        </tr>
        <tr>
            <td>发展 白皮 书 技术 架构</td>
            <td>人工智能</td>
            <td>发展 白皮 书 技术 架构</td>
        </tr>
        <tr>
            <td>发展 白皮 书 技术 架构</td>
            <td>人工智能</td>
            <td>产业 发展 联盟 编制 说明</td>
        </tr>
        <tr>
            <td>数据 安全 白皮 书 中国</td>
            <td>人工智能</td>
            <td>数据 安全 白皮 书 中国</td>
        </tr>
        <tr>
            <td>数据 安全 白皮 书 中国</td>
            <td>人工智能</td>
            <td>数据 安全 白皮 书 目录</td>
        </tr>
        <tr>
            <td>全球 人工 智能 战略 与</td>
            <td>人工智能</td>
            <td>战略 与 政策 观察 中国</td>
        </tr>
        <tr>
            <td>战略 与 政策 观察 中国</td>
            <td>人工智能</td>
            <td>产业 发展 联盟 年 月</td>
        </tr>
        <tr>
            <td>产业 发展 联盟 年 月</td>
            <td>人工智能</td>
            <td>战略 与 政策 观察 全球</td>
        </tr>
        <tr>
            <td>战略 与 政策 观察 全球</td>
            <td>人工智能</td>
            <td>战略 与 政策 观察 目录</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="a1aec2b68c25492bb70b5e6143457efb" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('a1aec2b68c25492bb70b5e6143457efb').style.width = document.getElementById('a1aec2b68c25492bb70b5e6143457efb').parentNode.clientWidth + 'px';
        var chart_a1aec2b68c25492bb70b5e6143457efb = echarts.init(
            document.getElementById('a1aec2b68c25492bb70b5e6143457efb'), 'white', {renderer: 'canvas'});
        var option_a1aec2b68c25492bb70b5e6143457efb = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u8bcd\u9891",
            "legendHoverLink": true,
            "data": [
                42,
                38,
                58,
                76,
                18,
                65,
                89,
                112,
                48,
                72,
                58,
                67,
                456,
                92,
                59,
                28,
                567,
                498,
                534,
                65,
                189,
                43,
                48
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u8bcd\u9891"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "AI\u6846\u67b6\u53d1\u5c55\u767d\u76ae\u4e66.txt",
                "5G\u4e91AI\u6570\u5b57\u7ecf\u6d4e\u65b0\u65f6\u4ee3\u7684\u5f15\u64ce.txt",
                "\u4e91\u8ba1\u7b97\u767d\u76ae\u4e662016\u5e74.txt",
                "\u4e91\u539f\u751f\u53d1\u5c55\u767d\u76ae\u4e662020\u5e74.txt",
                "\u4e91\u539f\u751f\u6280\u672f\u5b9e\u8df5\u767d\u76ae\u4e662019\u5e74.txt",
                "\u4e91\u8ba1\u7b97\u4e0e\u8fb9\u7f18\u8ba1\u7b97\u534f\u540c\u4e5d\u5927\u5e94\u7528\u573a\u666f.txt",
                "\u4e91\u8ba1\u7b97\u53d1\u5c55\u767d\u76ae\u4e662018\u5e74.txt",
                "\u4e91\u8ba1\u7b97\u53d1\u5c55\u767d\u76ae\u4e662020\u5e74.txt",
                "\u4e92\u8054\u7f51\u5e73\u53f0\u6cbb\u7406\u7814\u7a76\u62a5\u544a2019\u5e74.txt",
                "\u4eba\u5de5\u667a\u80fd\u5b89\u5168\u767d\u76ae\u4e662018\u5e74.txt",
                "\u4eba\u5de5\u667a\u80fd\u53d1\u5c55\u767d\u76ae\u4e66\u4ea7\u4e1a\u5e94\u7528\u7bc7.txt",
                "\u4eba\u5de5\u667a\u80fd\u53d1\u5c55\u767d\u76ae\u4e66\u6280\u672f\u67b6\u6784\u7bc7.txt",
                "\u4eba\u5de5\u667a\u80fd\u6570\u636e\u5b89\u5168\u767d\u76ae\u4e66.txt",
                "\u5148\u8fdb\u8ba1\u7b97\u53d1\u5c55\u7814\u7a76\u62a5\u544a2018\u5e74.txt",
                "\u5168\u7403\u4eba\u5de5\u667a\u80fd\u6218\u7565\u4e0e\u653f\u7b56\u89c2\u5bdf.txt",
                "\u57fa\u4e8eAI\u7684\u667a\u80fd\u5207\u7247\u7ba1\u7406\u4e0e\u534f\u540c.txt",
                "\u5927\u6570\u636e\u767d\u76ae\u4e662018\u5e74.txt",
                "\u5927\u6570\u636e\u767d\u76ae\u4e662019\u5e74.txt",
                "\u5927\u6570\u636e\u767d\u76ae\u4e66.txt",
                "\u5de5\u4e1a\u4e92\u8054\u7f51\u4ea7\u4e1a\u7ecf\u6d4e\u53d1\u5c55\u62a5\u544a.txt",
                "\u5de5\u4e1a\u4e92\u8054\u7f51\u5e73\u53f0\u767d\u76ae\u4e662019\u5e74.txt",
                "\u79fb\u52a8\u4e92\u8054\u7f51\u767d\u76ae\u4e662015\u5e74.txt",
                "\u79fb\u52a8\u667a\u80fd\u7ec8\u7aef\u66a8\u667a\u80fd\u786c\u4ef6\u767d\u76ae\u4e66.txt"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u4e2d\u6587\u8bcd\u8bed\u5206\u5e03\u5206\u6790 - \u6587\u4ef6\u9891\u7387\u5206\u5e03 (spaCy\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_a1aec2b68c25492bb70b5e6143457efb.setOption(option_a1aec2b68c25492bb70b5e6143457efb);
    </script>
                <div id="a9315ebca287413bbccdd7bd8c1d8217" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('a9315ebca287413bbccdd7bd8c1d8217').style.width = document.getElementById('a9315ebca287413bbccdd7bd8c1d8217').parentNode.clientWidth + 'px';
        var chart_a9315ebca287413bbccdd7bd8c1d8217 = echarts.init(
            document.getElementById('a9315ebca287413bbccdd7bd8c1d8217'), 'white', {renderer: 'canvas'});
        var option_a9315ebca287413bbccdd7bd8c1d8217 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "line",
            "name": "\u79bb\u6563\u5ea6",
            "connectNulls": false,
            "xAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "AI\u6846\u67b6\u53d1\u5c55\u767d\u76ae\u4e66.txt",
                    0.9567
                ],
                [
                    "5G\u4e91AI\u6570\u5b57\u7ecf\u6d4e\u65b0\u65f6\u4ee3\u7684\u5f15\u64ce.txt",
                    0.9634
                ],
                [
                    "\u4e91\u8ba1\u7b97\u767d\u76ae\u4e662016\u5e74.txt",
                    0.9298
                ],
                [
                    "\u4e91\u539f\u751f\u53d1\u5c55\u767d\u76ae\u4e662020\u5e74.txt",
                    0.8934
                ],
                [
                    "\u4e91\u539f\u751f\u6280\u672f\u5b9e\u8df5\u767d\u76ae\u4e662019\u5e74.txt",
                    0.9456
                ],
                [
                    "\u4e91\u8ba1\u7b97\u4e0e\u8fb9\u7f18\u8ba1\u7b97\u534f\u540c\u4e5d\u5927\u5e94\u7528\u573a\u666f.txt",
                    0.9187
                ],
                [
                    "\u4e91\u8ba1\u7b97\u53d1\u5c55\u767d\u76ae\u4e662018\u5e74.txt",
                    0.9345
                ],
                [
                    "\u4e91\u8ba1\u7b97\u53d1\u5c55\u767d\u76ae\u4e662020\u5e74.txt",
                    0.9578
                ],
                [
                    "\u4e92\u8054\u7f51\u5e73\u53f0\u6cbb\u7406\u7814\u7a76\u62a5\u544a2019\u5e74.txt",
                    0.8156
                ],
                [
                    "\u4eba\u5de5\u667a\u80fd\u5b89\u5168\u767d\u76ae\u4e662018\u5e74.txt",
                    0.9645
                ],
                [
                    "\u4eba\u5de5\u667a\u80fd\u53d1\u5c55\u767d\u76ae\u4e66\u4ea7\u4e1a\u5e94\u7528\u7bc7.txt",
                    0.9067
                ],
                [
                    "\u4eba\u5de5\u667a\u80fd\u53d1\u5c55\u767d\u76ae\u4e66\u6280\u672f\u67b6\u6784\u7bc7.txt",
                    0.8856
                ],
                [
                    "\u4eba\u5de5\u667a\u80fd\u6570\u636e\u5b89\u5168\u767d\u76ae\u4e66.txt",
                    0.9889
                ],
                [
                    "\u5148\u8fdb\u8ba1\u7b97\u53d1\u5c55\u7814\u7a76\u62a5\u544a2018\u5e74.txt",
                    0.9723
                ],
                [
                    "\u5168\u7403\u4eba\u5de5\u667a\u80fd\u6218\u7565\u4e0e\u653f\u7b56\u89c2\u5bdf.txt",
                    0.8198
                ],
                [
                    "\u57fa\u4e8eAI\u7684\u667a\u80fd\u5207\u7247\u7ba1\u7406\u4e0e\u534f\u540c.txt",
                    0.9067
                ],
                [
                    "\u5927\u6570\u636e\u767d\u76ae\u4e662018\u5e74.txt",
                    0.9887
                ],
                [
                    "\u5927\u6570\u636e\u767d\u76ae\u4e662019\u5e74.txt",
                    0.9856
                ],
                [
                    "\u5927\u6570\u636e\u767d\u76ae\u4e66.txt",
                    0.9901
                ],
                [
                    "\u5de5\u4e1a\u4e92\u8054\u7f51\u4ea7\u4e1a\u7ecf\u6d4e\u53d1\u5c55\u62a5\u544a.txt",
                    0.9423
                ],
                [
                    "\u5de5\u4e1a\u4e92\u8054\u7f51\u5e73\u53f0\u767d\u76ae\u4e662019\u5e74.txt",
                    0.9498
                ],
                [
                    "\u79fb\u52a8\u4e92\u8054\u7f51\u767d\u76ae\u4e662015\u5e74.txt",
                    0.9612
                ],
                [
                    "\u79fb\u52a8\u667a\u80fd\u7ec8\u7aef\u66a8\u667a\u80fd\u786c\u4ef6\u767d\u76ae\u4e66.txt",
                    0.9756
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        }
    ],
    "legend": [
        {
            "data": [
                "\u79bb\u6563\u5ea6"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "AI\u6846\u67b6\u53d1\u5c55\u767d\u76ae\u4e66.txt",
                "5G\u4e91AI\u6570\u5b57\u7ecf\u6d4e\u65b0\u65f6\u4ee3\u7684\u5f15\u64ce.txt",
                "\u4e91\u8ba1\u7b97\u767d\u76ae\u4e662016\u5e74.txt",
                "\u4e91\u539f\u751f\u53d1\u5c55\u767d\u76ae\u4e662020\u5e74.txt",
                "\u4e91\u539f\u751f\u6280\u672f\u5b9e\u8df5\u767d\u76ae\u4e662019\u5e74.txt",
                "\u4e91\u8ba1\u7b97\u4e0e\u8fb9\u7f18\u8ba1\u7b97\u534f\u540c\u4e5d\u5927\u5e94\u7528\u573a\u666f.txt",
                "\u4e91\u8ba1\u7b97\u53d1\u5c55\u767d\u76ae\u4e662018\u5e74.txt",
                "\u4e91\u8ba1\u7b97\u53d1\u5c55\u767d\u76ae\u4e662020\u5e74.txt",
                "\u4e92\u8054\u7f51\u5e73\u53f0\u6cbb\u7406\u7814\u7a76\u62a5\u544a2019\u5e74.txt",
                "\u4eba\u5de5\u667a\u80fd\u5b89\u5168\u767d\u76ae\u4e662018\u5e74.txt",
                "\u4eba\u5de5\u667a\u80fd\u53d1\u5c55\u767d\u76ae\u4e66\u4ea7\u4e1a\u5e94\u7528\u7bc7.txt",
                "\u4eba\u5de5\u667a\u80fd\u53d1\u5c55\u767d\u76ae\u4e66\u6280\u672f\u67b6\u6784\u7bc7.txt",
                "\u4eba\u5de5\u667a\u80fd\u6570\u636e\u5b89\u5168\u767d\u76ae\u4e66.txt",
                "\u5148\u8fdb\u8ba1\u7b97\u53d1\u5c55\u7814\u7a76\u62a5\u544a2018\u5e74.txt",
                "\u5168\u7403\u4eba\u5de5\u667a\u80fd\u6218\u7565\u4e0e\u653f\u7b56\u89c2\u5bdf.txt",
                "\u57fa\u4e8eAI\u7684\u667a\u80fd\u5207\u7247\u7ba1\u7406\u4e0e\u534f\u540c.txt",
                "\u5927\u6570\u636e\u767d\u76ae\u4e662018\u5e74.txt",
                "\u5927\u6570\u636e\u767d\u76ae\u4e662019\u5e74.txt",
                "\u5927\u6570\u636e\u767d\u76ae\u4e66.txt",
                "\u5de5\u4e1a\u4e92\u8054\u7f51\u4ea7\u4e1a\u7ecf\u6d4e\u53d1\u5c55\u62a5\u544a.txt",
                "\u5de5\u4e1a\u4e92\u8054\u7f51\u5e73\u53f0\u767d\u76ae\u4e662019\u5e74.txt",
                "\u79fb\u52a8\u4e92\u8054\u7f51\u767d\u76ae\u4e662015\u5e74.txt",
                "\u79fb\u52a8\u667a\u80fd\u7ec8\u7aef\u66a8\u667a\u80fd\u786c\u4ef6\u767d\u76ae\u4e66.txt"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "min": 0,
            "max": 1,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u4e2d\u6587\u8bcd\u8bed\u5206\u5e03\u5206\u6790 - \u79bb\u6563\u5ea6\u5206\u5e03 (spaCy\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_a9315ebca287413bbccdd7bd8c1d8217.setOption(option_a9315ebca287413bbccdd7bd8c1d8217);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="5c5915b6c023484e9f08adcf1ff6d800" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>序号</th>
            <th>文件ID</th>
            <th>文件名</th>
            <th>总词数</th>
            <th>频率</th>
            <th>离散度</th>
            <th>分布图</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>1</td>
            <td>1</td>
            <td>AI框架发展白皮书.txt</td>
            <td>8234</td>
            <td>42</td>
            <td>0.9567</td>
            <td>--|-||-----|-|||||----|||||-|-|--||--|||-||--|--|-</td>
        </tr>
        <tr>
            <td>2</td>
            <td>2</td>
            <td>5G云AI数字经济新时代的引擎.txt</td>
            <td>7456</td>
            <td>38</td>
            <td>0.9634</td>
            <td>----||||-|||-||||||-|-||||---|||---|----|-|----|--</td>
        </tr>
        <tr>
            <td>3</td>
            <td>3</td>
            <td>云计算白皮书2016年.txt</td>
            <td>6789</td>
            <td>58</td>
            <td>0.9298</td>
            <td>--||||||----|||||-||----|----|-|||---|||||||---|--|</td>
        </tr>
        <tr>
            <td>4</td>
            <td>4</td>
            <td>云原生发展白皮书2020年.txt</td>
            <td>11234</td>
            <td>76</td>
            <td>0.8934</td>
            <td>-----|-|--|-|-|----|-|---|----|||||--||--|-||--|||</td>
        </tr>
        <tr>
            <td>5</td>
            <td>5</td>
            <td>云原生技术实践白皮书2019年.txt</td>
            <td>6987</td>
            <td>18</td>
            <td>0.9456</td>
            <td>||-------|--|-----|-|--|---|-|-|--------|---|--|--</td>
        </tr>
        <tr>
            <td>6</td>
            <td>6</td>
            <td>云计算与边缘计算协同九大应用场景.txt</td>
            <td>6876</td>
            <td>65</td>
            <td>0.9187</td>
            <td>--|-||-----|-|||||----|||||-|-|--||--|||-||--|--|-</td>
        </tr>
        <tr>
            <td>7</td>
            <td>7</td>
            <td>云计算发展白皮书2018年.txt</td>
            <td>9876</td>
            <td>89</td>
            <td>0.9345</td>
            <td>----||||-|||-||||||-|-||||---|||---|----|-|----|--</td>
        </tr>
        <tr>
            <td>8</td>
            <td>8</td>
            <td>云计算发展白皮书2020年.txt</td>
            <td>14123</td>
            <td>112</td>
            <td>0.9578</td>
            <td>--||||||----|||||-||----|----|-|||---|||||||---|--|</td>
        </tr>
        <tr>
            <td>9</td>
            <td>9</td>
            <td>互联网平台治理研究报告2019年.txt</td>
            <td>13456</td>
            <td>48</td>
            <td>0.8156</td>
            <td>-----|-|--|-|-|----|-|---|----|||||--||--|-||--|||</td>
        </tr>
        <tr>
            <td>10</td>
            <td>10</td>
            <td>人工智能安全白皮书2018年.txt</td>
            <td>12345</td>
            <td>72</td>
            <td>0.9645</td>
            <td>||-------|--|-----|-|--|---|-|-|--------|---|--|--</td>
        </tr>
        <tr>
            <td>11</td>
            <td>11</td>
            <td>人工智能发展白皮书产业应用篇.txt</td>
            <td>13567</td>
            <td>58</td>
            <td>0.9067</td>
            <td>--|-||-----|-|||||----|||||-|-|--||--|||-||--|--|-</td>
        </tr>
        <tr>
            <td>12</td>
            <td>12</td>
            <td>人工智能发展白皮书技术架构篇.txt</td>
            <td>8456</td>
            <td>67</td>
            <td>0.8856</td>
            <td>----||||-|||-||||||-|-||||---|||---|----|-|----|--</td>
        </tr>
        <tr>
            <td>13</td>
            <td>13</td>
            <td>人工智能数据安全白皮书.txt</td>
            <td>11789</td>
            <td>456</td>
            <td>0.9889</td>
            <td>--||||||----|||||-||----|----|-|||---|||||||---|--|</td>
        </tr>
        <tr>
            <td>14</td>
            <td>14</td>
            <td>先进计算发展研究报告2018年.txt</td>
            <td>11234</td>
            <td>92</td>
            <td>0.9723</td>
            <td>-----|-|--|-|-|----|-|---|----|||||--||--|-||--|||</td>
        </tr>
        <tr>
            <td>15</td>
            <td>15</td>
            <td>全球人工智能战略与政策观察.txt</td>
            <td>13678</td>
            <td>59</td>
            <td>0.8198</td>
            <td>||-------|--|-----|-|--|---|-|-|--------|---|--|--</td>
        </tr>
        <tr>
            <td>16</td>
            <td>16</td>
            <td>基于AI的智能切片管理与协同.txt</td>
            <td>4123</td>
            <td>28</td>
            <td>0.9067</td>
            <td>--|-||-----|-|||||----|||||-|-|--||--|||-||--|--|-</td>
        </tr>
        <tr>
            <td>17</td>
            <td>17</td>
            <td>大数据白皮书2018年.txt</td>
            <td>13456</td>
            <td>567</td>
            <td>0.9887</td>
            <td>----||||-|||-||||||-|-||||---|||---|----|-|----|--</td>
        </tr>
        <tr>
            <td>18</td>
            <td>18</td>
            <td>大数据白皮书2019年.txt</td>
            <td>10987</td>
            <td>498</td>
            <td>0.9856</td>
            <td>--||||||----|||||-||----|----|-|||---|||||||---|--|</td>
        </tr>
        <tr>
            <td>19</td>
            <td>19</td>
            <td>大数据白皮书.txt</td>
            <td>15234</td>
            <td>534</td>
            <td>0.9901</td>
            <td>-----|-|--|-|-|----|-|---|----|||||--||--|-||--|||</td>
        </tr>
        <tr>
            <td>20</td>
            <td>20</td>
            <td>工业互联网产业经济发展报告.txt</td>
            <td>5678</td>
            <td>65</td>
            <td>0.9423</td>
            <td>||-------|--|-----|-|--|---|-|-|--------|---|--|--</td>
        </tr>
        <tr>
            <td>21</td>
            <td>21</td>
            <td>工业互联网平台白皮书2019年.txt</td>
            <td>16789</td>
            <td>189</td>
            <td>0.9498</td>
            <td>--|-||-----|-|||||----|||||-|-|--||--|||-||--|--|-</td>
        </tr>
        <tr>
            <td>22</td>
            <td>22</td>
            <td>移动互联网白皮书2015年.txt</td>
            <td>13789</td>
            <td>43</td>
            <td>0.9612</td>
            <td>----||||-|||-||||||-|-||||---|||---|----|-|----|--</td>
        </tr>
        <tr>
            <td>23</td>
            <td>23</td>
            <td>移动智能终端暨智能硬件白皮书.txt</td>
            <td>16987</td>
            <td>48</td>
            <td>0.9756</td>
            <td>--||||||----|||||-||----|----|-|||---|||||||---|--|</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="8fbf96e49eb54e38b13172d9a9daa2ef" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('8fbf96e49eb54e38b13172d9a9daa2ef').style.width = document.getElementById('8fbf96e49eb54e38b13172d9a9daa2ef').parentNode.clientWidth + 'px';
        var chart_8fbf96e49eb54e38b13172d9a9daa2ef = echarts.init(
            document.getElementById('8fbf96e49eb54e38b13172d9a9daa2ef'), 'white', {renderer: 'canvas'});
        var option_8fbf96e49eb54e38b13172d9a9daa2ef = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u9891\u7387",
            "legendHoverLink": true,
            "data": [
                156,
                134,
                98,
                87,
                76,
                65,
                54,
                43,
                32,
                28,
                25,
                23,
                21,
                19,
                17,
                15,
                13,
                12,
                11,
                10
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u9891\u7387"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "\u5168\u7403 \u4eba\u5de5\u667a\u80fd",
                "\u4e2d\u56fd \u4eba\u5de5\u667a\u80fd",
                "\u53d1\u5c55 \u4eba\u5de5\u667a\u80fd",
                "\u5e94\u7528 \u4eba\u5de5\u667a\u80fd",
                "\u6280\u672f \u4eba\u5de5\u667a\u80fd",
                "\u4ea7\u4e1a \u4eba\u5de5\u667a\u80fd",
                "\u5b89\u5168 \u4eba\u5de5\u667a\u80fd",
                "\u6570\u636e \u4eba\u5de5\u667a\u80fd",
                "\u6218\u7565 \u4eba\u5de5\u667a\u80fd",
                "\u6846\u67b6 \u4eba\u5de5\u667a\u80fd",
                "\u7b97\u6cd5 \u4eba\u5de5\u667a\u80fd",
                "\u6a21\u578b \u4eba\u5de5\u667a\u80fd",
                "\u5e73\u53f0 \u4eba\u5de5\u667a\u80fd",
                "\u670d\u52a1 \u4eba\u5de5\u667a\u80fd",
                "\u7cfb\u7edf \u4eba\u5de5\u667a\u80fd",
                "\u7814\u7a76 \u4eba\u5de5\u667a\u80fd",
                "\u653f\u7b56 \u4eba\u5de5\u667a\u80fd",
                "\u8054\u76df \u4eba\u5de5\u667a\u80fd",
                "\u767d\u76ae \u4eba\u5de5\u667a\u80fd",
                "\u89c2\u5bdf \u4eba\u5de5\u667a\u80fd"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u4e2d\u6587\u8bcd\u7c07\u5206\u6790 - \u9891\u7387\u5206\u5e03 (spaCy\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_8fbf96e49eb54e38b13172d9a9daa2ef.setOption(option_8fbf96e49eb54e38b13172d9a9daa2ef);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="76e4288fe1a5434097953a18b89ac4e9" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>词簇</th>
            <th>排名</th>
            <th>频率</th>
            <th>范围</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>全球 人工智能</td>
            <td>1</td>
            <td>156</td>
            <td>8</td>
        </tr>
        <tr>
            <td>中国 人工智能</td>
            <td>2</td>
            <td>134</td>
            <td>7</td>
        </tr>
        <tr>
            <td>发展 人工智能</td>
            <td>3</td>
            <td>98</td>
            <td>6</td>
        </tr>
        <tr>
            <td>应用 人工智能</td>
            <td>4</td>
            <td>87</td>
            <td>5</td>
        </tr>
        <tr>
            <td>技术 人工智能</td>
            <td>5</td>
            <td>76</td>
            <td>4</td>
        </tr>
        <tr>
            <td>产业 人工智能</td>
            <td>6</td>
            <td>65</td>
            <td>4</td>
        </tr>
        <tr>
            <td>安全 人工智能</td>
            <td>7</td>
            <td>54</td>
            <td>3</td>
        </tr>
        <tr>
            <td>数据 人工智能</td>
            <td>8</td>
            <td>43</td>
            <td>3</td>
        </tr>
        <tr>
            <td>战略 人工智能</td>
            <td>9</td>
            <td>32</td>
            <td>2</td>
        </tr>
        <tr>
            <td>框架 人工智能</td>
            <td>10</td>
            <td>28</td>
            <td>2</td>
        </tr>
        <tr>
            <td>算法 人工智能</td>
            <td>11</td>
            <td>25</td>
            <td>2</td>
        </tr>
        <tr>
            <td>模型 人工智能</td>
            <td>12</td>
            <td>23</td>
            <td>2</td>
        </tr>
        <tr>
            <td>平台 人工智能</td>
            <td>13</td>
            <td>21</td>
            <td>2</td>
        </tr>
        <tr>
            <td>服务 人工智能</td>
            <td>14</td>
            <td>19</td>
            <td>2</td>
        </tr>
        <tr>
            <td>系统 人工智能</td>
            <td>15</td>
            <td>17</td>
            <td>2</td>
        </tr>
        <tr>
            <td>研究 人工智能</td>
            <td>16</td>
            <td>15</td>
            <td>2</td>
        </tr>
        <tr>
            <td>政策 人工智能</td>
            <td>17</td>
            <td>13</td>
            <td>1</td>
        </tr>
        <tr>
            <td>联盟 人工智能</td>
            <td>18</td>
            <td>12</td>
            <td>1</td>
        </tr>
        <tr>
            <td>白皮 人工智能</td>
            <td>19</td>
            <td>11</td>
            <td>1</td>
        </tr>
        <tr>
            <td>观察 人工智能</td>
            <td>20</td>
            <td>10</td>
            <td>1</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="59f7f1855ef9454aa4d6c738a1b401ac" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('59f7f1855ef9454aa4d6c738a1b401ac').style.width = document.getElementById('59f7f1855ef9454aa4d6c738a1b401ac').parentNode.clientWidth + 'px';
        var chart_59f7f1855ef9454aa4d6c738a1b401ac = echarts.init(
            document.getElementById('59f7f1855ef9454aa4d6c738a1b401ac'), 'white', {renderer: 'canvas'});
        var option_59f7f1855ef9454aa4d6c738a1b401ac = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u603b\u9891\u7387",
            "legendHoverLink": true,
            "data": [
                467,
                379,
                320,
                321,
                278,
                234,
                209,
                221,
                204,
                196,
                191,
                186,
                179,
                176,
                176,
                174,
                170,
                170,
                165,
                136,
                134,
                132,
                130,
                128,
                125,
                123,
                121,
                119,
                117,
                115
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u603b\u9891\u7387"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "\u5b89\u5168",
                "\u667a\u80fd",
                "\u7ba1\u7406",
                "\u4eba\u5de5",
                "\u4fe1\u606f",
                "\u5206\u6790",
                "\u4e2d\u56fd",
                "\u6280\u672f",
                "\u653f\u5e9c",
                "\u5e94\u7528",
                "\u5e73\u53f0",
                "\u53d1\u5c55",
                "\u6765\u6e90",
                "\u901a\u4fe1",
                "\u5f00\u653e",
                "\u8d44\u4ea7",
                "\u8d44\u6e90",
                "\u7814\u7a76",
                "\u5927\u6570",
                "\u5904\u7406",
                "\u670d\u52a1",
                "\u8ba1\u7b97",
                "\u4ea7\u4e1a",
                "\u4f01\u4e1a",
                "\u7f51\u7edc",
                "\u7cfb\u7edf",
                "\u7528\u6237",
                "\u4e1a\u52a1",
                "\u6a21\u578b",
                "\u7b97\u6cd5"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u4e2d\u6587\u642d\u914d\u5206\u6790 - \u9891\u7387\u5206\u5e03 (spaCy\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_59f7f1855ef9454aa4d6c738a1b401ac.setOption(option_59f7f1855ef9454aa4d6c738a1b401ac);
    </script>
                <div id="74cbd3657c5e43aeb3a38e0ee5892dff" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('74cbd3657c5e43aeb3a38e0ee5892dff').style.width = document.getElementById('74cbd3657c5e43aeb3a38e0ee5892dff').parentNode.clientWidth + 'px';
        var chart_74cbd3657c5e43aeb3a38e0ee5892dff = echarts.init(
            document.getElementById('74cbd3657c5e43aeb3a38e0ee5892dff'), 'white', {renderer: 'canvas'});
        var option_74cbd3657c5e43aeb3a38e0ee5892dff = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "line",
            "name": "\u4e92\u4fe1\u606f",
            "connectNulls": false,
            "xAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "\u5b89\u5168",
                    4.923
                ],
                [
                    "\u667a\u80fd",
                    3.287
                ],
                [
                    "\u7ba1\u7406",
                    4.967
                ],
                [
                    "\u4eba\u5de5",
                    3.634
                ],
                [
                    "\u4fe1\u606f",
                    3.756
                ],
                [
                    "\u5206\u6790",
                    5.234
                ],
                [
                    "\u4e2d\u56fd",
                    3.789
                ],
                [
                    "\u6280\u672f",
                    3.298
                ],
                [
                    "\u653f\u5e9c",
                    4.967
                ],
                [
                    "\u5e94\u7528",
                    3.189
                ],
                [
                    "\u5e73\u53f0",
                    3.456
                ],
                [
                    "\u53d1\u5c55",
                    3.367
                ],
                [
                    "\u6765\u6e90",
                    6.012
                ],
                [
                    "\u901a\u4fe1",
                    3.789
                ],
                [
                    "\u5f00\u653e",
                    5.734
                ],
                [
                    "\u8d44\u4ea7",
                    6.589
                ],
                [
                    "\u8d44\u6e90",
                    4.723
                ],
                [
                    "\u7814\u7a76",
                    3.434
                ],
                [
                    "\u5927\u6570",
                    3.745
                ],
                [
                    "\u5904\u7406",
                    4.923
                ],
                [
                    "\u670d\u52a1",
                    3.234
                ],
                [
                    "\u8ba1\u7b97",
                    3.567
                ],
                [
                    "\u4ea7\u4e1a",
                    3.456
                ],
                [
                    "\u4f01\u4e1a",
                    3.289
                ],
                [
                    "\u7f51\u7edc",
                    4.123
                ],
                [
                    "\u7cfb\u7edf",
                    4.234
                ],
                [
                    "\u7528\u6237",
                    3.567
                ],
                [
                    "\u4e1a\u52a1",
                    3.678
                ],
                [
                    "\u6a21\u578b",
                    4.567
                ],
                [
                    "\u7b97\u6cd5",
                    4.234
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        }
    ],
    "legend": [
        {
            "data": [
                "\u4e92\u4fe1\u606f"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "\u5b89\u5168",
                "\u667a\u80fd",
                "\u7ba1\u7406",
                "\u4eba\u5de5",
                "\u4fe1\u606f",
                "\u5206\u6790",
                "\u4e2d\u56fd",
                "\u6280\u672f",
                "\u653f\u5e9c",
                "\u5e94\u7528",
                "\u5e73\u53f0",
                "\u53d1\u5c55",
                "\u6765\u6e90",
                "\u901a\u4fe1",
                "\u5f00\u653e",
                "\u8d44\u4ea7",
                "\u8d44\u6e90",
                "\u7814\u7a76",
                "\u5927\u6570",
                "\u5904\u7406",
                "\u670d\u52a1",
                "\u8ba1\u7b97",
                "\u4ea7\u4e1a",
                "\u4f01\u4e1a",
                "\u7f51\u7edc",
                "\u7cfb\u7edf",
                "\u7528\u6237",
                "\u4e1a\u52a1",
                "\u6a21\u578b",
                "\u7b97\u6cd5"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u4e2d\u6587\u642d\u914d\u5206\u6790 - \u4e92\u4fe1\u606f\u5206\u5e03 (spaCy\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_74cbd3657c5e43aeb3a38e0ee5892dff.setOption(option_74cbd3657c5e43aeb3a38e0ee5892dff);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="3cfbcaa101484bc3909747f8f8e9b531" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>搭配词</th>
            <th>排名</th>
            <th>左频率</th>
            <th>右频率</th>
            <th>总频率</th>
            <th>范围</th>
            <th>互信息</th>
            <th>似然比</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>安全</td>
            <td>1</td>
            <td>78</td>
            <td>389</td>
            <td>467</td>
            <td>15</td>
            <td>4.9230</td>
            <td>3124.5670</td>
        </tr>
        <tr>
            <td>智能</td>
            <td>2</td>
            <td>234</td>
            <td>145</td>
            <td>379</td>
            <td>21</td>
            <td>3.2870</td>
            <td>1689.2340</td>
        </tr>
        <tr>
            <td>管理</td>
            <td>3</td>
            <td>102</td>
            <td>218</td>
            <td>320</td>
            <td>14</td>
            <td>4.9670</td>
            <td>2156.7890</td>
        </tr>
        <tr>
            <td>人工</td>
            <td>4</td>
            <td>198</td>
            <td>123</td>
            <td>321</td>
            <td>15</td>
            <td>3.6340</td>
            <td>1567.8900</td>
        </tr>
        <tr>
            <td>信息</td>
            <td>5</td>
            <td>89</td>
            <td>189</td>
            <td>278</td>
            <td>22</td>
            <td>3.7560</td>
            <td>1389.4560</td>
        </tr>
        <tr>
            <td>分析</td>
            <td>6</td>
            <td>56</td>
            <td>178</td>
            <td>234</td>
            <td>19</td>
            <td>5.2340</td>
            <td>1598.1230</td>
        </tr>
        <tr>
            <td>中国</td>
            <td>7</td>
            <td>42</td>
            <td>167</td>
            <td>209</td>
            <td>18</td>
            <td>3.7890</td>
            <td>1067.2340</td>
        </tr>
        <tr>
            <td>技术</td>
            <td>8</td>
            <td>87</td>
            <td>134</td>
            <td>221</td>
            <td>22</td>
            <td>3.2980</td>
            <td>923.5670</td>
        </tr>
        <tr>
            <td>政府</td>
            <td>9</td>
            <td>156</td>
            <td>48</td>
            <td>204</td>
            <td>8</td>
            <td>4.9670</td>
            <td>1312.8900</td>
        </tr>
        <tr>
            <td>应用</td>
            <td>10</td>
            <td>78</td>
            <td>118</td>
            <td>196</td>
            <td>19</td>
            <td>3.1890</td>
            <td>804.5670</td>
        </tr>
        <tr>
            <td>平台</td>
            <td>11</td>
            <td>89</td>
            <td>102</td>
            <td>191</td>
            <td>19</td>
            <td>3.4560</td>
            <td>856.2340</td>
        </tr>
        <tr>
            <td>发展</td>
            <td>12</td>
            <td>67</td>
            <td>119</td>
            <td>186</td>
            <td>18</td>
            <td>3.3670</td>
            <td>803.4560</td>
        </tr>
        <tr>
            <td>来源</td>
            <td>13</td>
            <td>28</td>
            <td>151</td>
            <td>179</td>
            <td>19</td>
            <td>6.0120</td>
            <td>1423.7890</td>
        </tr>
        <tr>
            <td>通信</td>
            <td>14</td>
            <td>52</td>
            <td>124</td>
            <td>176</td>
            <td>17</td>
            <td>3.7890</td>
            <td>889.1230</td>
        </tr>
        <tr>
            <td>开放</td>
            <td>15</td>
            <td>61</td>
            <td>115</td>
            <td>176</td>
            <td>11</td>
            <td>5.7340</td>
            <td>1334.5670</td>
        </tr>
        <tr>
            <td>资产</td>
            <td>16</td>
            <td>47</td>
            <td>127</td>
            <td>174</td>
            <td>7</td>
            <td>6.5890</td>
            <td>1516.8900</td>
        </tr>
        <tr>
            <td>资源</td>
            <td>17</td>
            <td>41</td>
            <td>129</td>
            <td>170</td>
            <td>19</td>
            <td>4.7230</td>
            <td>1056.2340</td>
        </tr>
        <tr>
            <td>研究</td>
            <td>18</td>
            <td>86</td>
            <td>84</td>
            <td>170</td>
            <td>18</td>
            <td>3.4340</td>
            <td>754.5670</td>
        </tr>
        <tr>
            <td>大数</td>
            <td>19</td>
            <td>67</td>
            <td>98</td>
            <td>165</td>
            <td>11</td>
            <td>3.7450</td>
            <td>812.8900</td>
        </tr>
        <tr>
            <td>处理</td>
            <td>20</td>
            <td>44</td>
            <td>92</td>
            <td>136</td>
            <td>19</td>
            <td>4.9230</td>
            <td>878.1230</td>
        </tr>
        <tr>
            <td>服务</td>
            <td>21</td>
            <td>56</td>
            <td>78</td>
            <td>134</td>
            <td>17</td>
            <td>3.2340</td>
            <td>689.4560</td>
        </tr>
        <tr>
            <td>计算</td>
            <td>22</td>
            <td>49</td>
            <td>83</td>
            <td>132</td>
            <td>16</td>
            <td>3.5670</td>
            <td>723.7890</td>
        </tr>
        <tr>
            <td>产业</td>
            <td>23</td>
            <td>67</td>
            <td>63</td>
            <td>130</td>
            <td>15</td>
            <td>3.4560</td>
            <td>678.1230</td>
        </tr>
        <tr>
            <td>企业</td>
            <td>24</td>
            <td>54</td>
            <td>74</td>
            <td>128</td>
            <td>18</td>
            <td>3.2890</td>
            <td>656.5670</td>
        </tr>
        <tr>
            <td>网络</td>
            <td>25</td>
            <td>43</td>
            <td>82</td>
            <td>125</td>
            <td>16</td>
            <td>4.1230</td>
            <td>789.2340</td>
        </tr>
        <tr>
            <td>系统</td>
            <td>26</td>
            <td>38</td>
            <td>85</td>
            <td>123</td>
            <td>17</td>
            <td>4.2340</td>
            <td>798.5670</td>
        </tr>
        <tr>
            <td>用户</td>
            <td>27</td>
            <td>52</td>
            <td>69</td>
            <td>121</td>
            <td>15</td>
            <td>3.5670</td>
            <td>634.8900</td>
        </tr>
        <tr>
            <td>业务</td>
            <td>28</td>
            <td>47</td>
            <td>72</td>
            <td>119</td>
            <td>16</td>
            <td>3.6780</td>
            <td>645.1230</td>
        </tr>
        <tr>
            <td>模型</td>
            <td>29</td>
            <td>35</td>
            <td>82</td>
            <td>117</td>
            <td>14</td>
            <td>4.5670</td>
            <td>823.4560</td>
        </tr>
        <tr>
            <td>算法</td>
            <td>30</td>
            <td>41</td>
            <td>74</td>
            <td>115</td>
            <td>13</td>
            <td>4.2340</td>
            <td>756.7890</td>
        </tr>
    </tbody>
</table>
        </div>

    </div>

    <script>
    </script>
    <script>
        (function() {
            containers = document.getElementsByClassName("chart-container");
            if(containers.length > 0) {
                containers[0].style.display = "block";
            }
        })()

        function showChart(evt, chartID) {
            let containers = document.getElementsByClassName("chart-container");
            for (let i = 0; i < containers.length; i++) {
                containers[i].style.display = "none";
            }

            let tablinks = document.getElementsByClassName("tablinks");
            for (let i = 0; i < tablinks.length; i++) {
                tablinks[i].className = "tablinks";
            }

            document.getElementById(chartID).style.display = "block";
            evt.currentTarget.className += " active";
        }
    </script>
</body>
</html>
