# coding=utf-8
"""
英文文本分析 - 可视化 (使用NLTK库)
"""
import os
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import WordCloud, Bar, Tab
from pyecharts.components import Table

class EnglishNLTKVisualizer:
    """英文文本可视化类 - NLTK库版本"""

    def __init__(self):
        self.tab = Tab()

    def add_title(self, title, subtitle=""):
        return opts.TitleOpts(title=title, subtitle=subtitle, pos_left="center")

    def visualize_word_frequency(self, csv_file):
        """可视化词频分析结果"""
        if not os.path.exists(csv_file):
            return None

        df = pd.read_csv(csv_file)
        words_freq = list(zip(df['Word'].tolist(), df['Frequency'].tolist()))

        wordcloud = (
            WordCloud()
            .add("", words_freq[:100], word_size_range=[15, 80])
            .set_global_opts(title_opts=self.add_title("英文词频分析 - 词云图 (NLTK库)"))
        )

        top_20 = df.head(20)
        bar = (
            Bar()
            .add_xaxis(top_20['Word'].tolist())
            .add_yaxis("词频", top_20['Frequency'].tolist())
            .set_global_opts(title_opts=self.add_title("英文词频分析 - 前20高频词 (NLTK库)"))
        )

        return wordcloud, bar

    def visualize_kwic(self, csv_file):
        """可视化KWIC分析结果"""
        if not os.path.exists(csv_file):
            return None

        df = pd.read_csv(csv_file)

        # 创建表格数据
        table_data = []
        for _, row in df.iterrows():
            table_data.append([row['left'], row['keyword'], row['right']])

        headers = ["左上下文", "关键词", "右上下文"]
        table = Table()
        table.add(headers, table_data[:50])  # 显示前50条结果
        table.set_global_opts(
            title_opts=self.add_title(f"英文KWIC分析结果 - NLTK库（共{len(df)}条）")
        )

        return table

    def generate_visualization(self):
        """生成完整的可视化报告"""
        print("=== 英文文本分析可视化 (NLTK库) ===")
        
        # 创建主页
        table_data = [
            ["词频分析", "使用NLTK库进行英文分词和词形还原"],
            ["KWIC分析", "关键词上下文索引，基于NLTK分词结果"]
        ]
        table = Table()
        table.add(["分析类型", "说明"], table_data)
        table.set_global_opts(
            title_opts=self.add_title("英文文本分析可视化导航 - NLTK库版本")
        )
        self.tab.add(table, "首页")

        # 词频分析
        if os.path.exists('nltk_word_frequency_report.csv'):
            result = self.visualize_word_frequency('nltk_word_frequency_report.csv')
            if result:
                wordcloud, bar = result
                self.tab.add(wordcloud, "词频分析-词云图")
                self.tab.add(bar, "词频分析-柱状图")

        # KWIC分析
        kwic_files = [f for f in os.listdir('.') if f.startswith('nltk_KWIC_') and f.endswith('.csv')]
        if kwic_files:
            kwic_table = self.visualize_kwic(kwic_files[0])
            if kwic_table:
                self.tab.add(kwic_table, "KWIC分析")

        # 渲染
        output_file = "英文文本分析可视化_NLTK库.html"
        self.tab.render(output_file)
        print(f"可视化报告已生成: {output_file}")

def main():
    visualizer = EnglishNLTKVisualizer()
    visualizer.generate_visualization()

if __name__ == "__main__":
    main()
