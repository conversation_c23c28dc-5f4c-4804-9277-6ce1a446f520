 

 

 

 

云计算与边缘计算协同

九大应用场景

（2019年） 

 

 

 

 

云计算开源产业联盟 

OpenSource Cloud Alliance for induseRy, OSCAR

2019年7月

版权声明

本白皮书版权属于编写单位，并受法律保护。转载、摘编或

利用其它方式使用本调查报告文字或者观点的，应注明来源。违

反上述声明者，将追究其相关法律责任。

编写说明

牵头单位： 中国信息通信研究院

参与单位： 中国联合网络通信集团有限公司、中兴通讯股份有限公司、四川虹微技术有限

公司、中国电信股份有限公司北京研究院、烽火通信科技股份有限公司、上海

云轴信息科技有限公司、阿里云计算有限公司、华为软件技术有限公司、中移

物联网有限公司、新华三集团、杭州数梦工场科技有限公司

编写人： 栗蔚、徐恩庆、董恩然、罗欧、张琳琳、吕华章、李响、常清雪、王峰、肖扬、

王光杰、梅磊、朱松、高巍、毛峻岭、万晓兰、叶航辉、李小庆、陈旭

 

 

前 言 

2019 年边缘计算备受产业关注，一度引起了资本市场的投资热

潮，很多人把 2019 年称作边缘计算的元年。理性来看，造成如此火

爆局势难免有一些炒作因素在推波助澜，毕竟边缘计算的概念存世也

已多年。当然，毋庸置疑的是，工业互联网的大力推动、5G 大规模

商用的持续酝酿等因素，让整个产业对 IT 和 OT 的深度融合充满信

心和期待。这种情况下，也许边缘计算不火也难。

为了褒扬边缘计算，有些声音把云计算的概念描述的稍显狭隘或

者刻意地把云计算放到了边缘计算的对立面。但从技术或商业演进的

实际情况来看，边缘计算其实更多的是云计算向终端和用户侧延伸形

成的新解决方案。边缘计算本身就是云计算概念的延伸，即便是赋予

其独立的概念，也无法做到与云计算切割开，二者本就是相依而生、

协同运作的。云边协同将成为主流模式，这种协同模式下，云计算在

向一种更加全局化的分布式节点组合新形态进阶。

在开展云边协同相关研究的初期，本白皮书以《云计算与边缘计

算协同九大应用场景》为题，由中国信通院联合产业多方撰写。旨在

以理性而乐观的态度，分析云边协同在典型场景下的应用需求和业务

模式，为引导产业发展和制定相关标准做铺垫、打基础。

目 录 

一、云边协同的新浪潮...................................................................................................................1

（一）边缘计算是云计算向边缘侧分布式拓展的新触角...........................................................1

（二）边缘计算典型产品和业务模式...........................................................................................3

1. ICT 服务商将云计算能力逐步扩展到边缘设备........................................................4

2.工业企业依托丰富的工业场景发挥现场级应用能力................................................5

3.电信运营商依托 5G 全面部署 MEC ...........................................................................5

（三）云计算与边缘计算如何协同...............................................................................................6

二、云边协同九大应用场景...........................................................................................................7

（一）云边协同在 CDN 场景中的应用 ........................................................................................8

1.价格和网络成为 CDN 发展痛点 .................................................................................8

2.CDN 与边缘云结合向下一代内容分发平台升级.......................................................8

3.典型案例......................................................................................................................10

（二）云边协同在工业互联网场景中的应用.............................................................................10

1. 工业互联网助力工业企业智能化转型.................................................................10

2. 云边协同是工业互联网的重要支柱.....................................................................10

3. 典型案例.................................................................................................................12

（三）云边协同在能源场景中的应用.........................................................................................12

1. 传统能源行业信息化转型痛点.............................................................................12

2. 云边协同助力传统能源产业向能源互联网升级.................................................13

3. 典型案例.................................................................................................................14

（四）云边协同在智能家庭场景中的应用.................................................................................15

1. 智能化信息服务逐步进家入户.............................................................................15

2. 云边协同赋予智能家庭新内涵.............................................................................15

3. 典型案例.................................................................................................................16

（五）云边协同在智慧交通场景中的应用.................................................................................17

1. 智慧交通发展中的痛点.........................................................................................17

2. 智慧交通借助云边协同向车路协同发展.............................................................17

3. 典型案例.................................................................................................................19

（六）云边协同在安防监控场景中的应用.................................................................................20

1. 安防监控传统部署方式对网络和成本的要求较高.............................................20

2. 云边协同引领安防智能化技术潮流.....................................................................20

3. 典型案例.................................................................................................................21

（七）云边协同在农业生产场景中的应用.................................................................................22

1. 传统农业信息化发展痛点和趋势.........................................................................22

2. 云边协同加速传统农业向智慧农业转型.............................................................22

3. 典型案例.................................................................................................................23

（八）云边协同在医疗保健场景中的应用分析.........................................................................24

1. 医疗保健行业发展迅速后的担忧.........................................................................24

2. 医疗保健未来发展方向.........................................................................................25

3. 云边协同助力医疗保健向智能医疗升级.............................................................25

（九）云边协同在云游戏场景中的应用分析.............................................................................26

1. 游戏市场火热背后的隐患.....................................................................................26

2. “云游戏”迅猛发展 .................................................................................................27

3. 云边协同助力云游戏实现升级.............................................................................28

三、云边协同发展建议.................................................................................................................29

（一）以应用为导向 稳步推进计算能力向边缘侧下沉...........................................................29

（二）加快标准体系建设 注重协同能力规范...........................................................................29

《云计算与边缘计算协同九大应用场景》

1

一、云边协同的新浪潮

（一）边缘计算是云计算向边缘侧分布式拓展的新触

角

欧洲电信标准化协会认为边缘计算是在移动网络边缘提供 IT 服

务环境和计算能力，强调靠近移动用户，以减少网络操作和服务交付

的时延，提高用户体验。

Gartner 认为边缘计算描述了一种计算拓扑，在这种拓扑结构中，

信息处理、内容采集和分发均被置于距离信息更近的源头处完成。

维基百科认为边缘计算是一种优化云计算系统的方法，在网络边

缘执行数据处理，靠近数据的来源。

边缘计算产业联盟认为边缘计算是在靠近物或数据源头的网络

边缘侧，融合网络、计算、存储、应用核心能力的开放平台，就近提

供边缘智能服务，满足行业数字化在敏捷联接、实时业务、数据优化、

应用智能、安全与隐私保护等方面的关键需求。

开放雾计算联盟认为雾计算是一种水平的系统级架构，可以将云

到物连续性中的计算、存储、控制、网络功能更接近用户。

上述边缘计算的各种定义虽然表述上各有差异，但基本都在表达

一个共识：在更靠近终端的网络边缘上提供服务。

“中心-边缘-端”的形态从电信开始之初就已经形成。电信时

代，程控交换中心、程控交换机、电话形成了最初的“中心-边缘-端”

形态；互联网时代，数据中心、CDN、移动电话/PC 延续了这种形态；

《云计算与边缘计算协同九大应用场景》

到达云计算+物联网时代，云计算中心、小数据中心/网关、传感器则

形成了新的“云-边-端”形态。

图 1 “云-边-端”发展示意图

如果仅从边缘侧本身寻求边缘计算的定义，似乎很难让人完全

接受这个似乎有点包罗万象的新事物。一个比较简单的质疑就是，

智能终端、家庭网关或者其他早就存在我们身边的计算设备，难道

隐藏了多年的边缘计算身份么。

为了褒扬边缘计算，有些声音把云计算的概念描述的稍显狭隘

或者刻意地把云计算放到了边缘计算的对立面。但从技术或商业演

进的实际情况来看，边缘计算其实更多的是云计算向终端和用户侧

延伸形成的新解决方案。边缘计算本身就是云计算概念的延伸，即

便是赋予其独立的概念，也无法做到与云计算切割开，二者本就是

相依而生、协同运作的。

在本白皮书中，我们认为，在面向物联网、大流量等场景下，

为了满足更广连接、更低时延、更好控制等需求，云计算在向一种

2

《云计算与边缘计算协同九大应用场景》

更加全局化的分布式节点组合形态进阶，边缘计算是其向边缘侧分

布式拓展的新触角。值得一提的是，中国信通院牵头撰写的

《Global Management Framework of Distributed Cloud》已在

ITU 成功立项，旨在基于中心云与边缘云形成的分布式一体化形态

上，探索全局化管理新框架。

图 2 分布式云示意图

（二）边缘计算典型产品和业务模式

主宰云计算市场的巨头公司依托云计算技术先发优势，将云计算

技术下沉到边缘侧，以强化边缘侧人工智能为契机，大力发展边缘计

算。工业企业依托丰富的工业场景，开展边缘计算实践强化现场级控

制力。电信运营商正迎接 5G 市场机遇，全面部署边缘节点，为布局

下一代基础设施打下牢牢的根基。

3

《云计算与边缘计算协同九大应用场景》

1. ICT 服务商将云计算能力逐步扩展到边缘设备

在国际上，云计算巨头亚马逊、微软和谷歌都已经推出了相关边

缘计算产品。亚马逊推出 AWS Greengrass 功能软件，将 AWS 扩展

到设备上，在本地处理终端生成的数据，同时仍然可以使用运来进行

管理、数据分析和持久的存储；微软发布 Azure IoT Edge 边缘侧产

品，将云分析扩展到边缘设备，支持离线使用，同时聚焦边缘的人工

智能应用；谷歌也在 2018 年推出了硬件芯片 Edge TPU 和软件堆栈

Cloud IoT Edge，可将数据处理和机器学习功能扩展到边缘设备，

使设备能够对来自其传感器的数据进行实时操作，并在本地进行结果

预测。

在国内，阿里、腾讯、百度、华为、中兴通讯、数梦工场、新华

三等也推出了相应的边缘计算产品。阿里推出 Link IoT Edge 平台。

通过部署在不同量级的智能设备和端侧计算节点中。通过定义物模型

连接不同协议、不同数据格式的设备，提供安全可靠、低延时、低成

本、易扩展的本地计算服务；腾讯针对边缘计算推出了 CDN Edge，

将数据中心的服务下沉至 CDN 边缘节点，以最低的延迟相应终端用

户，同时降低用户数据中心的计算压力和网络负载；百度推出智能边

缘 BIE，将云计算能力拓展至用户现场，提供临时离线、低延时的计

算服务，同时配合智能边缘云端管理套件，形成“云管理，端计算”

的端云一体解决方案；华为在 2018 年推出了 IEF 平台，通过纳管用

户的边缘节点，提供将云上应用延伸到边缘的能力，联动边缘和云端

的数据，为企业提供完整的边缘和云协同的一体化服务的边缘计算解

4

《云计算与边缘计算协同九大应用场景》

决方案；中兴通讯推出了边缘计算产品，提供从硬件到软件全套的基

础设施，支持多种边缘计算系统级方案，在边缘计算平台上提供多种

高算力的应用的资源；数梦工场推出统一的 DT 资源管控产品，提供

“两领域+三形态”的融合管控能力，覆盖中心节点领域、边缘节点领

域以及云平台、数据中台、业务中台三种形态的统一管控；新华三推

出了超融合边缘计算产品 UIS-Edge，支持多种硬件形态与部署方式，

将云原生能力延伸至边缘，提供完善的设备接入、边缘计算及云边协

同能力，为企业提供一站式云边端融合解决方案。

2.工业企业依托丰富的工业场景发挥现场级应用能力

海尔专门为物联网企业打造的一站式设备管理平台 COSMO￾Edge 平台，提供多源的边缘设备接入能力与强大的边缘计算能力，

支持多种工业协议解析，提供可视化流式管道，提供数字化建模与实

体映射，提供设备即服务的应用模式，帮助用户快速构建物联网应用，

实现数字化生产，助力于企业效益提升；树根互联提供的开放物联平

台，提供物联网关 Gateway、根云 T-Box 车载物联盒、根云连接器、

根云物联代理开放平台等一系列覆盖主流工业控制器和工业协议解

析，实现全行业各类设备一站式快速接入，提供便捷、便宜、开放的

设备接入解决方案。

3.电信运营商依托 5G 全面部署 MEC

移动边缘计算（MEC）是利用无线接入网络就近提供电信用户 IT

所需服务和云端计算功能，实现计算及存储资源的弹性利用。多接入

5

《云计算与边缘计算协同九大应用场景》

边缘计算（MAEC）则是将边缘计算从电信蜂窝网络进一步延伸至其

他无线接入网络。

中国移动已在全国 10 省 20 多个地市现网开展 MEC 应用试点，

尝试构建基于边缘 TIC 的 MEC 端到端方案验证平台，并基于 5G 边

缘云技术在 VR 上进行相关应用，未来将从标准、技术、产业三方面

增强 MEC 与 5G 的结合；中国联通在 2018 年发布《中国联通 Edge￾Cloud 平台架构及产业生态白皮书》，并在 15 个省市进行规模试点

及试商用网络建设，计划 2019 年面向 5G 平滑演进，基于 Edge￾Cloud 平台打造智慧港口、智能驾驶、智慧场馆、智能制造、视频监

控、云游戏、智慧医疗等 30 余个试商用样板工程；中国电信在深圳

召开的5G创新合作大会上对外展示了自主研发的基于分布式开放平

台的边缘计算 MEC 平台，该平台就近提供边缘智能服务，支持固定

/移动网络接入、第三方能力/应用灵活部署以及边缘能力统一开放，

可应用于工业互联网、高清视频、车联网等行业。

（三）云计算与边缘计算如何协同

以物联网场景举例。物联网中的设备产生大量的数据，数据都上

传到云端进行处理，会对云端造成巨大的压力，为分担中心云节点的

压力，边缘计算节点可以负责自己范围内的数据计算和存储工作。同

时，大多数的数据并不是一次性数据，那些经过处理的数据仍需要从

边缘节点汇聚集中到中心云，云计算做大数据分析挖掘、数据共享，

同时进行算法模型的训练和升级，升级后的算法推送到前端，使前端

设备更新和升级，完成自主学习闭环。同时，这些数据也有备份的需

6

《云计算与边缘计算协同九大应用场景》

要，当边缘计算过程中出现意外情况，存储在云端的数据也不会丢失。

图 3 物联网场景下云边协同示意图

云计算与边缘计算需要通过紧密协同才能更好地满足各种需求

场景的匹配，从而最大化体现云计算与边缘计算的应用价值。同时，

从边缘计算的特点出发，实时或更快速的数据处理和分析、节省网络

流量、可离线运行并支持断点续传、本地数据更高安全保护等在应用

云边协同的各个场景中都有着充分的体现。

二、云边协同九大应用场景

本白皮书将对云边协同在 CDN、工业互联网、能源、智能家庭、

智慧交通、安防监控、农业生产等场景中的应用进行介绍，同时对云

边协同在医疗保健、云游戏等场景中的前景进行分析。

7

《云计算与边缘计算协同九大应用场景》

（一）云边协同在 CDN 场景中的应用

1.价格和网络成为 CDN 发展痛点

价格过高。随着国内互联网的大踏步发展，CDN 服务作为互联

网内容的快递员，显得越发重要了。对于专业的 CDN 服务商，其专

注核心业务的发展，易扩大经营规模，进入市场较早，具有成熟的运

营机制和较高的服务能力。但由于 CDN 运营的不灵活导致带宽资费

设置不灵活，不能按需索取，导致 CDN 价格居高不下。

占用网络资源带宽。视频类业务的快速增长对移动运营商的网络

承载能力带来了很大的冲击。当前移动网的 CDN 系统一般部署在省

级 IDC 机房，并非运行于移动网络内部，离移动用户较远，仍然需要

占用大量的移动回传带宽，服务的“就近”程度尚不足以满足对时延

和带宽更敏感的移动业务场景。另外，目前 OTT 厂家已经规模部署

了很多 CDN 节点，但 CDN 主要部署在固网内部，移动用户访问视

频业务均需要通过核心网后端进行访问，对运营商的网络资源传输带

宽带来了很大的挑战。

2.CDN 与边缘云结合向下一代内容分发平台升级

随着目前 5G 的部署，配合 AI 技术、大数据、云计算、IoT 等，

万物互联的信息时代将让互联网进入一个新的阶段，现阶段的 CDN

架构已经无法满足 5G 时代的应用需求，CDN 将迎来以边缘云+AI

的新发展，以快速响应需求并实现服务能力、服务状态和服务质量的

更加透明。通过将 CDN 部署到移动网络内部，比如借助边缘云平台

8

《云计算与边缘计算协同九大应用场景》

将 vCDN（virtual Content Delivery Network，虚拟内容分发网络）

下沉到运营商的边缘数据中心中，将大大缓解传统网络的压力，并且

提升移动用户视频业务的体验。基于云边协同构建 CDN，不仅在中

心 IDC 的基础上扩大 CDN 资源池，同时还可以有效的利用边缘云进

一步提升 CDN 节点满足资源弹性伸缩的能力。

图 4 基于边缘云的 vCDN 实现场景

CDN 云边协同适用于本地化+热点内容频繁请求的场景，适用

于商超、住宅、办公楼宇、校园等。对于近期热点视频和内容，可能

出现本地化频繁请求，通过一次远端内容回源本地建立 vCDN 节点

之后，本地区内多次请求热点内容均可从本地节点分发，提高命中率，

降低响应时延，可提升 QoS 指标。同理，还可将此类过程应用于 4K、

8K、AR/VR、3D 全息等场景，本地化快速建立场景和环境，同时提

高用户体验，降低眩晕感和延迟卡顿。

9

《云计算与边缘计算协同九大应用场景》

3.典型案例

中国电信自主研发的内容边缘化分发缓存的虚拟网络（EECDN）。

相比传统 CDN，边缘节点更加下沉，将内容分发能力延伸至区县级，

将源站内容分发至最接近用户的节点，使用户可就近获取所需内容，

解决带宽及性能带来的访问延迟问题，从而提高用户访问的响应速度

和成功率，适用于站点下载加速、点播、直播等场景。

（二）云边协同在工业互联网场景中的应用

1. 工业互联网助力工业企业智能化转型

近年来，在国家供给侧改革政策的推动下，工业领域的需求在持

续复苏，但在人们对于物质品质需求不断提高、人力成本不断上涨以

及上游材料成本提升等多重因素下，逼迫工业企业向智能化靠拢。工

业互联网凭借其新一代信息技术与工业系统全方位深度融合的特点，

成为工业企业向智能化转型的关键综合信息基础设施。

2. 云边协同是工业互联网的重要支柱

近年来，随着政府部门陆续出台相关政策支持以及生态建设的不

断完善，中国工业互联网产业正在迅猛发展。据 IDC 预测，到 2020

年全球将有超过 50%的物联网数据将在边缘处理，而工业互联网作

为物联网在工业制造领域的延伸，也继承了物联网数据海量异构的特

点。在工业互联网场景中，边缘设备只能处理局部数据，无法形成全

局认知，在实际应用中仍然需要借助云计算平台来实现信息的融合，

因此，云边协同正逐渐成为支撑工业互联网发展的重要支柱。

10

《云计算与边缘计算协同九大应用场景》

11

工业互联网的边缘计算与云计算协同工作,在边缘计算环境中安

装和连接的智能设备能够处理关键任务数据并实时响应，而不是通过

网络将所有数据发送到云端并等待云端响应。设备本身就像一个迷你

数据中心，由于基本分析正在设备上进行，因此延迟几乎为零。利用

这种新增功能，数据处理变得分散，网络流量大大减少。云端可以在

以后收集这些数据进行第二轮评估，处理和深入分析。

同时，在工业制造领域，单点故障在工业级应用场景中是绝对不

能被接受的，因此除了云端的统一控制外，工业现场的边缘计算节点

必须具备一定的计算能力，能够自主判断并解决问题，及时检测异常

情况，更好的实现预测性监控，提升工厂运行效率的同时也能预防设

备故障问题。将处理后的数据上传到云端进行存储、管理、态势感知，

同时，云端也负责对数据传输监控和边缘设备使用进行管理。

图 5 工业互联网利用边缘云实现云边协同示意图

中心云 中心云

边缘云 边缘云 边缘云

边缘网关

边缘控制器 边缘网关

边缘控制器

智能终端

工业总线

公有云

《云计算与边缘计算协同九大应用场景》

3. 典型案例

四川爱联科技有限公司，通过在工厂的网络边缘层部署边缘计算

设备及配套设备，边缘计算设备通过数据采集模块从所有 PLC 设备

采集实时数据，存储于实时数据库内，供 MES、ERP 等其他功能模

块、系统调用处理，建立起工单、物料、设备、人员、工具、质量、

产品之间的关联关系，保证信息的继承性与可追溯性，在边缘层快速

建立一体化和实时化的信息体系，满足工业现场对实时性要求，实现

工业现场的传感器、自动化设备、机器人的数据接入，提供数据采集、

数据分析、人工智能（推理阶段）等服务。由边缘计算设备接入云端，

实现大量、异地分布的数据接入，既可以向生产管理人员提供车间作

业和设备的实际状况，也可以向业务部门提供客户订单的生产情况，

还能根据实际生产情况计算出直接物料的成本、产量、设备故障、消

耗等，构建云端-边缘协同化的生产管理体系。

（三）云边协同在能源场景中的应用

1. 传统能源行业信息化转型痛点

电力、石油石化等传统能源行业的信息化具有接入设备多、服务

对象广泛、信息量大、业务周期峰值明显等行业特色。云计算技术虚

拟化、资源共享和弹性伸缩等特点能够很好地解决服务对象广泛及业

务周期峰值等问题，但对于海量接入设备产生的大量数据，如果全都

上传至云端进行处理，一方面会给云端带来过大的计算压力；另一方

面会给网络带宽资源造成巨大的负担。同时，由于电力、石油企业涉

12

《云计算与边缘计算协同九大应用场景》

及的很多终端设备、传感器处于环境极端、地理位置偏远的地区，大

部分都没有很好的网络传输条件，无法满足原始数据的大批量传输工

作。

2. 云边协同助力传统能源产业向能源互联网升级

能源互联网是一种互联网与能源生产、传输、存储、消费以及能

源市场深度融合的能源产业发展新形态，具有设备智能、多能协同、

信息堆成、供需分散、系统扁平、交易开放等主要特征。

在传统能源产业向能源互联网升级的过程中，利用云计算和边缘

计算两方的优势，可以加速升级过程。

以石油行业为例，在油气开采、运输、储存等各个关键环节，均

会产生大量的生产数据。在传统模式下，需要大量的人力通过人工抄

表的方式定期对数据进行收集，并且对设备进行监控检查，以预防安

全事故的发生。抄表员定期将收集的数据进行上报，再由数据员对数

据进行人工的录入和分析，一来人工成本非常高，二来数据分析效率

低、时延大，并且不能实时掌握各关键设备的状态，无法提前预见安

全事件防范事故。而边缘计算节点的加入，则可以通过温度、湿度、

压力传感器芯片以及具备联网功能的摄像头等设备，实现对油气开采

关键环节关键设备的实时自动化数据收集和安全监控，将实时采集的

原始数据首先汇集至边缘计算节点中进行初步计算分析，对特定设备

的健康状况进行监测并进行相关的控制。此时需要与云端交互的数据

仅为经过加工分析后的高价值数据，一方面极大的节省了网络带宽资

源，另一方面也为云端后续进一步大数据分析、数据挖掘提供了数据

13

《云计算与边缘计算协同九大应用场景》

预加工服务，为云端规避了多种采集设备带来的多源异构数据问题。

图 6 云边协同在石油行业的应用

云边协同中，要求终端设备或者传感器具备一定的计算能力，能

够对采集到的数据进行实时处理，进行本地优化控制，故障自动处理，

负荷识别和建模等操作，把加工汇集后的高价值数据与云端进行交互，

云端进行全网的安全和风险分析，进行大数据和人工智能的模式识别、

节能和策略改进等操作。同时，如果遇到网络覆盖不到的地区，可以

先在边缘侧进行数据处理，在有网络的情况下将数据上传到云端，云

端进行数据存储和分析。

3. 典型案例

浪潮云的智慧能源管理解决方案，通过智能物联网网关连接终端

感知层的水表、电表、燃气表、热能表等设备，在边缘侧实时采集设

备数据，对数据进行处理，可以实现对终端设备的能搞管理、安全预

警、无功补偿等操作，并可以进行断点续传；同时将分析后的数据上

14

《云计算与边缘计算协同九大应用场景》

传到云端，云端对数据进行表码分析、用量分析、需量分析等大数据

处理，同时存储大量数据，用户可以随时查询历史记录。

（四）云边协同在智能家庭场景中的应用

1. 智能化信息服务逐步进家入户

随着信息化技术的逐步发展、网络技术的日益完善、可应用网络

载体的日益丰富和大带宽室内网络入户战略的逐步推广，智能化信息

服务进家入户成为可能。智慧家庭综合利用互联网技术、计算机技术、

遥感控制技术等，将家庭局域网络、家庭设备控制、家庭成员信息交

流等家庭生活有效结合，创造出舒适、便捷、安全、高效的现代化家

居生活。

2. 云边协同赋予智能家庭新内涵

在家庭智能化信息服务进家入户的今天，各种异构的家用设备如

何简单地接入智能家庭网络，用户如何便捷地使用智能家庭中的各项

功能成为关注焦点。

在智能家庭场景中，边缘计算节点（家庭网关、智能终端）具备

各种异构接口，包括网线、电力线、同轴电缆、无线等等，同时还可

以对大量异构数据进行处理，再将处理后的数据统一上传到云平台。

用户不仅仅可以通过网络连接边缘计算节点，对家庭终端进行控制，

还可以通过访问云端，对长时间的数据进行访问。

同时，智能家庭云边协同基于虚拟化技术的云服务基础设施，以

多样化的家庭终端为载体，通过整合已有业务系统，利用边缘计算节

15

《云计算与边缘计算协同九大应用场景》

点将包括家用电器、照明控制、多媒体终端、计算机等家庭终端组成

家庭局域网。边缘计算节点再通过互联网（未来 5G 时代还会通过 5G

移动网络）与广域网相连，继而与云端进行数据交互，从而实现电器

控制、安全保护、视频监控、定时控制、环境检测、场景控制、可视

对讲等功能。

未来，智能家庭场景中云边协同将会越来越得到产业链各方的重

视，电信运营商、家电制造商、智能终端制造商等都会在相应的领域

进行探索。在不远的将来，家庭智能化信息服务业不仅仅限于对于家

用设备的控制，家庭能源、家庭医疗、家庭安防、家庭教育等产业也

将于家庭智能化应用紧密结合，成为智能家庭大家族中的一员。

图 7 云边协同在智慧家庭信息化中的应用示意图

3. 典型案例

16

《云计算与边缘计算协同九大应用场景》

小米家居智能防盗方案，通过门窗传感器+网关+智能摄像头的

组合，当门、窗推开，两只传感器错位，网关发出高分贝警报声同时

闪烁红色警报光，智能摄像头开启，录像上传云端，同时通知手机 App；

警报声如果没有吓走入侵者，智能摄像头拍摄画面稍有异动（比如画

面中闪过一个人影），立刻抓拍前后十五秒录像上传云端保存，实时

记录家庭内部情况。

（五）云边协同在智慧交通场景中的应用

1. 智慧交通发展中的痛点

城市交通系统是一个复杂而巨大的系统，如何提高整个交通系统

效率、提升居民出行品质是智慧交通最重要的关注点和挑战。在传统

模式中，创新技术如何从实验室中落地到实际的交通应用中、各种传

感器和终端设备标准如何统一规范、信息如何共享、大量生成数据如

何及时进行处理等已经成为制约智慧交通发展的瓶颈。

2. 智慧交通借助云边协同向车路协同发展

车路协同，是智慧交通的重要发展方向。车路协同系统是采用先

进的无线通信和新一代互联网等技术，全方位实施车车、车路动态实

时信息交互，并在全时空动态交通信息采集与融合的基础上开展车辆

主动安全控制和道路协同管理，充分实现人车路的有效协同，保证交

通安全，提高通行效率，从而形成的安全、高效和环保的道路交通系

统。据公安部统计，截至 2018 年底，我国汽车保有量已突破 2.4 亿

辆，汽车驾驶人达到 3.69 亿人。可以预见，车路协同在我国有巨大

17

《云计算与边缘计算协同九大应用场景》

的市场空间，这为智慧交通在我国的发展和落地提供了得天独厚的

“试验场”。

过去各方对于智慧交通的关注点主要集中在车端，例如自动驾驶，

研发投入也主要在车的智能化上，这对于车的感知能力和计算能力提

出了很高的要求，导致智能汽车的成本居高不下。另一方面，在当前

的技术条件下，自动驾驶车辆在传统道路环境中的表现仍然不尽人意。

国内外各大厂商逐渐意识到，路侧智能对于实现智慧交通是不可或缺

的，因此最近两年纷纷投入路侧的智能化，目标是实现人、车、路之

间高效的互联互通和信息共享。

在实际应用中，边缘计算可以与云计算配合，将大部分的计算负

载整合到道路边缘层，并且利用 5G、LTE-V 等通信手段与车辆进行

实时的信息交互。未来的道路边缘节点还将集成局部地图系统、交通

信号信息、附近移动目标信息和多种传感器接口，为车辆提供协同决

策、事故预警、辅助驾驶等多种服务。与此同时，汽车本身也将成为

边缘计算节点，与云边协同相配合为车辆提供控制和其他增值服务。

汽车将集成激光雷达、摄像头等感应装置，并将采集到的数据与道路

边缘节点和周边车辆进行交互，从而扩展感知能力，实现车与车、车

与路的协同。云计算中心则负责收集来自分布广泛的边缘节点的数据，

感知交通系统的运行状况，并通过大数据和人工智能算法，为边缘节

点、交通信号系统和车辆下发合理的调度指令，从而提高交通系统的

运行效率，最大限度的减少道路拥堵。

18

《云计算与边缘计算协同九大应用场景》

图 8 云边协同与车路协同参考框架

3. 典型案例

中兴通讯将视频监控与智能车辆控制相结合，在广西联通（柳州）

部署了自动泊车系统。此系统通过摄像头在停车场采集视频图像，由

边缘云上的第三方应用进行视频本地处理和识别。识别后的车辆信息

和位置信息通过电信核心网回传到部署有车辆控制应用的中心云中

进行车辆泊车控制处理。无需在摄像头本地进行视频处理和分析，也

不用全部视频回传到后端服务器处理。从而达到降低摄像头成本，提

升传输和控制效率的目的。

19

《云计算与边缘计算协同九大应用场景》

图 9 中兴通讯自动泊车系统

（六）云边协同在安防监控场景中的应用

1. 安防监控传统部署方式对网络和成本的要求较高

目前安防监控领域，从部署安装角度，一般传统的监控部署采用

有线方式，有线网络覆盖全部的摄像头，布线成本高，效率低，占用

大量有线资源。采用 WiFi 回传的方式，WiFi 稳定性较差，覆盖范围

较小，需要补充大量路由节点以保证覆盖和稳定性。传统方式下需要

将监控视频通过承载网和核心网传输至云端或服务器进行存储和处

理，不仅加重了网络的负载，业务的端到端时延也难以得到有效的保

障。

同时，大量的摄像采集终端都配备较强的数据采集能力，一方面

对摄像头的整体架构提出了较高的要求，如何在尺寸体积固定和耗电

量较低的情况下，保证处理能力和便捷安装，同时另一方面又尽可能

的保障摄像采集端成本较低，是一个比较重要的问题。

2. 云边协同引领安防智能化技术潮流

基于上述诉求，可以将监控数据分流到边缘计算节点（边缘计算

20

《云计算与边缘计算协同九大应用场景》

业务平台），从而有效降低网络传输压力和业务端到端时延。此外，

视频监控还可以和人工智能相结合，在边缘计算节点上搭载 AI 人工

智能视频分析模块，面向智能安防、视频监控、人脸识别等业务场景，

以低时延、大带宽、快速响应等特性弥补当前基于 AI 的视频分析中

产生的时延大、用户体验较差的问题，实现本地分析、快速处理、实

时响应。云端执行 AI 的训练任务，边缘计算节点执行 AI 的推论，二

者协同可实现本地决策、实时响应，可实现表情识别、行为检测、轨

迹跟踪、热点管理、体态属性识别等多种本地 AI 典型应用。

图 10 智能安防系统云边协同应用示意图

3. 典型案例

华为云基于IEF平台的安平监控系统，通过在边缘的视频预分析，

实现园区、住宅、商超等视频监控场景实时感知异常事件，实现事前

布防、预判，事中现场可视、集中指挥调度，事后可回溯、取证等业

务优势。边缘侧视频预分析，结合云端的智能视频分析服务，精准定

21

《云计算与边缘计算协同九大应用场景》

位可疑场景、事件，不需要人工查询大量监控数据，效率高；通过云

端可对边缘应用全生命周期进行管理，降低运维成本。

图 11 云边协同在华为 IEF 平台中实现方式

（七）云边协同在农业生产场景中的应用

1. 传统农业信息化发展痛点和趋势

农业是万亿级规模的市场，但现阶段，我国农业信息化发展相对

较为落后，信息化程度低。采集信息和数据不准确，带宽和网络覆盖

不全面、生产风险不可控是传统农业信息化发展的三大痛点。

未来农业信息化必将向智慧农业进行转型，以云计算、大数据、

人工智能等计算机技术和互联网技术为基础的网络化、智能化、数字

化农业将成为农业信息化发展的趋势。

2. 云边协同加速传统农业向智慧农业转型

22

《云计算与边缘计算协同九大应用场景》

智慧农业是农业生产的高级阶段，是集新兴的互联网、移动互联

网、云计算和物联网技术为一体，依托部署在农业生产现场的各种传

感节点和无线通信网络实现农业生产环境的智能感知、智能预警、智

能决策、智能分析、专家在线指导，为农业生产提供精准化种植、可

视化管理、智能化决策。

以智慧大棚为例：针对条件较好的大棚，安装有电动卷帘，排风

机，电动灌溉系统等机电设备，通过云端可实现远程控制功能。农户

可通过手机或电脑登录云端系统，控制温室内的水阀、排风机、卷帘

机的开关；也可在云端设定好控制逻辑，云端将控制逻辑下放到边缘

控制设备，边缘控制设备通过传感设备实时采集大棚环境的空气温度、

空气湿度、二氧化碳、光照、土壤水分、土壤温度、棚外温度与风速

等数据，自动根据内外情况自动开启或关闭卷帘机、水阀、风机等大

棚机电设备。

图 12 云边协同在智慧大棚应用示意图

3. 典型案例

23

《云计算与边缘计算协同九大应用场景》

阿里云智能大田作物种植解决方案，通过运用物联网和云计算技

术，可实时远程获取大田种植园的相关信息，通过网络传输到云端，

经过作物生长模型分析适宜作物生长的各种条件，云端将分析后的模

型下放到智能网关，智能网关根据模型和传感器采集到信息，对大田

种植园中的各种设备实时进行控制，保证种植园的空气温湿度、土壤

水分、土壤温度、CO2 浓度、光照强度。同时云边协同还可以根据作

物长势或病虫草害情况，有效降低劳动强度和生产成本，减少病害发

生，提升农产品品质和经济效益。

（八）云边协同在医疗保健场景中的应用分析

1. 医疗保健行业发展迅速后的担忧

在国家政策的大力支持下，我国医疗健康产业未来市场前景广阔，

产业规模从 2011 年的 1.6 万亿元增长至 2015 年的 3.9 万亿元，

2016 年市场规模已经达到 4.6 万亿元，同比增长约 18%，2017 年

市场规模已经达到 5.1 万亿元。根据《“健康中国 2030”规划纲要》

指出，预计到 2020 年，我国健康产业总规模将超过 8 万亿元。

在医疗保健行业迅速发展的背后，在医疗信息化互联互通还存在

着一些担忧。信息集成困难，如何全面集成门诊、急诊、住院、查体

等不同类型的就诊记录，如何长期管理和再现历史数据成为了难以实

现的目标。智能化效果堪忧，医院的智能化服务水平偏低，复杂多样

的智能化规则和电子病历无法集成，多种人工智能产品无法纳入工作

流程。数据收集困难，工作量大、数据缺失、数据共享困难，业务系

24

《云计算与边缘计算协同九大应用场景》

统与科研数据采集难以统一等数据收集方面也成为了拦路虎。

2. 医疗保健未来发展方向

随着医疗设备在人们日常保健应用比例的提高，产品在不断降低

成本的同时，最值得关注的还是安全性、可靠性、易用性等人性化要

求。医疗保健行业中患者的数据是极为隐私的，必须加以安全保护。

另一方面，诊断精度直接影响设备诊断结果，这必然也是未来产品设

计的关注重点。

随着社会的进步和人口结构的改变，医疗保健的发展主要呈现出

三个特点：便携化、智能化和多功能化。便携式移动医疗、大数据分

析、云服务等智能医疗迎来发展热潮并在个体群体之间不断创新。

3. 云边协同助力医疗保健向智能医疗升级

目前，用于生命体征监测的可穿戴设备正在快速发展，其中低功

耗、小尺寸和设计简单性已经成为方案设计中的关键所在。包括智能

手表在内的腕戴式健身和健康设备越来越受欢迎，这些设备不仅具有

步进跟踪功能，而且提供相关的健身/健康指标，包括受力分析参数

以及基础心率和心率变异分析等功能。但是，要真正地从所收集的海

量数据中获益，实时分析可能是必不可少的——许多的可穿戴设备直

接连接到云上，但也有其他的一些设备支持离线运行。一些可穿戴健

康监控器可以在不连接云的情况下本地分析脉搏数据或睡眠模式。然

后，医生可以远程对病人进行评估，并就病人的健康状况提供即时反

馈。

25

《云计算与边缘计算协同九大应用场景》

图 13 云边协同在医疗保健中的应用

例如，能够独立分析健康数据的心率监视器可以立即提供必要的

响应，以在患者需要帮助时提醒护理者。同时监视器将分析后的数据

上传到云端，在云端进行 AI 分析，记录患者长期的健康情况，为医

生和患者提供病情分析，辅助进行下阶段治疗。

同时，机器人辅助手术是医疗保健中云边协同的另一个用例，这

些机器人需要能够自己分析数据，以便安全、快速和准确地为手术提

供帮助；同时将数据上传到云端，在云端进行 AI 学习，完善机器人

程序，并在适当时机将学习完成的模型下发到机器人终端。

（九）云边协同在云游戏场景中的应用分析

1. 游戏市场火热背后的隐患

根据 Newzoo2018 全球游戏市场报告显示，2018 年全球游戏

市场达到 1379 亿美元的市场规模，其中中国达到 379 亿美元，占全

球游戏收入的28%，从2017年至2021年，全球游戏市场将以10.3％

的复合年增长率增长，到2021年将达到1801亿美元。同时，Newzoo

26

《云计算与边缘计算协同九大应用场景》

还指出到 2021 年，移动游戏将占据全球游戏市场 59％的份额，并

将本身成为一个价值千亿美元的市场。

虽然游戏市场持续火热，但游戏厂商和游戏玩家对未来游戏的发

展也存有相当大的担心。越来越高的游戏设备要求和价格，现在游戏

画面越来越丰富、逼真，动辄几十 G 的游戏容量，更有甚者需要上百

G 的硬盘来安装游戏；同时，高端的视频处理器和音频处理器也是必

备装备，以《最终幻想 15》为例，玩家想要充分体验游戏质感，需要

使用最高端的 RTX 2080Ti 显卡，而该显卡的价格为近万元，为一款

游戏而更换终端配置的情况并不是所有玩家都可以接受的。游戏流量

不断变化，游戏领域的特点是：快速开发，用户爆发量大，生命周期

短，传统的游戏做法是预先部署服务器，那么结果就是要么用户数不

足而导致 IT 资源闲置，要么用户数激增导致短时间（一两周内）服

务器已经满负荷运转，两种结果都是游戏厂商无法接受的。游戏终端

之间不兼容，现在的游戏终端越来越多，一款游戏想同时在电视、电

脑、平板、手机、AR/VR 等终端上进行体验是游戏厂商和玩家的共同

愿望。

2. “云游戏”迅猛发展

随着互联网的发展，以及 5G 网络已经成为现实的今天，“云游

戏”这个名词也开始被越来越多的厂商利用，同时也被越来越多的玩

家所期待。 所谓“云游戏”，就是所有游戏都在云端服务器中运行，

云端将渲染完毕后的游戏画面压缩后通过网络传送给用户发送到终

端。在终端，用户的游戏设备不需要任何高端处理器和显卡，只需要

27

《云计算与边缘计算协同九大应用场景》

具备基本的视频解压和指令转发功能即可。

在 2018 年，已经有 AT&T、Verizon 等电信巨头以及微软、亚

马逊等 IT 巨头先后公布了云游戏相关的测试或者布局。在 2019 年

的 MWC 上，国内手机厂商 OPPO 和一加也分别展示了相关的云游

戏服务。根据第三方机构的预测，全球云游戏市场将从 2018 年的

0.66 亿美元增加到 2023 年的 4.5 亿美元，复合年均增长率为 47％。

3. 云边协同助力云游戏实现升级

以 AR 为例，应用程序需要通过相机的视图、定位技术或将两者

结合起来，判断用户处于哪个位置以及面向哪个方向。对位置和方向

信息加以分析之后，应用程序可以实时向用户提供其他信息。而当用

户移动后，需要刷新该信息。边缘计算将计算任务卸到边缘服务器或

移动端上，降低平均处理的延时。前景的交互放在云上，背景则交给

移动端，最终实现完整的 AR 体验。

再以大家耳熟能详的 LOL、守望先锋、王者荣耀，再到现在的绝

地求生、APEX 等，“多人游戏多人竞争”似乎成为近几年游戏的风

向。而多人游戏对带宽和延迟的要求可不是一般的高。可以通过应用

云边协同，同一区域的玩家通过连接一个边缘计算节点，大大减少他

们的延迟；同时云端运行游戏，减少本地游戏体积，使玩家可以方便、

快捷地接入游戏。

28

《云计算与边缘计算协同九大应用场景》

图 14 云边协同在云游戏中应用框图

三、云边协同发展建议

（一）以应用为导向 稳步推进计算能力向边缘侧下沉

当前边缘计算的概念过热，各类靠近用户侧的产品和业务都容易

被冠以边缘计算的帽子，实际上并不利于边缘计算的发展，也不利于

云边协同的尽快推进。建议产业界仍应保持理性，从典型场景的业务

需求出发，在企业上云的大环境下，综合成本因素和实际效果，逐步

探索将部分计算能力下沉到边缘侧，切忌过于着急将计算能力边缘化

部署。

（二）加快标准体系建设 注重协同能力规范

加快云边协同标准体系建设。目前针围绕边缘云等边缘侧的标准

已经有所考虑，但针对云边协同的标准仍处于缺位状态。建议相关研

究机构从整体布局之初，就将中心云与边缘侧的协同框架进行标准化

29

《云计算与边缘计算协同九大应用场景》

设计。可针对不同应用场景，完善云边协同应用场景能力要求的标准

体系，加快制定相关协同技术、服务和应用标准，引导企业提升云边

协同服务水平，保障云边协同健康发展。

只有在有效协同云计算与边缘计算二者的前提下，才能满足部分

场景在敏捷连接、实时业务、数据优化、安全与隐私保护等方面的计

算需求。

图 15 云边协同参考框架

云边协同参考框架主要涉及云计算和边缘计算节点在基础设施、

平台、应用三个层面的全面协同， 基础设施层面主要指，IaaS 与

ECIaaS 之间需要实现计算、网络、存储等方面的资源协同；平台层

面主要指，PaaS 与 ECPaaS 之间需要实现数据协同、智能协同、服

务编排协同和部署协同；应用层面主要指，SaaS 与 ECSaaS 之间需

30

《云计算与边缘计算协同九大应用场景》

31

要实现应用服务协同。除此之外，云边协同在资源、平台、应用的基

础上面还需要考虑计费、运维、安全等方面的协同。

《云计算与边缘计算协同九大应用场景》

云计算开源产业联盟

2019 年 7 月

32

