# coding=utf-8
"""
测试KWIC分析 - 非交互式版本
"""
import jieba
import os
import sys
import pandas as pd
import re
from collections import defaultdict

sys.stdout.reconfigure(encoding='utf-8')

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip() for line in f if line.strip()])
    return stopwords

def clean_text(text):
    """清理文本，保留原始结构用于KWIC"""
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def preprocess_folder(folder_path, stopwords_file):
    """预处理文件夹中的所有文件"""
    stopwords = load_stopwords(stopwords_file)
    all_docs = {}
    word_freq = defaultdict(int)
    
    print(f"开始预处理文件夹: {folder_path}")
    
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                text = clean_text(text)
                words = jieba.cut(text, cut_all=False)
                for word in words:
                    word = word.strip()
                    if (len(word) >= 2 and 
                        word not in stopwords and
                        not word.isdigit() and
                        not word.isspace()):
                        word_freq[word] += 1
                
                all_docs[filename] = text
                print(f"已处理: {filename}")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    return all_docs, word_freq

def kwic_search(all_docs, keyword, window_size=5):
    """执行KWIC搜索"""
    results = []
    
    print(f"搜索关键词: '{keyword}'，窗口大小: {window_size}")
    
    for filename, text in all_docs.items():
        words = list(jieba.cut(text, cut_all=False))
        
        for i, word in enumerate(words):
            if keyword in word or word == keyword:
                start_idx = max(0, i - window_size)
                end_idx = min(len(words), i + window_size + 1)
                
                left_context = ''.join(words[start_idx:i])
                right_context = ''.join(words[i+1:end_idx])
                
                left_context = left_context.strip()
                right_context = right_context.strip()
                
                results.append({
                    'file': filename,
                    'left': left_context,
                    'keyword': word,
                    'right': right_context,
                    'position': i
                })
    
    return results

def main():
    """主函数"""
    folder_path = "../中文文本"
    stopwords_file = "../stopwords-zh.txt"
    keyword = "人工智能"  # 固定关键词
    window_size = 5
    
    print("=== 中文KWIC分析测试 (jieba库) ===")
    
    # 预处理文件
    all_docs, word_freq = preprocess_folder(folder_path, stopwords_file)
    
    if not all_docs:
        print("未找到有效的文档")
        return
    
    # 执行搜索
    results = kwic_search(all_docs, keyword, window_size)
    
    # 显示结果
    if results:
        print(f"\n找到 {len(results)} 处匹配")
        
        # 保存结果
        df = pd.DataFrame(results)
        filename = f"jieba_KWIC_{keyword}.csv"
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"结果已保存至: {filename}")
        
        # 显示前10个结果
        print(f"\n前10个匹配结果:")
        for i, result in enumerate(results[:10]):
            print(f"{i+1}. {result['left']} [{result['keyword']}] {result['right']}")
    else:
        print(f"未找到关键词 '{keyword}' 的匹配结果")

if __name__ == "__main__":
    main()
