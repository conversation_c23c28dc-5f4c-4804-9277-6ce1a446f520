# coding=utf-8
import jieba
import pandas as pd
import sys
import os
from collections import defaultdict
import nltk
from nltk.tokenize import word_tokenize

sys.stdout.reconfigure(encoding='utf-8')
nltk.download('punkt')  # 下载nltk分词所需的数据

# 加载停用词
def load_stopwords(stopwords_file=None):
    """
    加载停用词列表
    Args:
        stopwords_file: 停用词文件路径，如果为None则使用默认停用词
    Returns:
        停用词集合
    """
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip() for line in f])
    # 添加基本标点符号作为停用词
    stopwords.update(['，', '。', '！', '？', '、', '；', '：', '"', '"', ''', ''', '（', '）', '【', '】', '.', ',', '!', '?', ';', ':', '(', ')', '[', ']'])
    return stopwords

def is_chinese(text):
    """判断文本是否主要为中文"""
    for char in text:
        if '\u4e00' <= char <= '\u9fa5':
            return True
    return False

def process_files(folder_path, stopwords_file=None):
    """
    处理文件夹中的所有txt文件，返回词频统计和文件分布信息
    Args:
        folder_path: 文件夹路径
        stopwords_file: 停用词文件路径
    Returns:
        词频统计信息
    """
    word_info = defaultdict(lambda: {'frequency': 0, 'files': set()})
    stopwords = load_stopwords(stopwords_file)

    # 遍历文件夹中的所有txt文件
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            with open(filepath, 'r', encoding='utf-8') as f:
                text = f.read()

            if is_chinese(text):
                # 使用jieba进行中文分词
                tokens = [word for word in jieba.cut(text)
                         if word not in stopwords
                         and len(word.strip()) > 0]
            else:
                # 使用nltk进行英文分词
                tokens = [word.lower() for word in word_tokenize(text) # 添加了lower()，使英文单词统一为小写，避免重复统计。
                          if word.lower() not in [sw.lower() for sw in stopwords]
                          and len(word.strip()) > 0]

            # 更新统计信息
            unique_words = set(tokens)
            for word in tokens:
                word_info[word]['files'].add(filename)
                word_info[word]['frequency'] += 1

    return word_info

def generate_report(word_info):
    """
    生成包含词频、排名、分布和文件列表的报告
    """
    # 转换为DataFrame
    data = []
    for word, info in word_info.items():
        data.append({
            'Word': word,
            'Frequency': info['frequency'],
            'Range': len(info['files']),
            'Files': ', '.join(sorted(info['files']))
        })

    df = pd.DataFrame(data)

    # 按频率排序并添加排名
    df = df.sort_values(by='Frequency', ascending=False)
    df = df.reset_index(drop=True)
    df.insert(1, 'Rank', df.index + 1)

    return df

def main():
    folder_path = r"英文新闻"  # 修改为你的文件夹路径
    stopwords_file = r"stopwords-zh.txt"  # 可选：指定停用词文件路径
    output_file = 'mixed_language_word_analysis_report.csv' #修改输出文件名。

    # 处理文件并生成报告
    word_info = process_files(folder_path, stopwords_file)
    report_df = generate_report(word_info)

    # 打印前20个结果
    print(report_df.head(20))

    # 保存完整结果到CSV
    report_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n完整分析结果已保存至 {output_file}")

if __name__ == "__main__":
    main()