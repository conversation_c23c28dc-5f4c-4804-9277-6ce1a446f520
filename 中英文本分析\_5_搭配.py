# coding=utf-8
import os
import sys
import csv
import math
import time
import psutil
import jieba
from collections import defaultdict
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm
import nltk
from nltk.tokenize import word_tokenize

sys.stdout.reconfigure(encoding='utf-8')
nltk.download('punkt')

def load_stopwords(stopwords_file=None):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip() for line in f])
    stopwords.update(['，', '。', '！', '？', '、', '；', '：', '"', '"', ''', ''', '（', '）', '【', '】', '.', ',', '!', '?', ';', ':', '(', ')', '[', ']'])
    return stopwords

def is_chinese(text):
    """判断文本是否主要为中文"""
    for char in text:
        if '\u4e00' <= char <= '\u9fa5':
            return True
    return False

# 可序列化的全局函数
def default_collocate_stats():
    return {'count': 0, 'left': 0, 'right': 0, 'files': set()}

def default_file_stats():
    return {'left': 0, 'right': 0}

class CollocateAnalyzer:
    def __init__(self, folder_path, stopwords_file=None):
        self.folder_path = folder_path
        self.files = self._filter_files()  # 过滤大文件
        self.total_files = len(self.files)
        self.corpus_stats = defaultdict(default_collocate_stats)
        self._word_freq_cache = None
        self.total_tokens = 0
        self.memory_threshold = 0.75  # 内存警戒阈值
        self.chunk_size = 100_000  # 分块读取大小
        self.stopwords = load_stopwords(stopwords_file)

    def _filter_files(self):
        """过滤超过50MB的大文件"""
        valid_files = []
        for f in os.listdir(self.folder_path):
            if f.endswith('.txt'):
                fp = os.path.join(self.folder_path, f)
                if os.path.getsize(fp) < 50_000_000:  # 50MB限制
                    valid_files.append(f)
        return sorted(valid_files, key=lambda x: x.lower())

    @staticmethod
    def _preprocess(text, stopwords):
        """优化后的预处理函数"""
        if is_chinese(text):
            return [
                word for word in jieba.cut(text)
                if word not in stopwords and len(word.strip()) > 0
            ]
        else:
            return [word.lower() for word in word_tokenize(text)
                    if word.lower() not in [sw.lower() for sw in stopwords]
                    and len(word.strip()) > 0]

    @staticmethod
    def _process_chunk(tokens, target, left_win, right_win):
        """处理文本块的核心逻辑"""
        file_stats = defaultdict(default_file_stats)
        target_indices = [i for i, t in enumerate(tokens) if t == target]

        for idx in target_indices:
            # 左边窗口
            start = max(0, idx - left_win)
            for i in range(start, idx):
                file_stats[tokens[i]]['left'] += 1

            # 右边窗口
            end = min(len(tokens), idx + right_win + 1)
            for i in range(idx + 1, end):
                file_stats[tokens[i]]['right'] += 1

        return file_stats, len(target_indices)

    @staticmethod
    def _process_file(args):
        """改进后的文件处理方法"""
        folder_path, target, left_win, right_win, filename, stopwords = args
        filepath = os.path.join(folder_path, filename)

        total_tokens = 0
        target_count = 0
        file_stats = defaultdict(default_file_stats)

        with open(filepath, 'r', encoding='utf-8') as f:
            while True:
                text_chunk = f.read(100_000)  # 每次读取100KB
                if not text_chunk:
                    break

                tokens = CollocateAnalyzer._preprocess(text_chunk, stopwords)
                total_tokens += len(tokens)

                chunk_stats, chunk_target = CollocateAnalyzer._process_chunk(
                    tokens, target, left_win, right_win
                )
                target_count += chunk_target

                # 合并统计结果
                for collocate, stats in chunk_stats.items():
                    file_stats[collocate]['left'] += stats['left']
                    file_stats[collocate]['right'] += stats['right']

        return filename, file_stats, total_tokens, target_count

    def _check_memory(self):
        """内存使用检查"""
        mem = psutil.virtual_memory()
        if mem.percent > self.memory_threshold * 100:
            print(f"\n内存使用过高 ({mem.percent}%)，暂停处理10秒...")
            time.sleep(10)

    def calculate_log_likelihood(self, O11, O12, O21, O22):
        """对数似然比计算"""
        try:
            total = O11 + O12 + O21 + O22
            if total == 0:
                return 0.0

            E11 = (O11 + O12) * (O11 + O21) / total
            E12 = (O11 + O12) * (O12 + O22) / total
            E21 = (O21 + O22) * (O11 + O21) / total
            E22 = (O21 + O22) * (O12 + O22) / total

            llr = 0.0
            for observed, expected in [(O11, E11), (O12, E12), (O21, E21), (O22, E22)]:
                if observed > 0 and expected > 0:
                    llr += observed * math.log(observed / expected)
            return 2 * llr
        except:
            return 0.0

    def calculate_effect_size(self, O11, R1, C1, N):
        """效应值计算"""
        try:
            p = O11 / R1 if R1 != 0 else 0.0
            q = (C1 - O11) / (N - R1) if (N - R1) != 0 else 0.0
            return math.log(p / q) if q != 0 else 0.0
        except:
            return 0.0

    def analyze(self, target_word, left_win, right_win):
        """带内存监控的分析方法"""
        args = [(self.folder_path, target_word, left_win, right_win, f, self.stopwords)
               for f in self.files]

        max_workers = max(1, os.cpu_count() // 2)
        results = []

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(self._process_file, arg) for arg in args]

            with tqdm(total=len(futures), desc="分析文件") as pbar:
                for future in as_completed(futures):
                    self._check_memory()
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        print(f"\n文件处理错误: {str(e)}")
                    finally:
                        pbar.update(1)

        # 合并统计结果
        total_target = 0
        total_tokens = 0
        for filename, file_stats, token_count, target_count in results:
            total_tokens += token_count
            total_target += target_count

            for collocate, counts in file_stats.items():
                self.corpus_stats[collocate]['left'] += counts['left']
                self.corpus_stats[collocate]['right'] += counts['right']
                self.corpus_stats[collocate]['files'].add(filename)
                self.corpus_stats[collocate]['count'] += (counts['left'] + counts['right'])

        # 生成报告
        report = []
        N = total_tokens
        C1 = total_target

        for collocate, stats in self.corpus_stats.items():
            O11 = stats['count']
            R1 = self.word_freq(collocate)

            if R1 == 0:  # 跳过未出现的词汇
                continue

            O12 = C1 - O11
            O21 = R1 - O11
            O22 = N - O11 - O12 - O21

            log_likelihood = self.calculate_log_likelihood(O11, O12, O21, O22)
            effect_size = self.calculate_effect_size(O11, R1, C1, N)

            report.append({
                'collocate': collocate,
                'freqL': stats['left'],
                'freqR': stats['right'],
                'range': len(stats['files']),
                'likelihood': log_likelihood,
                'effect': effect_size,
                'files': ';'.join(sorted(stats['files']))
            })

        # 排序和添加排名
        report.sort(key=lambda x: (-x['likelihood'], -x['effect']))
        for rank, item in enumerate(report, 1):
            item['rank'] = rank

        return report

    def word_freq(self, word):
        """带缓存的词频统计"""
        if self._word_freq_cache is None:
            self._word_freq_cache = defaultdict(int)
            for filename in tqdm(self.files, desc="构建词频缓存"):
                filepath = os.path.join(self.folder_path, filename)
                with open(filepath, 'r', encoding='utf-8') as f:
                    tokens = self._preprocess(f.read(), self.stopwords)
                    for token in tokens:
                        self._word_freq_cache[token] += 1
        return self._word_freq_cache.get(word, 0)

    def interactive_mode(self):
        """交互模式优化版"""
        target = input("请输入目标词：").strip()
        left_win = int(input("左边窗口大小（词数）：").strip())
        right_win = int(input("右边窗口大小（词数）：").strip())

        start_time = time.time()
        report = self.analyze(target, left_win, right_win)

        # 显示摘要
        print(f"\n分析完成，耗时 {time.time() - start_time:.1f} 秒")
        print(f"共发现 {len(report)} 个有效搭配词")

        # 显示前20结果
        print("\n{:<15} {:<6} {:<8} {:<8} {:<8} {:<10} {:<10} {}".format(
            '搭配词', '排名', '左频', '右频', '范围', '对数似然比', '效应值', '文件'))
        print("-" * 90)
        for item in report[:20]:
            print("{:<15} {:<6} {:<8} {:<8} {:<8} {:<10.2f} {:<10.2f} {}".format(
                item['collocate'][:14],
                item['rank'],
                item['freqL'],
                item['freqR'],
                item['range'],
                item['likelihood'],
                item['effect'],
                item['files'][:30] + '...' if len(item['files']) > 30 else item['files']
            ))

        # 保存结果
        save = input("\n是否保存结果到CSV文件？(y/n): ").lower()
        if save == 'y':
            filename = f"collocate_{target}.csv"
            with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=['collocate', 'rank', 'freqL', 'freqR',
                                                     'range', 'likelihood', 'effect', 'files'])
                writer.writeheader()
                writer.writerows(report)
            print(f"结果已保存至 {filename}")

if __name__ == "__main__":
    folder_path = r"英文新闻"
    stopwords_file = r"stopwords-zh.txt"  # 可选：指定停用词文件路径
    analyzer = CollocateAnalyzer(folder_path, stopwords_file)
    analyzer.interactive_mode()