云计算发展白皮书

（2020 年）

中国信息通信研究院

2020年7月

版权声明

本白皮书版权属于中国信息通信研究院，并受法律保护。

转载、摘编或利用其它方式使用本白皮书文字或者观点的，

应注明“来源：中国信息通信研究院”。违反上述声明者，本

院将追究其相关法律责任。

前 言

在刚刚结束的全国“两会”中，“新基建”首次写入政府工作报告。

“新基建”不仅有助于扩内需、促消费、稳增长，还将为产业发展注

入数字动力，成为促进经济增长的新动能。随着“新基建”的推进，

云计算必将加快应用落地进程，在各个行业实现快速发展。

本白皮书是继《云计算白皮书（2012 年）》之后，中国信息通信

研究院第 6 次发布云计算白皮书。本白皮书在前几版的基础上，聚

焦过去一年多来云计算产业的发展变化，梳理当前发展热点，展望

未来发展趋势。白皮书首先介绍了云计算产业发展概况，然后重点

围绕云原生、SaaS、分布式云、原生云安全、数字化转型、新基建

等云计算领域热点话题进行探讨，最后对云计算未来发展进行了展

望。

目 录

一、云计算产业发展概况............................................................................................1

（一）全球云计算市场稳定增长，我国公有云规模首超私有云.................... 1

（二）我国 IaaS 发展成熟，PaaS 增长高速，SaaS 潜力巨大........................3

（三）云技术不断推陈出新，云原生采纳率持续攀升.....................................5

（四）云计算使用率持续提升，分布式云初露头角.........................................5

（五）安全能力备受关注，原生云安全理念兴起.............................................7

（六）降本增效显著，云计算成数字化转型关键要素.....................................7

（七）利好政策不断加码，云计算成新基建重要组成.....................................8

二、云原生技术体系日臻成熟，构建数字中台底座................................................9

（一）云原生重塑中间件产品...........................................................................10

（二）云原生如何更好的服务上层应用成焦点...............................................11

（三）云原生助力数字中台建设.......................................................................12

三、SaaS 市场开始加速，将成企业上云重要抓手................................................ 14

（一）国外 SaaS 市场模式成熟，国内 SaaS 蓄势待发..................................14

（二）疫情推动 SaaS 服务迎来发展新机遇.................................................... 16

（三）SaaS 直击企业痛点，加速中小企业应用上云..................................... 18

（四）深耕行业，SaaS 服务向平台化、智能化发展..................................... 19

四、分布式云成云计算新形态，助力行业转型升级..............................................21

（一）云计算从中心向边缘延伸.......................................................................21

（二）云边协同成为分布式云的核心...............................................................22

（三）云边协同助力行业应用转型升级...........................................................25

五、原生云安全理念兴起，推动安全与云深度融合..............................................30

（一）云原生重塑 IT 架构，端到端安全风险引关注.................................... 30

（二）原生云安全推动安全与云深度融合.......................................................33

六、数字化转型旨在提高生产力，云化能力是关键..............................................40

（一）数字化转型核心是提高生产力，传统信息基础设施亟待升级.......... 40

（二）云计算加速数字化转型，显著提升企业生产力...................................43

（三）IT 云化管理平台作用凸显，技术服务助力企业转型升级................. 45

七、云定位从基础资源向基建操作系统扩展，提升算力与网络水平................. 48

（一）新基建概念明确，云计算既是基础资源也是操作系统...................... 48

（二）云计算加速网络变革，推动通信网络基础设施优化升级.................. 53

（三）云计算加强多种算力统一调度，提高算力基础设施资源利用率...... 56

八、云计算发展展望..................................................................................................58

图 目 录

图 1 全球云计算市场规模及增速............................................................................. 2

图 2 中国公有云市场规模及增速............................................................................. 2

图 3 中国私有云市场规模及增速............................................................................. 3

图 4 中国公有云细分市场规模及增速..................................................................... 4

图 5 2019 年中国公有云 IaaS 市场份额占比............................................................ 4

图 6 云计算技术成熟度曲线..................................................................................... 5

图 7 中国云计算使用率情况..................................................................................... 6

图 8 边缘计算应用情况............................................................................................. 6

图 9 私有云安全投入占 IT 系统的比例....................................................................7

图 10 企业使用云计算降低的 IT 成本......................................................................8

图 11 企业应用云计算带来的效果............................................................................8

图 12 数字中台架构图............................................................................................. 14

图 13 可信云企业级 SaaS 评估领域分布情况....................................................... 15

图 14 分布式云架构图............................................................................................. 22

图 15 视频云行业图谱............................................................................................. 26

图 16 原生云安全架构............................................................................................. 33

图 17 可信研发运营安全体系................................................................................. 35

图 18 云服务安全性重点关注方向......................................................................... 37

图 19 企业数字基础设施云化管理示意图............................................................. 46

图 20 新基建的三方面............................................................................................. 50

图 21 云计算在新基建中的作用............................................................................. 50

图 22 信息基础设施与计算机系统的类比............................................................. 51

图 23 云计算对 5G 网络架构改造示意.................................................................. 54

图 24 多个数据中心之间的网络联接..................................................................... 56

图 25 云计算对算力基础设施的资源整合与分配................................................. 56

表 目 录

表 1 传统安全与云原生体系安全的主要区别....................................................... 30

云计算发展白皮书（2020 年）

云计算自 2006 年提出至今，大致经历了形成阶段、发展阶段和

应用阶段。过去十年是云计算突飞猛进的十年，全球云计算市场规模

增长数倍，我国云计算市场从最初的十几亿增长到现在的千亿规模，

全球各国政府纷纷制推出“云优先”策略，我国云计算政策环境日趋

完善，云计算技术不断发展成熟，云计算应用从互联网行业向政务、

金融、工业、医疗等传统行业加速渗透。

未来，云计算仍将迎来下一个黄金十年，进入普惠发展期。一是

随着新基建的推进，云计算将加快应用落地进程，在互联网、政务、

金融、交通、物流、教育等不同领域实现快速发展。二是全球数字经

济背景下，云计算成为企业数字化转型的必然选择，企业上云进程将

进一步加速。三是新冠肺炎疫情的出现，加速了远程办公、在线教育

等 SaaS 服务落地，推动云计算产业快速发展。

在此背景下，本文对云计算产业发展进行剖析，重点探讨云计算

领域热点话题并给出发展展望。

一、云计算产业发展概况

（一）全球云计算市场稳定增长，我国公有云规模首

超私有云

全球云计算市场保持稳定增长态势。2019 年，以 IaaS、PaaS 和

SaaS 为代表的全球云计算市场规模达到 1883 亿美元，增速 20.86%。

预计未来几年市场平均增长率在 18%左右，到 2023 年市场规模将超

过 3500 亿美元。

1

云计算发展白皮书（2020 年）

数据来源：Gartner，2020 年 1 月

图 1 全球云计算市场规模及增速

我国公有云市场规模首次超过私有云。2019 年我国云计算整体

市场规模达 1334 亿元，增速 38.6%。其中，公有云市场规模达到 689

亿元，相比 2018 年增长 57.6%，预计 2020-2022 年仍将处于快速增长

阶段，到 2023 年市场规模将超过 2300 亿元。私有云市场规模达 645

亿元，较 2018 年增长 22.8%，预计未来几年将保持稳定增长，到 2023

年市场规模将接近 1500 亿元。

数据来源：中国信息通信研究院，2020 年 5 月

图 2 中国公有云市场规模及增速

2

云计算发展白皮书（2020 年）

数据来源：中国信息通信研究院，2020 年 5 月

图 3 中国私有云市场规模及增速

（二）我国 IaaS 发展成熟，PaaS 增长高速，SaaS 潜

力巨大

2019 年，我国公有云 IaaS 市场规模达到 453 亿元，较 2018 年增

长了 67.4%，预计受新基建等政策影响，IaaS 市场会持续攀高；公有

云 PaaS 市场规模为 42 亿元，与去年相比提升了 92.2%，在企业数字

化转型需求的拉动下，未来几年企业对数据库、中间件、微服务等

PaaS 服务的需求将持续增长，预计仍将保持较高的增速；公有云 SaaS

市场规模达到 194 亿元，比 2018 年增长了 34.2%，增速较稳定，与

全球整体市场（1095 亿美元）的成熟度差距明显，发展空间大，2020

年受疫情影响，预计未来市场的接受周期会缩短，将加速 SaaS 发展。

3

云计算发展白皮书（2020 年）

4

数据来源：中国信息通信研究院，2020 年 5 月

图 4 中国公有云细分市场规模及增速

厂商市场份额方面。据中国信息通信研究院调查统计1，阿里云、

天翼云、腾讯云占据公有云 IaaS 市场份额前三，华为云、光环新网

（排名不分先后）处于第二集团2；阿里云、腾讯云、百度云、华为

云位于公有云 PaaS 市场前列。

阿里云

3 6 .7 % 

天翼云

1 2 .8 % 

腾讯云

1 1 .4 %

为云、光环新网

1 4 .8 %

其他

2 4 .3 % 

数据来源：中国信息通信研究院，2020 年 5 月

图 5 2019 年中国公有云 IaaS 市场份额占比

1市场规模为 2019 年的统计，主要依据企业财报、人员访谈、可信云评估、历史公开数据等得出。对于市

场数据不明确的领域，只发布头部企业整体情况，不做具体排名。

2因为 IaaS 和 CDN 是两种业态，需要分别获得互联网资源协作牌照和 CDN 牌照，所以 IaaS 市场统计不包

括 CDN（云分发）收入，只统计计算、存储、网络（不包括 CDN）、数据库等纯基础资源服务的收入。

华

云计算发展白皮书（2020 年）

5

（三）云技术不断推陈出新，云原生采纳率持续攀升

云计算技术不断推陈出新。早期，虚拟化技术凭借较高的可用性、

灵活性、扩展性等优势受到人们的追逐。然而，基于传统技术栈构建

的应用包含了太多开发需求，云端强大的服务能力红利还并没有完全

得到释放。近年来，以容器、微服务、DevOps 为代表的云原生技术，

可以为企业提供更高的敏捷性、弹性和云间的可移植性，受到了人们

的广泛关注。

2000 2005 2010 2015 2020 2025 2030

虚拟化及虚拟化管理

容器及编排

微服务、服务网格

无服务器

来源：公开资料整理

图 6 云计算技术成熟度曲线

云原生技术采纳率持续提升。中国信息通信研究院的云计算发展

调查报告显示3，2019 年 43.9%的被访企业表示已经使用容器技术部

署业务应用，计划使用容器技术部署业务应用的企业占比为 40.8%；

28.9%的企业已经使用微服务架构进行应用系统开发，另外有 46.8%

的企业计划使用微服务架构。

（四）云计算使用率持续提升，分布式云初露头角

3数据来源：中国信息通信研究院，《中国公有云发展调查报告（2020 年）》、《中国私有云发展调查报告（2020

年）》、《中国混合云发展调查报告（2020 年）》

云计算发展白皮书（2020 年）

云计算应用度持续提升。根据中国信息通信研究院的云计算发展

调查报告，2019 年我国已经应用云计算的企业占比达到 66.1%，较

2018 年上升了 7.5%。其中，采用公有云的企业占比 41.6%，较去年

提高了 5.2%；私有云占比为 14.7%，与去年相比有小幅提升；有 9.8%

的企业采用了混合云，与 2018 年相比提高了 1.7%。

数据来源：中国信息通信研究院，2020 年 5 月

图 7 中国云计算使用率情况

数据来源：中国信息通信研究院，2020 年 5 月

图 8 边缘计算应用情况

分布式云初露头角。中国信息通信研究院的云计算发展调查报告

显示，目前我国有 3.37%的企业已经应用了边缘计算；计划使用边缘

计算的企业占比达到 44.23%。随着国家在 5G、工业互联网等领域的

支持力度不断加深，预计未来基于云边协同的分布式云使用率将快速

6

云计算发展白皮书（2020 年）

增长。（五）安全能力备受关注，原生云安全理念兴起

云计算安全能力备受关注。中国信息通信研究院的云计算发展调

查报告显示，42.4%的企业在选择公有云服务商时会考虑服务安全性，

是影响企业选择的重要因素；43%的企业在私有云安全上的投入占 IT

总投入的 10%以上，较上一年度提升了 4.8%。

数据来源：中国信息通信研究院，2020 年 5 月

图 9 私有云安全投入占 IT 系统的比例

原生云安全理念开始兴起。国际上，Gartner、Forrester、Rackspace、

VMware 等研究机构和厂商纷纷提出原生安全理念；在国内，阿里云、

360 等厂商将原生安全定义为企业下一代云安全架构。根据 Forrester

《Cloud Security Solutions Forecast, 2018 To 2023》统计，2019 年全球

云平台原生安全占据了云安全市场规模的 73%。 （六）降本增效显著，云计算成数字化转型关键要素

云计算降本增效显著。中国信息通信研究院的云计算发展调查报

告显示，95%的企业认为使用云计算可以降低企业的 IT 成本，其中，

7

云计算发展白皮书（2020 年）

超过 10%的用户成本节省在一半以上。另外，超四成的企业表示使用

云计算提升了 IT 运行效率，IT 运维工作量减少和安全性提升的占比

分别为 25.8%和 24.2%。可见，云计算将成为企业数字化转型的关键

要素。

数据来源：中国信息通信研究院，2020 年 5 月

图 10 企业使用云计算降低的 IT 成本

数据来源：中国信息通信研究院，2020 年 5 月

图 11 企业应用云计算带来的效果

（七）利好政策不断加码，云计算成新基建重要组成

在前期“企业上云”工作基础上，为进一步推进企业运用新一代信

8

云计算发展白皮书（2020 年）

息技术完成数字化、智能化升级改造，工信部、发改委、网信办等部

委先后发文，鼓励云计算与大数据、人工智能、5G 等新兴技术融合，

实现企业信息系统架构和运营管理模式的数字化转型。

 2020 年 3 月 18 日，工业和信息化部印发《中小企业数字化

赋能专项行动方案》，鼓励以云计算、人工智能、大数据、

边缘计算、5G 等新一代信息技术与应用为支撑，引导数字

化服务商针对中小企业数字化转型需求，建设云服务平台、

开放数字化资源、开发数字化解决方案，为中小企业实现数

字化网络化智能化转型夯实基础。

 2020 年 4 月 7 日，国家发展改革委、中央网信办联合印发的

《关于推进“上云用数赋智”行动培育新经济发展实施方案》

中，鼓励在具备条件的行业领域和企业范围内，探索大数据、

人工智能、云计算、数字孪生、5G、物联网和区块链等新一

代数字技术应用和集成创新，为企业数字化转型提供技术支

撑。

 2020 年 4 月 20 日，国家发展改革委首次正式对“新基建”的

概念进行解读，云计算作为新技术基础设施的一部分，将与

人工智能、区块链、5G、物联网、工业互联网等新兴技术融

合发展，从底层技术架构到上层服务模式两方面赋能传统行

业智能升级转型。

二、云原生技术体系日臻成熟，构建数字中台底座

9

云计算发展白皮书（2020 年）

（一）云原生重塑中间件产品

过去几年，业界致力于使用云原生构建底层架构，相关研究主要

围绕容器和微服务展开。利用容器及编排技术解决应用开发环境一致

性问题，构建了容错性好、管理便捷的底层资源系统；践行微服务理

念对单体应用拆分，构建了松耦合的应用开发框架，便捷的实现独立

服务的升级、部署、扩展等流程，使用户能够更快地构建和部署云原

生应用程序。容器和微服务的组合为云原生应用开发提供了基本的底

层架构。在此基础上，当前云原生技术关注点逐渐上移，云原生中间

件、服务网格、无服务器等技术使用户更加聚焦业务逻辑，最大化应

用开发的价值。

在容器及编排技术、微服务等云原生技术的带动下，在云端开发

部署应用已经是大势所趋，重塑中间件以实现应用向云上的变迁势在

必行。在保证业务代码不变的情况下，完成企业应用上云，中间件起

到了至关重要的作用。中间件作为一种连接操作系统、数据库等系统

软件和应用软件之间的分布式软件，通过提供标准接口和协议来解决

异构网络环境下分布式应用软件的互连与互操作问题。

云原生中间件是指在公有云、私有云和混合云等新型动态环境中，

用于构建和运行可弹性扩展的应用，持续交付部署业务生产系统的分

布式中间件。云原生时代的中间件延续了传统中间件的功能，不同之

处在于其将功能从应用中剥离，在运行时为应用动态赋能。

云原生中间件能提供应用管理、发布部署、运维编排、监控分析、

故障恢复等全生命周期管理能力，支撑云原生应用的开发与管理，满

10

云计算发展白皮书（2020 年）

足经典和云原生架构的运维保障需求。在应用开发方面，能提供开发

者体验工具支撑、API 开放能力、产品定制能力、中间件平台、服务

市场应用商店等，来支持云原生应用的开发与管理。云原生中间件正

促进云原生构建完整的体系化解决方案。

（二）云原生如何更好的服务上层应用成焦点

随着云原生实践的深入，上层应用成为焦点。如何提高分布式应

用的性能、如何提高应用开发效率，成为这个阶段的云原生领域思考

的问题。为了解决众多微服务间通信的性能瓶颈问题，服务网格

（Service Mesh）应运而生。作为一种用于管理、观测、支持工作负

载实例之间安全通信的基础设施层，Service Mesh 重新构建了服务间

通信模式，给云原生应用开发者带来更好的开发和维护体验，加速业

务的创新效率。首先，服务网格将服务治理与业务逻辑解耦。服务网

格把 SDK 中的大部分能力从应用中剥离出来，拆解为独立进程，以

Sidecar 模式部署，将服务通信及相关管控功能从业务程序中分离并

下沉到基础设施层，使其和业务系统完全解耦，使开发人员更加专注

于业务本身。其次，服务网格将异构系统的统一治理。随着新技术的

发展和人员更替，在同一家公司中往往会出现不同语言、不同框架的

应用和服务，为了能够统一管控这些服务，以往的做法是为每种语言、

每种框架都开发一套完整的 SDK，维护成本非常之高，而且给公司

的中间件团队带来了很大的挑战。有了服务网格之后，通过将主体的

服务治理能力下沉到基础设施，多语言的支持就轻松很多了。只需要

提供一个非常轻量级的 SDK，甚至很多情况下都不需要一个单独的

11

云计算发展白皮书（2020 年）

SDK，就可以方便地实现多语言、多协议的统一流量管控、监控等需

求。

为进一步聚焦应用开发，无服务器（Serverless）的理念近两年逐

渐火热起来。Serverless 是一种软件系统架构思想和方法，它的核心

思想是开发者使用 Serverless 无须关注底层资源，而只需关注业务应

用的开发，即用户只需要关注函数这个实现业务逻辑的最小单元，无

需关注和业务不相关的资源申请和资源运维等工作，从而缩短流程，

节省的运维人力可以投入到研发，提高研发效率，缩短业务上线时间。

Serverless 按需申请资源，用户只需要为处理请求的计算资源付费，

而无须为应用空闲时段的资源占用付费。此外，Serverless 对实例的

弹性伸缩更加敏感，可短时间内根据业务实际需求扩容足够多的实例

以承载业务高峰期的流量，提供极致的弹性伸缩能力。

（三）云原生助力数字中台建设

数字中台是将企业的共性需求进行抽象，并打造成平台化、组件

化的系统能力，以接口、组件等形式共享给各业务单元使用。使企业

可以针对特定问题，快速灵活地调用资源构建解决方案，为业务的创

新和迭代赋能。数字中台涵盖广泛，其中业务中台将企业经营管理涉

及到的业务场景流程标准化、数据化，为数据中台提供完整的数据源，

保证数据的可复用性，完成业务数据化，通过成熟业务来沉淀企业的

数字化能力；数据中台将业务数据化沉淀的数据，通过大数据、机器

学习等技术处理进行价值提炼，形成企业数据资产，提供决策支持，

赋能前端业务；技术中台利用云原生技术将容器及编排、微服务平台、

12

云计算发展白皮书（2020 年）

中间件产品等组件进行整合并封装，提供规范统一的接口，完成资源

调度、服务治理、消息传递、服务编排、数据分析、数据服务等方面

的工作，降低了应用开发、应用管理和系统运维的复杂度，为前台、

业务中台、数据中台的建设提供技术能力支撑。基于云原生技术实现

中台弹性扩容，依靠平台能力为各个系统产品输出统一管理能力，帮

助企业实现业务数据化、数据业务化，赋能企业智能化营销。

数字中台为业务而生，快速敏捷地响应业务变化，以服务的形式

为业务提供支撑，服务接入层以统一的路由适配转发。在整个技术构

架上就需要考虑可拓展性、敏捷性、轻量化，并注重与前台的交互，

灵活地通过服务编排实现应用功能，满足前台需求。因此数字中台融

合分布式、微服务、容器云、DevOps、大数据处理及高可用高性能

高并发架构，遵循“高内聚、松耦合”设计原则。业务中台需要微服务、

云原生、分布式事务体系支撑，并设计业务模型和微服务边界，最终

形成业务单元；数据中台引入多终端、多形态数据，采用数据分层架

构模式，同时需要指标管理、数据服务、元数据管理等一系列的数据

管理技术做支撑。云原生技术为数字中台建设提供了强有力的技术支

撑，形成了数字中台建设的技术底座，为企业数字化转型和业务能力

沉淀赋能。

13

云计算发展白皮书（2020 年）

来源：中国信息通信研究院

图 12 数字中台架构图

三、SaaS 市场开始加速，将成企业上云重要抓手

（一）国外 SaaS 市场模式成熟，国内 SaaS 蓄势待发

国外 SaaS 服务市场发展成熟，细分领域独角兽企业显现。据

Gartner 调查显示，SaaS 是云计算中最大的细分市场，预计 2020 年全

球市场将增长到 1277 亿美元。以微软、甲骨文、SAP 等为代表的大

型独立软件开发商（ISV）选择采用收购 SaaS 服务的方式来弥补其在

SaaS 服务研发创新上的不足，并取得了云上业务的快速增长。以 AWS

为代表的云服务商则通过在自身云服务的基础上构建生态，建立涵盖

自身 SaaS 服务以及第三方 SaaS 服务的云服务市场。以 Salesforce、

ServiceNow、Workday 为代表的创新型 SaaS 服务商则更加专注于解

决企业管理或者运营服务中某一环节的难题，帮助企业更好发展。这

些 SaaS 服务商凭借其对于服务领域的深入研究以及产品服务专业性，

14

云计算发展白皮书（2020 年）

15

在过去的十年左右时间里快速成长，获得了企业用户的认可，成为了

相关细分垂直领域的独角兽。

数据来源：可信云评估，2020 年 5 月

图 13 可信云企业级 SaaS 评估领域分布情况

国内 SaaS 服务数量显著增长，服务专业性同步提升。据可信云4

企业级 SaaS 评估统计，国内 SaaS 服务主要关注于企业管理和运营的

各个环节服务，涉及企业资源管理、财务管理、协同办公、客服管理

以及客户管理和营销等诸多领域。同时，在企业上云的政策影响下，

关注政务、金融、教育、工业等特定行业的 SaaS 服务显著增加。根

据评估观察，国内 SaaS 服务商多深入企业管理与运营的某个环节研

发产品，如人力、财务、营销等，将产品服务与企业切实业务需求结

合，以提升产品服务的专业性。行业 SaaS 服务商则凭借其对行业业

务需求的理解和行业专业知识的掌握，着力于解决行业内关键生产领

域的核心问题，例如工业领域的质量管理、供应链管理环节等。国内

4 可信云是中国信息通信研究院下属的云计算服务评估品牌，也是我国最早针对云计算服务开展的评估体

系。

云计算发展白皮书（2020 年）

SaaS 服务专业性有所提升，以用友、金蝶、畅捷通、Udesk、北森、

销售易等为代表的 SaaS 服务商，在各细分领域已展露头角。但总体

上看，国内 SaaS 服务商在市场份额、技术成熟度等方面，仍与国外

SaaS 服务商存在较大差距。

国内云服务商开始重视 SaaS 生态深度建设。2019 年，以阿里云、

腾讯云为代表的云服务商依托自身市场优势，先后推出了“SaaS 服务

加速器”、“千帆计划”等，助力 SaaS 服务商成长。云计算市场发展至

今，IaaS 和 PaaS 服务提供商已初具规模效应，市场认可度高，且能

够提供较为成熟的技术为 SaaS 应用快速开发部署提供基础。在此之

前，虽然 IaaS 服务商也有云市场等模式为 SaaS 服务商发展提供平台，

但缺少深度合作。目前，IaaS 服务商和 SaaS 服务商在布局传统企业

上云这一市场过程中，均存在一定短板，具体表现在以下三个方面：

一是 IaaS 服务商多发源于互联网行业，对传统行业企业需求认知仍

不够深入，无法真正理解行业痛点需求；二是创业 SaaS 服务提供商

虽业务创新能力强，能够填补市场需求空白，但缺乏品牌效应，仅凭

一己之力获客成本较高；三是由传统软件服务转型而来的 SaaS 服务，

虽具备良好的业务基础，但缺乏底层云服务支撑。因此，双方通过深

度合作，形成优势力量互补，打通产业链上下游，必将建立一个繁荣、

和谐的 SaaS 服务生态。

（二）疫情推动 SaaS 服务迎来发展新机遇

2020 年 2 月，工信部办公厅发布了《关于运用新一代信息技术

支撑服务疫情防控和复工复产工作的通知》，并指出要充分运用新一

16

云计算发展白皮书（2020 年）

代信息技术支撑服务疫情防控和企业复工复产工作。疫情防控方面。

以智能机器人外呼方式进行居民健康信息摸排，能够帮助社区工作人

员将原本 1 天才能完成的收集居民健康信息工作，压缩至 2-3 小时完

成并实现结果的统计分析。企业复工复产方面。以自动化办公协同、

企业网盘和文档协作为例，能够实现远程办公场景下企业内部员工的

流程审批、文件共享与协同编辑，极大地提高了员工远程办公工作效

率。视频会议、电子合同等软件服务则解决了企业内部员工以及与企

业客户之间远程沟通、合同签署等难题。在线教育方面。视频会议、

视频直播、空中课堂等，为教师和学生提供授课、培训等在线教育服

务，满足学校及各类教育机构教学需求。

疫情之下，SaaS 服务企业用户认可度显著提升，国内 SaaS 服务

迎来发展新机遇。传统软件部署需要现场实施，周期长且需要接触，

无法满足疫情期间的实际需求。SaaS 服务在这时展现出了极大的优

势，减少了本地部署所需的大量前期投入和面对面交付的成本，适合

疫情期间的远程管控需求，避免人员的交叉接触。用户仅需接入互联

网，即可实现软件服务的接入，即时满足疫情防控、企业复工复产、

在线教育的各种需求。疫情期间，企业用户对 SaaS 服务的应用比例

提高，对 SaaS 服务的认可度显著提升。

后疫情时代，SaaS 服务商应当持续提高服务的专业性和主动客

户服务能力，更好的服务于企业客户，推动 SaaS 服务市场快速发展。

虽然疫情期间，企业用户对 SaaS 服务的认可度大幅度提升，但随着

疫情防控进入常态化，SaaS 服务商需要思考如何进一步提升企业用

17

云计算发展白皮书（2020 年）

户对其 SaaS 服务的用户粘性，增强企业用户的付费意愿。一方面，

SaaS 服务商应当将 SaaS 服务聚焦于解决企业业务需求的痛点，以助

力企业创新发展为目标，持续挖掘自身在垂直领域的产品服务深度，

体现服务专业性。另一方面，为提升 SaaS 服务留存率，降低 SaaS 服

务获客成本，SaaS 服务提供商应当提升其客户成功服务能力，重视

用户体验。

（三）SaaS 直击企业痛点，加速中小企业应用上云

中小企业从基础设施上云到应用全面上云仍道阻且长，面临人才

储备不足、技术能力薄弱等挑战。当前，从工信部到各地方政府虽然

发布了助力企业上云的政策和扶持措施，但仍以基础设施上云为主。

然而，行业企业，特别是中小企业，在落地实施过程中发现，仅仅基

础设施上云并无法完全实现企业的降本增效。一方面，传统软件系统

开发部署周期长，早已无法及时响应企业用户对业务系统快速变化的

需求。由于缺乏人才和技术积累，当前阶段多数中小企业仅仅完成了

基础设施的云化，却尚未进行业务系统架构的云化改造，导致应用系

统无法实现快速迭代上线，严重影响企业业务发展。另一方面，中小

企业传统 IT 部门对于上云后如何合理利用基础设施服务资源以及实

现应用的云化部署和运维仍缺乏经验，导致企业上云而无法合理用云，

最大化发挥云计算带来的红利。

SaaS 服务输出多元化应用，具备灵活、稳定、安全等优势，大

大缩短中小企业应用上云路径。伴随着企业上云进程的不断深入，企

业对云计算服务需求呈逐步向应用层上移趋势，不仅注重云资源的使

18

云计算发展白皮书（2020 年）

用，更加重视上云的效率。一方面，SaaS 服务往往由云服务商结合

行业需求研发运营，在满足企业业务基础需求的同时，SaaS 服务商

可以不断升级以满足企业的需求，企业用户免去开发成本的同时，也

可以获得多元化、灵活的应用服务。另一方面，SaaS 服务实施上线

的周期较短，服务的稳定性和安全性由 SaaS 服务商承担，将大大降

低企业后期的运维成本，提高系统安全性。SaaS 服务逐渐成熟并得

以落地应用，将有效解决企业上云面临的人才和技术痛点问题，加速

企业应用上云进程。

SaaS 服务聚焦解决企业业务发展痛点环节，有针对性输出 SaaS

服务能力，助力企业业务创新。一方面，企业用户需要企业内部管理

软件实现办公自动化和协同化，提升企业管理效率，例如人力管理、

财务管理、营销与客服管理、文档协同、视频会议以及电子合同等。

该类软件服务具备一定的行业通用性，但企业实际管理制度和流程存

在差异化。因此 SaaS 服务商普遍关注点在于通过平台化的服务能力

解决 SaaS 服务根据企业实际管理制度和流程进行灵活调整的核心问

题，降低 SaaS 服务部署实施的时间和人力成本。另一方面，政务、

金融、工业、教育等行业需要行业业务属性强相关的 SaaS 服务，以

助力企业实现业务的创新发展。因此，SaaS 服务多聚焦于解决各行

业生产关键环节的痛点问题，将服务做深做精，更好地服务行业客户。

（四）深耕行业，SaaS 服务向平台化、智能化发展

SaaS 服务将深耕行业，为企业上云赋能，助力传统行业数字化

转型。伴随着企业上云进程的推进，目前企业上云用云逐步向 SaaS

19

云计算发展白皮书（2020 年）

层上移，以最大化实现云服务的价值，服务于各行业核心业务创新发

展。SaaS 服务将从协同办公、CRM 等企业管理领域，逐渐向金融、

医疗、教育、政务、工业等行业垂直领域核心应用深入。未来，SaaS

服务将与大数据、人工智能、5G 等新信息基础设施融合，支撑传统

行业业务应用系统升级，全面实现平台化、智能化，服务于企业创新

发展。

企业级 SaaS 服务将向平台化发展，满足上云后企业业务系统定

制化和集成能力需求，实现业务数据流动。大型 SaaS 服务提供商纷

纷构建 PaaS 平台，通过服务模块化，以低代码成本满足企业客户需

求，促进其数字化转型。一是满足企业客户定制化需求，定制化开发

往往意味着成本的增加以及交付周期的延迟，基于 PaaS 平台的开发

能力，SaaS 服务提供商则能够以低代码成本的方式，快速交付定制

化需求。二是满足企业客户系统集成需求，基于 PaaS 平台的开放能

力，SaaS 服务提供商可以通过开放微服务或者 API 接口，实现 SaaS

服务以及系统间的相互集成。三是实现系统服务间数据打通，打破信

息孤岛，系统服务间数据将真正流通起来，服务于企业业务发展。

企业级 SaaS 服务将向智能化发展，实现上云后企业智能化管理

和运营。SaaS 服务将进一步与大数据、人工智能等新一代信息技术

结合，助力企业服务智能化转型。一方面，目前企业办公、营销、客

服管理、呼叫中心、视频监控等 SaaS 服务等均呈现出了智能化的趋

势。以智能客服为例，智能机器人客服不仅能够实现 7*24 小时客服，

同时可以为企业节省人力成本，全面提升服务效率和满意度。另一方

20

云计算发展白皮书（2020 年）

面，语音识别、人脸识别等技术也逐步以智能 SaaS 服务形式对外提

供服务，大幅度降低企业智能化门槛。

四、分布式云成云计算新形态，助力行业转型升级

（一）云计算从中心向边缘延伸

1.边缘产业逐步兴起

边缘计算的兴起，使得如何为边缘侧赋能成为业界关注的热点。

边缘的具体形态分为边缘云和边缘终端。边缘云是云计算向网络边缘

侧进行拓展而产生的新形态，是未来产业关注重点，是连接云和边缘

终端的重要桥梁。边缘终端位于边缘云与数据源头路径之间，靠近用

户或数据源头的任意具备一定硬件配置的设备，包括边缘网关、边缘

服务器、智能盒子等终端设备。围绕边缘云与边缘终端，在 CDN、

视频渲染、游戏、工业制造、自动驾驶、农业、智慧园区、交通管理、

安防监控等应用场景下，相关产业已初现端倪，蓄势待发。

2.边缘侧需求催生分布式云新形态

为了满足视频直播、AR/VR、工业互联网等场景下，更广连接、

更低时延、更好控制等需求，云计算在向一种更加全局化的分布式组

合模式进阶。

分布式云或分布式云计算，是云计算从单一数据中心部署向不同

物理位置多数据中心部署、从中心化架构向分布式架构扩展的新模式。

分布式云是未来计算形态的发展趋势，是整个计算产业未来决胜的关

21

云计算发展白皮书（2020 年）

键方向之一，对于物联网、5G 等技术的广泛应用起到重要支撑作用。

包括电信运营商、互联网云服务商等在内的各类型厂家纷纷进行相关

尝试，利用自身优势资源，将云计算服务逐步向网络边缘侧进行分布

式部署。

来源：中国信息通信研究院

图 14 分布式云架构图

分布式云一般根据部署位置的不同、基础设施规模的大小、服务

能力的强弱等要素，分为三个业务形态：中心云、区域云和边缘云。

中心云构建在传统的中心化云计算架构之上，部署在传统数据中

心之中，提供全方面的云计算服务；区域云位于中心云和边缘云之间，

一般按照需求部署在省会级数据之中，主要作用是为中心云和边缘云

之间进行有效配置；边缘云与中心云相对应，是构筑在靠近事物和数

据源头的网络边缘处，提供可弹性扩展的云服务能力的云计算模式，

并能够支持与中心云协同。

（二）云边协同成为分布式云的核心

为了让“万物互联”的世界更加智能，通过将云计算的能力进行拓

22

云计算发展白皮书（2020 年）

展，边缘云能够深入到传统中心云无法覆盖到的边缘应用场景。同时，

分布式云通过云边协同，提供了一种更加全局化的弹性算力资源，为

边缘侧提供有针对性的算力。

1.边缘云应用服务产品不断丰富

电信运营商整合网络和边缘基础设施优势，采用“网络+平台”的

融合部署模式，推出 MEC 边缘解决方案，通过用户面功能网元（UPF）

的不同部署位置，提供专享型和共享型边缘云服务。互联网云服务商

通过边缘云服务、边缘节点管理平台等云边协同产品将中心云功能下

沉到网络边缘，同时打通中心云与边缘云通道，加强中心云对边缘云

在服务和应用上的管理能力。同时，电信运营商和互联网云服务商均

着眼于边缘侧 PaaS 平台建设，通过统一 API 接口将服务能力开放到

应用侧，提升边缘侧典型场景的应用落地能力。

中国信息通信研究院为保障电信运营商和云服务商在云边协同

应用服务方面的能力，依托 CCSA、ITU 等标准化组织，积极研究云

边协同相关标准，制定了《分布式云全局管理框架》、《边缘云服务信

任能力要求》、《基于云边协同的边缘节点管理解决方案能力要求》、

《MEC 边缘云服务信任能力要求》等标准，对边缘云及云边协同能

力提出了具体的技术要求，为应用企业选择可信、安全的云边协同服

务或解决方案提供相关参考。

2.云边协同技术体系架构趋于成熟

随着云边协同相关技术的不断发展，以资源协同、数据协同、服

23

云计算发展白皮书（2020 年）

务协同、应用协同、运维协同、安全协同等为基础的分布式云体系架

构已经逐渐成熟，而电信运营商和互联网云服务商则发挥各自优势，

继续完善云边协同技术体系架构。电信运营商利用 5G 网络的特性，

以 CT 联接能力和 IT 计算能力融合作为切入点，通过部署用户面功

能网元（UPF），对软硬件接口进行解耦，构建涵盖硬件资源层、虚

拟化层、平台能力层、应用层、应用安全和运维的一体化的服务能力，

搭建 5G+MEC 边缘云的云边协同基础设施和 MEC 边缘云平台，构建

一站式边缘侧解决方案。互联网云服务商依托中心云服务基础，将容

器、微服务等能力扩展至边缘侧，在边缘侧提供弹性分布式算力资源，

同时在云端向边缘侧进行远程资源业务调度、数据处理分析、服务编

排、运维指令下达等操作，构建“中心云-边缘云”的分布式云架构；

或者通过分布式云管理平台和边缘节点软件相互配合，对边缘侧计算

资源进行远程管控、数据处理、分析决策等操作，提供完整的云边协

同的一体化服务。

3.云边协同布局促使计算资源分布式发展

现阶段，云计算基础设施布局已经相对清晰，各大云计算服务商

已经在全国构建了大型数据中心，满足中心云相关服务需求，而在区

域云和边缘云等边缘侧基础设施建设中还存在数量少、覆盖小、网络

差等问题。未来需重点推进边缘侧基础设施建设部署，同时增强云边

协同能力建设，促进分布式计算资源发展。

增加边缘基础设施数量，是计算资源分布式发展的基础。在更靠

近用户的网络边缘侧建设边缘机房，或利用现有边缘机房进行改造，

24

云计算发展白皮书（2020 年）

增加边缘云节点数量。现阶段，重点在工厂、园区、港口、医院等对

计算资源需求较为旺盛的场所进行部署，保证计算资源能够全面覆盖

典型应用场景。

增强云边协同能力，是计算资源分布式发展的核心。边缘侧更多

提供轻量化，实时性的计算能力，对于应用场景中复杂的环境，需要

中心云对数据进行分析挖掘、数据共享、模型训练和升级等工作，并

对边缘侧进行管理。只有增强云边协同能力，才能保证中心与边缘进

行更好的协同工作，使分布式计算资源能够得到更好的利用。

（三）云边协同助力行业应用转型升级

1.数媒行业依托云边协同增强服务能力，提升用户体验

伴随着移动互联网的发展和智能手机、平板电脑等智能终端的

普及，广大人民通过碎片化的时间获取的信息愈发多元化。相关调查

显示，目前我国智能终端用户超过七成的碎片化时间“消耗”在视频直

播、短视频、游戏为代表的文娱应用中，数媒文娱产业进入爆发增长

阶段。面对当前每日激增的用户流量和越发激烈的市场竞争，数媒行

业探索依托云边协同实现业务模式的创新，增强服务能力，提升用户

体验。

传统 CDN 结合云边协同降本增效，实现业务智能升级。随着视

频、游戏等行业快速发展，爆发式增长的数据量对传统 CDN 行业带

来了较大挑战，硬件设备及网络带宽高昂的成本以及海量数据情况下

的网络传输压力成为了传统 CDN 行业发展的痛点。云边协同模式的

25

云计算发展白皮书（2020 年）

结合应用是各大 CDN 厂商提升自身竞争力的第一选择。首先，基于

云边协同模式构建 CDN 服务可以将云计算弹性伸缩按需扩展的计算

能力下沉至 CDN 节点边缘侧，大幅降低边缘侧设备资源的一次性购

置成本；其次，CDN 边缘节点可以在靠近终端用户的边缘侧对数据

进行预计算处理，进而有效降低与中心云之间的网络传输流量，缓解

带宽压力；最后，通过云边协同赋予的资源智能调度能力，可以对边

缘侧 CDN 节点进行最优化选择，为边缘侧用户提供高效稳定的服务。

来源：公开资料整理

图 15 视频云行业图谱

视频领域面对海量用户和激增流量，借助云边协同大幅提升用

户体验。相关数据显示，目前我国视频用户已经超过社交用户，成为

全网第一用户群。随着用户基数的激增和热点事件的频现，如何在高

流量下保证低时延、少卡顿，满足用户对视频直播、点播、短视频、

在线会议等细分场景下对于高清、流畅、实时互动的用户体验需求成

为视频行业必须解决的头等问题。针对视频行业的火爆场景，我国各

大云服务商将云边协同模式在视频云产品中落地实践，通过产品化的

SDK、遍布全国的边缘计算节点以及强大的云端处理能力，为视频行

业客户提供包含“采集-预处理-编码封装-边缘推流-云端处理-边缘分

26

云计算发展白皮书（2020 年）

发-播放”的端到端一站式解决方案，垂直行业覆盖游戏文娱、在线教

育、新闻媒体、零售电商等各领域。

游戏行业依托云边协同新模式，打造全新升级体验。近年来，游

戏市场的火爆对各大游戏厂商提出了更高的要求，如何在激烈的市场

竞争环境下通过更好的用户体验获取用户青睐是各大游戏厂商的第

一要务。“云边协同+游戏”使得近年大热的 AR、VR 以及云游戏变得

更加容易落地实践。一方面，通过云边协同可以根据不同场景需求分

别在近用户的边缘侧和云端进行游戏画面渲染计算，减少由于高延迟

和低刷新率造成的头晕等不适应感，实现完整的 AR、VR 体验；另

一方面，对于大规模多人在线游戏而言，通过云边协同可以基于位置

匹配玩家，实现同一地域玩家就近通过同一个边缘节点进行连接，降

低游戏互动时延。随着 5G 时代的到来，云边协同+5G 将会促进云游

戏模式发展落地，将为游戏行业带来全新的体验升级。

2.云边协同加速传统行业数字化进程

现阶段，边缘计算市场规模增长迅速，尤其在工业制造、自动驾

驶等为代表的生产控制类应用场景中，大量数据需要在用户侧进行处

理，来保证低时延、高并发、大流量等需求。同时，数据存储、模型

训练、大数据挖掘等操作需要云端的强大计算能力支持。在实际应用

中，云边协同已成为加速工业、农业、交通等行业数字化进程的主流

模式。

在工业领域，云边协同实现传统工业与信息化的融合。以工业制

造为例，工业现场的边缘计算节点具备一定的计算能力，能够自主判

27

云计算发展白皮书（2020 年）

断并解决问题，及时检测异常情况，更好的实现预测性监控，提升工

厂运行效率的同时也能预防设备故障问题，将处理后的数据上传到云

端进行存储、管理、态势感知。同时，云端也负责对数据传输监控和

边缘设备使用进行管理。中心云与边缘云在资源管理、远程控制、安

全管理、运维监控等方面协同运作，保证现场接入设备能够快速、准

确、方便的进行相关生产操作，同时预防设备及产品故障，同时加强

数字化建模与实体映射，真正实现数字化生产。

在农业领域，云边协同帮助传统农业向数字化、智能化、网络化

转型。以畜牧养殖为例，边缘网关、边缘控制器等边缘计算设备接收

来自猪、牛、羊、兔等牲畜佩戴设备上传的数据，并在边缘侧对养殖

数据进行处理，实时掌握牲畜分布、饲料使用量、疫情数据等信息。

云计算平台与边缘网关、边缘控制器等边缘计算设备协同工作，解决

带宽和网络覆盖不全面、生产风险不可控等问题，保证牲畜采集信息

和数据的准确性，对检疫和疫情进行有效管控，全程监管畜牧生产过

程，最终达到数字化、智能化、网络化养殖。

在交通领域，云边协同助力智能驾驶升级。以自动驾驶为例，汽

车将集成激光雷达、摄像头等感应装置，并将采集到的数据与道路边

缘节点和周边车辆进行交互，从而扩展感知能力，实现车与车、车与

路的协同。云计算中心则负责收集来自分布广泛的边缘节点的数据，

感知交通系统的运行状况，并通过大数据和人工智能算法，为边缘节

点、交通信号系统和车辆下发合理的调度指令，从而提高交通系统的

运行效率。

28

云计算发展白皮书（2020 年）

3.智慧城市利用云边协同实现感知、互联和智慧

智慧城市是城市化发展的高级阶段，主要指利用新一代信息技术，

推动城市各类信息系统之间的资源共享和业务协同，实现城市“感知、

互联和智慧”。智慧城市的运行中，具有广泛终端接入、海量数据处

理和实时性要求高等特点，利用云边协同可以大幅降低网络负载，提

高响应速度，降低能源消耗。目前，云边协同已经在交通、园区、安

防等领域发挥作用，成为智慧城市建设的新生力量。

智慧交通领域应用云边协同助解城市拥堵病。随着我国机动车保

有量迅速增加，城市交通系统结构复杂，形成包含人、车、路，视频、

图片、交通流等各类的海量交通数据，交通领域如何获取实时路况信

息、快速解决突发事故、缓解交通拥堵是社会关注的焦点。云边协同

模式下，云计算相当于智慧交通的“大脑”，边缘计算相当于智慧交通

的“神经末梢”。在边缘服务器上通过运行智能交通控制系统来实时获

取和分析数据，根据实时路况来控制交通信号灯，以减轻路面车辆拥

堵等问题。同时，可借助多方数据资源，通过自动比对和分析，对道

路拥堵情况提前发出预警，辅助人工决策。

智慧园区领域应用云边协同加速园区创新转型。传统园区的信息

化往往存在数据不互通、业务难融合、运营效率低、业务创新难等痛

点。智慧园区的构建聚焦于园区在能源、环境、安防等方面的智慧化

服务，为园区的管理者和使用者提供深度数据共享和服务体验。基于

云边协同可实现园区视频监控和态势感知能力的提升，通过将园区内

的所有摄像头接入到边缘服务器，视频分析算法下沉实现对视频流直

29

云计算发展白皮书（2020 年）

30

接进行处理，从而无需把视频流数据上传到云端，既可以减少带宽成

本，又可以提高处理的实时性。构建开放的“云、管、边、端”协同的

园区信息基础设施架构，有助于加快园区智慧化转型。

智慧安防领域应用云边协同筑牢城市安全防线。随着“平安城市”、

“雪亮工程”的深入开展，安防行业对视频图像的清晰度、网络带宽要

求越来越高，更多的数据需要借助在前端处理以提升决策准确率。云

边协同在安防领域的实践从根本上打破了“智能”应用落地的壁垒，让

原本受限于计算力、传输环境、存储环境等诸多问题的应用设想得以

实现。在边缘计算节点上搭载 AI 人工智能视频分析赋能边缘智慧化，

完善重大刑事案件和恐怖袭击活动预警系统和处置机制，提高视频监

控系统的防范刑事犯罪和恐怖袭击能力。

五、原生云安全理念兴起，推动安全与云深度融合

（一）云原生重塑 IT 架构，端到端安全风险引关注

云原生技术正在全球范围内被快速采纳，根据 IDC 预测，到 2022

年全球 60%的组织机构将增加对云原生应用及平台的资金投入5。以

容器、微服务、DevOps 为代表的云原生技术的引入，促使 IT 架构从

稳态转向敏捷。IT 架构的变换也使得基于边界的传统安全模型不在

适用，新架构下的安全风险备受关注。

表 1 传统安全与云原生体系安全的主要区别

5数据来源：IDC 《IDC FutureScape: Worldwide Cloud 2020 Predictions》

云计算发展白皮书（2020 年）

31

传统安全 云原生安全

安全模型 重视边界安全 注重端到端的全链路安全性。

身份管理 基于 IP 地址的身份管理 基于服务的身份管理

隔离粒度 虚拟机或物理机级别，隔离依靠物理

设备或管理程序实现隔离。

容器级别，使用 namespace 和 cgroup

等技术实现进程级隔离。

威胁应对

被动。快速检测威胁是首要任务。确

定漏洞后才执行用于缓解威胁的步

骤。

主动。恶意软件活跃于易受攻击的软

件上以及静态不变的系统上。首先要

做的是强势改变系统状态，从而消除

恶意软件生存所需的条件。

漏洞修补

方式

增量修补。修补程序以增量方式应用

到系统，因为每个修补程序都要由内

部团队审批。

通过彻底重新部署进行修补。带有最

新组件的全新镜像利用自动化和不

可变基础架构的理念应用到数据中

心。

来源：公开资料整理

1.基础设施轻量化在隔离和组件交互方面带来新的挑战

以容器和编排为核心基础的云原生技术架构，将容器取代虚拟机

成为资源承载调度的最小单元。部署模式的改变带来全新安全问题：

在隔离性方面，不同于虚拟机的独立操作系统，容器技术共享宿主机

操作系统，这种进程级别的“软”隔离，增加了逃逸风险；在数据共享

方面，紧密联系的多个容器通常共享某些数据，这些容器中的某一个

被攻破都会导致数据泄露，使得攻击面大大增加；在组件交互方面，

容器及其编排系统的组件高度解耦、分散部署，协同交互的组件链条

增长，中间态的攻击风险增加。同时新技术的不熟悉也导致因部署不

合规而被攻击的现象频发。

2.容器镜像全链路追踪管控成为难题

容器镜像是云原生技术架构中软件交付流转的主要形态，贯穿应

用研发、测试、预生产和生产运营的全生命周期，流转链路长、涉及

人员机构复杂，安全管控难度较高。基础镜像来源复杂，源头管控难，

云计算发展白皮书（2020 年）

除官方镜像仓库外，还存在大量第三方镜像仓库，镜像来源难以统一

把控，同时包括官方镜像在内的大量基础镜像均存在一定程度的安全

漏洞，也为镜像安全的源头控制增加了难度。传输过程中间人攻击篡

改难校验，现阶段容器签名技术尚未被企业、用户大范围采用，若用

户采用非加密方式在镜像仓库下载传输容器镜像，传输过程中被中间

人篡改而难以发现。全链路交互人员复杂，链路监测追踪难实现。容

器镜像的完整生命周期涉及跨部门的多团队协作，开发、测试和运维

人员会根据不同需求操作容器镜像，任何一个环节的改动都会增加镜

像的安全风险。实现容器镜像全生命周期的流转监测、更改、部署和

反向追踪难度较大。

3.应用微服务化增加攻击风险，存在连锁攻破可能

容器技术保证了运行环境的强一致性，为应用服务的拆分解耦提

供了前提，应用微服务化进程加速，同时也带来新的安全隐患：单体

应用拆分导致端口数量暴增，攻击面大幅增加。微服务将单体架构的

传统应用拆分成众多个服务，应用间交互的端口成指数级增长，相较

于单体应用架构集中在一道口防护的简单易行，微服务化应用在端口

防护、访问权限、授权机制等方面的难度陡增。强关联微服务间连锁

攻破风险高。尽管微服务提倡隔离、轻量、独立开发部署、业务松耦

合，但现实情况中很多场景下的多个服务间是紧密联系的，这些强关

联的微服务间是点对点的形式进行沟通连接，随着连接点的增多，整

个连接体系中的单一服务因漏洞被攻破会增加整个系统的破解风险。

32

云计算发展白皮书（2020 年）

（二）原生云安全推动安全与云深度融合

随着云计算应用的普及与成熟，安全问题日益受到关注，原生云

安全理念应运而生，原生云安全理念并不是只解决云原生技术带来的

安全问题，而是一个全新的安全理念，旨在将安全与云计算深度融合，

推动云服务商提供更安全的云服务，帮助云计算客户更安全的上云。

来源：中国信息通信研究院

图 16 原生云安全架构

原生云安全指云平台安全的原生化和云安全产品的原生化。安全

原生化的云平台能够将安全融入从设计到运营的整个过程中，向用户

交付更安全的云服务；原生化的云安全产品能够内嵌融合于云平台，

解决用户云计算环境和安全架构割裂的痛点。

1.云平台安全原生化，交付用户更安全的云服务

原生云安全强调云平台安全原生化，云服务商从云平台的设计阶

段起考虑安全因素、纳入解决方案，将安全前置，让云计算成为更安

全可信的新型基础设施。与大部分云计算客户相比，云服务商具备更

强、更专业的安全技术和管理能力。当云计算平台深度融合了云服务

33

云计算发展白皮书（2020 年）

商的安全能力，云计算客户在此基础上使用云服务，一些安全责任由

云服务商承担，部分安全问题已被云服务商解决，云计算客户无需再

关注，与云计算客户以往自建 IT 架构相比，将更加安全便捷。

云服务商可以从三大方面充分发挥自身安全能力优势，不断促进

云平台安全原生化：

一是从研发阶段关注云计算安全问题，前置安全管理。针对云服

务的安全，通过在产品研发早期便融入安全措施来提升软件质量，覆

盖软件研发运营整个过程，降低解决安全问题的成本，具体措施如图

所示，包括：1）管理架构，建立合适的人员组织架构与制度流程，

保证研发运营流程安全的具体实施；2）安全培训，针对人员进行安

全培训，增强安全意识，进行相应考核管理；3）明确安全要求，前

期明确安全要求，如设立质量安全门限要求，进行安全审计，对于第

三方组件进行安全管理等；4）安全需求分析与设计，在研发阶段之

前，进行安全方面的需求分析与设计，从合规要求以及安全功能需求

方面考虑，进行威胁建模，确定安全需求与设计；5）安全研发测试，

搭配安全工具确保编码安全，同时对于开源及第三方组件进行风险管

理，在测试过程中，针对安全、隐私问题进行全面、深度的测试；6）

安全发布，服务上线发布前进行完整性审查，制定事先响应计划，确

保发布安全；7）运营安全，上线运营阶段，进行安全监控与安全运

营，通过渗透测试等手段进行风险评估，针对突发事件进行应急响应，

并及时复盘，形成处理知识库，汇总运营阶段的安全问题，形成反馈

机制，优化研发运营全流程；8）服务停用下线，制定服务下线方案

34

云计算发展白皮书（2020 年）

与计划，明确隐私保护合规方案，确保数据留存符合最小化原则。

来源：中国信息通信研究院

图 17 可信研发运营安全体系

关于安全前置的实现，目前业界的共识为通过安全解决方案将安

全融入现有研发流程，不破坏现有的研发环境。Gartner 也提出要使

用安全测试工具和流程去适应研发人员；使用新型工具和方法来最大

程度降低对于研发人员的影响。新型的工具主要包括静态应用程序安

全测试、交互式应用程序安全测试、软件组成分析、运行时应用自保

护。

二是落地应用新兴安全技术，推动云平台整体安全。随着云计算

安全态势的日益严峻与场景复杂化发展，安全技术也在迅速衍生，新

安全技术的落地与应用，能够有效提升云平台安全性。1）零信任革

新无边界网络安全架构。云计算使得网络安全边界概念消失，传统的

基于边界的网络安全架构和防护机制难以有效应用，零信任架构应运

而生。零信任以数字身份为基石，对人、设备、应用等所有访问主体

35

云计算发展白皮书（2020 年）

赋予数字身份，通过信任评估模型和算法，对身份进行持续的信任等

级评估，动态的授予最小权限，彻底推翻边界安全架构下对信任的假

设和滥用，以适应云计算环境下复杂的网络安全环境。2）智能+安全，

提升应用效率与深度。随着 AI 技术的不断发展和成熟，越来越多的

云服务商将其与云安全技术结合，实现云安全智能分析、检测和防御。

在智能分析方面，AI 技术能够对云安全相关的海量数据进行深入挖

掘以提取有效信息，极大程度提升分析效率。在智能检测方面，利用

AI 技术，能够发现人为难以察觉的潜在的安全事件，对可能发生的

风险进行预警。在智能防御方面，结合历史事件与经验，提出精细化

的事件处置建议或自动响应事件，实现安全运营闭环。3）保密计算

保障云上数据使用中安全。目前，在数据全生命周期加密处理中，数

据加密存储和传输已发展较为成熟，而加密数据的计算作为生命周期

中的关键环节，仍存在较大缺口。Gartner 在“Hype Cycle for Cloud

Security 2019”中首次列入保密计算（confidential computing）技术，

国内外头部云服务商也已经开展相关技术研究或应用工作。保密计算

基于可信执行环境（TEE），以可信硬件为载体，提供硬件级强安全

隔离和通用计算环境，加密数据只能在可信执行环境中计算和运行，

所有数据参与方均无法触及和控制计算行为，能够有效保障云上数据

使用中的安全。目前，保密计算在用户数据与隐私保护、多方/跨境

数据合作、密钥管理等场景下已经得到应用并取得显著效果。

三是提高交付云服务的安全性，SaaS 安全迎来新挑战。随着云

服务市场的迅速发展，如何交付更安全的云服务显得尤为重要。根据

36

云计算发展白皮书（2020 年）

中国信通院《中国公有云发展调查报告（2020 年）》统计，有 42.4%

的企业在选择公有云服务商时会考虑服务安全性，是影响企业选择的

重要因素。云服务安全性的重点关注方向包括外部环境、云平台、管

理流程、人员管理、合规管理以及业务连续性管理等方面。

来源：中国信息通信研究院

图 18 云服务安全性重点关注方向

疫情期间，协同办公、视频会议、电子签约、数字营销等 SaaS

产品成为企业刚需，SaaS 服务为各行各业数字化转型、信息化升级

提供了便利，同时，也带来了新的安全挑战。除服务功能、服务可用

性外，SaaS 服务的安全性是企业选择和使用 SaaS 时的核心考量因素，

让 SaaS 更安全是服务提供商的重要发展方向。针对 SaaS 安全，目前

关注较多的问题包括如何保障数据安全、如何实现完善的多租户隔离、

如何保障 SaaS 应用程序部署时的安全性、如何实现有效的网络安全

防护以及 SaaS 服务的安全合规、安全备份与恢复、身份鉴别及权限

管理等问题。

37

云计算发展白皮书（2020 年）

2.云安全产品原生化，为用户提供更有力保障

原生云安全提倡安全产品原生化。云服务商、安全厂商等提供内

嵌于云、能够有效解决云上安全风险的原生安全产品；云计算客户能

够利用原生安全产品，建设与云计算环境融合的安全体系与架构，规

避传统安全架构与云计算环境割裂等问题，更加安全的使用云计算。

内嵌于云的原生安全产品，能够充分了解和利用云平台，最大限度发

挥安全防护能力，极大程度提升云计算客户体验。原生安全产品特性

和优势主要体现在四方面：1）产品采用内嵌的方式而无需外挂部署，

云计算客户可以更加便捷的使用，产品运行更加稳定和安全；2）产

品充分利用云平台原生的资源和数据优势，拥有弹性的计算和网络能

力，整合、关联分析云平台内海量数据，深入挖掘潜在安全风险；3）

产品与客户云资产有效联动，实现从检测、告警到处置的安全运营自

动化闭环；4）解决云计算架构带来的特有安全问题，实现云计算的

使用安全。云服务商自身也是云安全的提供商，安全厂商在安全领域

有其特有的优势。根据中国信息通信研究院《中国私有云发展调查报

告（2020 年）》统计，55.2%的企业表示私有云安全产品和服务由第

三方安全厂商提供，选择私有云服务商提供安全产品和服务的企业占

比为 37.6%。因此，为实现真正意义上的原生云安全，云服务厂商与

安全厂商应加强合作，共建云安全生态。

单类云安全产品趋于整合，形成综合的云安全解决方案。针对综

合的云安全解决方案，原生云化部署充分利用云平台原生资源和数据

优势，与云资产联动，避免安全管理复杂化、碎片化，实现安全运营

38

云计算发展白皮书（2020 年）

自动化闭环，提升云的整体安全性。目前综合的云安全解决方案主要

包括三种类型：1）云工作负载保护平台（CWPP），以工作负载为中

心的安全解决方案，旨在解决混合云、多云数据中心基础架构中服务

器工作负载的独特保护要求，不受地理位置的影响，为物理机、虚拟

机、容器和无服务器工作负载提供统一的可视化和控制力。CWPP 产

品通常结合使用网络分段、系统完整性保护、应用程序控制、行为监

控、基于主机的入侵防御和可选的反恶意软件保护等措施，保护工作

负载免受攻击。CWPP 集成云防火墙、主机安全、恶意软件扫描、应

用程序控制、漏洞管理、日志管理和监控等云安全产品的能力。2）

云访问安全代理（CASB），核心价值是解决深度可视化、数据安全、

威胁防护、合规性这四类问题。a）深度可视化包括影子 IT 发现、组

织机构云服务格局的统一视图以及从任何设备或位置访问云服务中

数据的用户详细信息；b）数据安全指实施以数据为中心的安全策略，

以防止基于数据分类、数据发现以及因监控敏感数据访问或提升权限

等用户活动而进行有害活动；c）威胁防护指防止有害设备、用户和

应用程序访问云服务，包括通过嵌入式 UEBA 识别异常行为、威胁

情报、网络沙箱以及恶意软件识别和缓解；d）合规性指确定云中的

敏感数据，并执行数据泄露防护策略，满足合规需求。CASB 主要集

成访问控制（IAM）、数字证书等身份与访问控制类与数据加密等数

据安全类产品的能力。3）云安全态势管理（CSPM），核心解决的是

云平台在使用过程中的配置安全问题，配置问题包括了几种类型：账

号特权、网络和存储配置、以及安全配置（如加密设置），如果发现

39

云计算发展白皮书（2020 年）

40

配置不合规，CSPM 会采取行动进行修正。CSPM 与访问控制配置、

网络配置、存储配置、数据库配置等与云安全配置管理相关的安全产

品密切关联。

六、数字化转型旨在提高生产力，云化能力是关键

（一）数字化转型核心是提高生产力，传统信息基础设

施亟待升级

在全球经济承受巨大下行压力，增长动能弱化的背景下，数字经

济异军突起成为提振世界经济的强心剂。根据中国信息通信研究院统

计，2018 年我国数字经济规模达到 31.3 万亿元，占 GDP 的比重达到

34.8%6。数字经济时代背景下，企业面对的风险与挑战愈发严峻，企

业间的竞争十分激烈，迫切需要通过新的技术手段提质增效、创新动

力源泉，更好地提升发展质量效益。企业数字化转型的本质是提高生

产力，企业数字化转型是指企业与数字技术全面融合，提升效率的经

济转型过程，即利用数字技术，把企业各要素、各环节全部数字化，

推动技术、业务、人才、资本等资源配置优化，推动业务流程、生产

方式重组变革，从而提高企业经济效率。企业数字基础设施指的是企

业数字化转型过程中，用人工智能、云计算、大数据等新一代数字技

术改造和企业信息技术相关的基础软硬件，包括网络、数据中心、云

计算平台、基础软件、IT 工具软件等方面，从而形成企业各项活动

相关的数字化技术体系和多层次的 IT 能力。利用数字基础设施加速

6数据来源：中国信息通信研究院《中国数字经济发展与就业白皮书》，2019 年

云计算发展白皮书（2020 年）

转型既是国家发展战略的重要导向，也是经济高质量发展和消费升级

的客观要求。

1.宏观环境与内外部需求变化驱动企业数字化转型

数字化转型是经济增长新动能，中国经济进入“新常态”，整体增

速放缓，企业发展需要在生产方式和运营模式上进一步创新。数字化

转型为企业进一步发展注入活力，加速催生新业态、新模式、新服务，

培育经济增长的新动能。数字化转型是企业应对外部需求变化的最有

效手段，进入数字化时代，消费者不再是产品的被动接受者，个性化、

定制化需求使消费者深度参与至产品的生产过程。快速获取消费者的

多元化需求，数据驱动产品优化，加速迭代交付速度以获取差异化的

竞争优势成为关键，数字化转型加速了企业积聚核心数据，缩短了交

付周期。数字化转型符合企业降本增效的内在发展需求，数字化转型

可以支撑企业业务应用的标准化及快速定制化，实现以数字技术为核

心要素、以开放平台为基础支撑、以数据驱动的精细化运营，改善以

人治为核心的低效管控治理方式，降低管理成本，提升企业生产效率。

2.传统信息基础设施陈旧掣肘数字化转型

根据《2019 中国企业数字转型指数研究》显示，中国企业在数

字化转型方面成效显著的比例仅为 9%，平均成绩只有 45 分，数字化

程度整体偏低。信息基础设施作为数据这一生产要素的产生、加工和

价值挖掘的主要承载工具，直接影响数字化转型质量，传统信息基础

设施陈旧无力支撑上层应用的多元需求成为转型的一大瓶颈。

41

云计算发展白皮书（2020 年）

跨平台异构环境的数据难打通。由于历史原因，早期企业部门间

的 IT 建设缺乏统一管理，应用需求差异较大且开发时间不一致，导

多个软硬件平台的信息系统同时运行。这些系统数据相互独立、隔离，

应用间的数据天然割裂。随着数字化进入到全新的发展阶段，构建数

据驱动的精细化运营体系，需要打通组织内部的数据壁垒，实现生产

过程全链条的全量数据汇集，这也成为企业基础设施主要瓶颈。

高并发、不可预测访问需求承载力有限。随着网联化的进程持续

推进，互联网形态的业务日渐丰富。相比较过去传统业务，网联业务

具有更强的互联网业务形态，在诸如抢购、秒杀、网促等场景下，要

求 IT 架构能更好的支撑高并发、高弹性的业务需求。而现有基础设

施架构为应对可能存在的业务峰值，需要储备大量物理资源，造成了

严重的资源闲置，随时存在因不可预测流量冲击导致业务中断的风险。

上层应用敏捷化的交付需求难支撑。快速响应用户需求变化，推

动应用产品迭代更新是数字时代企业最有力的竞争手段，偏稳态的传

统基础设施略显乏力。在数字技术支撑方面，传统基础设施对大数据、

区块链、人工智能等新兴数字技术在算力支撑、统一服务编排调度等

方面的支持能力有限，难以构建基于数据的自驱动交付流程，产品交

付的效率大打折扣；在应用开发方面，基于传统信息系统构建的业务

应用，集成了多个业务逻辑，修改其中部分程序也需要对整个程序进

行重建和部署，阻碍了应用开发效率；在运维管理模式方面，传统基

础设施架构下企业将开发、IT 运营和质量保障分三个各自独立的部

门，软件开发和部署涉及组织多部门间的联动合作，沟通协作成本较

42

云计算发展白皮书（2020 年）

高，影响应用交付效率。

多场景多层次的 IT 服务需求难实现。企业数字化转型的经济效

率更高，业务产品上线周期缩短，业务运营更加快速，传统的 IT 服

务能力捉襟见肘，难以支撑复杂的业务应用场景需求和产品的上线速

度，企业数字基础设施资源和能力的交付亟需从被动支撑向更敏捷、

更主动的交付转型。首先需要结合云计算技术实现对底层基础软硬件

资源的统一管理，其中虚拟化实现资源的快速交付，容器实现应用的

快速部署，微服务架构实现应用开发的拆分；其次需要资源和共性组

件模块化、PaaS 平台化，低成本定制开发适配多种业务场景和职能

需求的 IT 工具产品，从而实现 IT 工具服务的敏捷交付和自服务式使

用。

（二）云计算加速数字化转型，显著提升企业生产力

数字化转型是利用数字技术，把企业各环节要素数字化，推动要

素资源配置优化、业务流程生产方式重组变革，从而提高企业经济效

率的过程，其中数字基础设施是生产工具，数据是生产资料。以云计

算为核心，融合人工智能、大数据等技术实现企业信息技术软硬件的

改造升级，创新应用开发和部署工具，加速数据的流通、汇集、处理

和价值挖掘，有效提升了应用的生产率。云原生技术彻底改变了传统

信息基础设施架构，加速了基础设施的敏捷化，进一步提升了企业生

产效能。

单元轻量化提升资源效能。在资源粒度方面，云原生技术体系以

容器为基本的调度单元，相比虚拟机资源的切分粒度细化至进程级，

43

云计算发展白皮书（2020 年）

进一步提升了资源的利用效率。在资源弹性方面，容器共享内核的技

术特点使载体更加轻量，秒级的资源弹性伸缩能力，能够更加快速灵

活的响应不同场景的需求，大幅提升资源复用率。例如，国内某快递

公司基于云原生容器技术构建转运作业融合系统，来解决转运作业融

合等业务线技术栈割裂、业务响应周期长、资源利用率低、维护困难

等问题。投产后运维环节和开发测试环节操作效率提升 53%，单次部

署平均时间缩短为 2 分钟，容器内存资源利用率提升 12.5%，CPU 利

用率提升 20%-25%，资源效能大大提升。

技术中台化提升研发效能。借助云原生技术标准化的交互方式，

应用与应用基础设施（编程框架、中间件等）逐步分离，应用基础设

施从专用转为通用，从中心化转为松耦合模块化。应用基础设施下沉

与云平台充分融合，将云能力与应用基础设施能力进行整合封装构筑

统一的技术中台，对业务应用提供简单一致易于使用的应用基础设施

能力接口。技术中台化缩减了重复开发的人力与资源成本，降低了用

户在基础设施层的心智负担，使用户能够聚焦价值更高业务逻辑，提

升研发整体效能。例如，某银行基于容器云搭建技术中台，采用“微

服务+容器化”的云原生技术路线架构，在云化工具、异构组件、云化

管理三个方向上进行了深入应用，通过工具、组件、管理协同，共同

打造了一系列易用的数据服务和数据产品，赋能业务创新，为银行的

数字化改革转型战略做出重要贡献。

流程标准化提升交付效能。研运流程标准化，通过引入 DevOps

理念强化软件研发运营全周期的管理，从软件需求到生产运维的全流

44

云计算发展白皮书（2020 年）

程改进和优化，结合统一工具链，实现文化、流程、工具的一致性，

降低组织内部的沟通与管理障碍，加速业务的流程化、自动化；开发

流程标准化，应用微服务化开发，服务之间使用标准的 API 接口进行

通信。松耦合架构会减轻因需求变更导致的系统迭代成本，为多团队

并行开发提供基础，并加快交付速度；部署流程标准化，标准容器化

的打包方式实现了真正的应用可移植性，不再受限于特定的基础架构

环境。例如，某电力集团的研发一体化平台，实现了业务应用的松耦

合、自治、共享、可扩展、可配置等能力。采用 DevOps 实现开发运

维一体化，形成闭环，提高应用服务生命周期管理效率；采用 API 管

理实现了内外统一的、稳定的、可重用接口层；通过平台云化部署，

打通开发、测试、运维通道，实现软件持续开发、持续测试、持续构

建，实现了交付效能的显著提升。

（三）IT 云化管理平台作用凸显，技术服务助力企业

转型升级

企业数字基础设施和信息技术服务为当代企业提供生产工具，是

企业数字化转型中生产力和效率提高的关键。如何用基于云计算技术

的平台统一管理企业数字基础设施、用产品和服务运营的思路服务业

务部门是企业数字化转型中最重要的环节，企业面临着向 IT 云化管

理和服务运营的变革转型。水平较高的企业寻求打造以数据为轴的 IT

云化管理中台，发挥以客户为中心的服务运营价值，对业务部门、客

户和行业产生创新推动的作用，大部分企业寻求 IT 云化管理和服务

运营的最佳实践，以构建支撑企业数字化转型的能力。

45

云计算发展白皮书（2020 年）

来源：中国信息通信研究院

图 19 企业数字基础设施云化管理示意图

企业数字基础设施云化管理是实现 IT 服务敏捷化的基础。一是

使用虚拟化、云原生等技术，构建统一的基础软硬件云化管理平台，

提供纳管多种异构架构的能力，为上层应用提供稳定、安全、敏捷的

资源供给，当有需求来时可以快速的交付资源，比如云主机、对象存

储等产品；二是企业数字基础设施作为信息技术服务的基础，为上层

应用提供敏捷的 IT 服务支撑能力，需要共性组件首先模块化、平台

化，实现从烟囱式工具支撑到标准化、敏捷化工具平台服务的演进，

同时集成完整的前后端开发框架、API 组件、工具发布和工具托管功

能，依托可视化环境和低代码、无代码技术，实现自助式、拖拽式的

工具组装，极大降低开发和维护成本，可提供面向更多复杂场景的低

成本定制化工具服务能力，实现共性组件平台和业务系统的完全集成；

三是面对业务开发、职能部门的需求，需要 IT 服务能够形成标准化

46

云计算发展白皮书（2020 年）

的服务目录和自动化服务交付能力，通过便捷方式快速提供按需定制、

个性化、低成本的工具产品，实现 IT 和各部门的无缝衔接，提供多

场景多层次的 IT 服务能力。

基于产品和数据的服务运营体系有效体现数字基础设施价值。企

业数字化转型中，IT 服务转型的关键是建立标准化、可量化、自动

化的服务运营体系。一是从服务设计、服务运营、服务交付和服务改

进等方面构建完善的服务运营体系，从客户需求出发，运营产品化的

服务和数据，交付层次化的自服务式服务，为内部和外部用户持续输

出有价值的 IT 服务产品；二是在各部门数字一体化的趋势中，数据

贯穿 IT 管理和业务运营全生命周期，基于对数字基础设施、应用系

统、业务间调用和运营数据的统一归集、应用和消费，将运维对象、

客户需求、服务运营数字化，形成完备的数据治理体系，不断改进优

化 IT 服务水平，持续提升 IT 服务价值。

以客户为中心的标准化 IT 管理促进 IT 管理数字化转型升级。企

业数字化转型下，IT 管理转型的关键是管理者以客户为中心的服务

运营思路。一是 IT 管理者更加关注用户体验和客户满意度，从战略

管理上将服务运营理念运用到 IT 管理过程中，打破部门间壁垒，重

组资源配置、业务流程和生产方式，促进企业各方面 IT 管理更加精

准有效，发挥企业整体效能；二是通过合理规划组织架构、人才、资

源与技术的配比，使组织架构与 IT 服务产品能力和服务运营体系相

匹配，以最小的成本投入输出高质量的 IT 服务和产品，促进 IT 服务

能力最大化，持续提升客户满意度，最大化带来价值，实现 IT 部门

47

云计算发展白皮书（2020 年）

从成本中心到价值中心的转型升级。

基于数字化转型下企业对数字基础设施云化管理和 IT 服务运营

的需求，中国信息通信研究院制定了《企业数字基础设施云化管理和

服务运营能力成熟度模型》标准，分别对云化管理和服务运营领域的

能力要求进行详细描述，并针对服务产品化、能力平台化、数据价值

化、管理精益化、服务运营化和风控横贯化等具体能力要素的指标提

出了相应的技术要求，明确企业数字基础设施和信息技术服务在企业

数字化转型中的作用、定位和价值，明确企业 IT 管理的能力，定位

未来改进发展方向。

七、云定位从基础资源向基建操作系统扩展，提升算力

与网络水平

（一）新基建概念明确，云计算既是基础资源也是操作

系统

国家高度重视“新基建”，内涵不断丰富。基础设施的建设对于

经济的发展至关重要，从 2018 年国务院在中央经济工作会议中第一

次提出新型基础设施建设这个概念至今，已经累积超过 10 次中央级

别会议或重要文件强调新型基础设施建设的重要性。进入 2020 年以

来，国家关于新型基础设施建设的部署要求更加密集，政策路线更

加清晰，其内涵也更加丰富。同时，全国各地也纷纷出台相关政策，

不断加快“新基建”步伐，例如广东、湖南、浙江等省推进的云计算、

工业互联网、物联网、5G、人工智能、区块链等重点项目都在万亿

48

云计算发展白皮书（2020 年）

规模。

“新基建”概念范畴广泛。2020 年 4 月 20 日，国家发改委首次就

“新基建”概念作出解释，新一代信息技术引领的新型基础设施建设

也正式确定了其边界。“新基建”具体包含了信息基础设施、融合基

础设施、创新基础设施三个方面。其中，信息基础设施主要是指基

于新一代信息技术演化生成的基础设施，比如，5G、物联网、工业

互联网、卫星互联网为代表的通信网络基础设施，以人工智能、云

计算、区块链等为代表的新技术基础设施，以数据中心、智能计算

中心为代表的算力基础设施等；融合基础设施主要是指深度应用互

联网、大数据、人工智能等新技术，支撑传统基础设施转型升级，

进而形成的融合基础设施；创新基础设施主要是指支撑科学研究、

技术开发、产品研制的具有公益属性的基础设施。“新基建”的三大

基础设施体现出以信息基础设施为核心的特点，融合基础设施强调

的是新技术与传统行业的结合与深度应用，创新基础设施则需要围

绕新技术强化资源投入与设施部署，以更好的布局与建设运行模式

支撑新技术的深度发展，加强科技创新能力。

49

云计算发展白皮书（2020 年）

来源：公开资料整理

图 20 新基建的三方面

云计算成为行业数字化转型的重要基础资源。一方面，北京、

浙江、上海、山东等地纷纷出台推动新基建相关政策，加快云计算

基础设施部署，打造架构先进、应用丰富、设施完善、融合创新的

云计算发展新格局。另一方面，云计算是传统行业数字化转型的重

要基石，各行业用云量保持快速增长态势，政务领域是云计算应用

的“排头兵”，金融、工业领域上云已形成规模化体系，视频、游戏、

交通、能源、农业等行业也在加速上云。

来源：中国信息通信研究院

图 21 云计算在新基建中的作用

信息基础设施是“新基建”的核心，包含了新技术、算力与通信网

络三个部分。如果把信息基础设施比作一个计算机系统，那么云计算

就是其中的“操作系统”。在计算机系统中，操作系统起到了向下管理

和控制计算机硬件与软件资源，向上为各种应用部署运行提供接口和

环境的作用，在信息基础设施中，云计算同样承担了“操作系统”的角

色，主要体现在以下二个方面：

50

云计算发展白皮书（2020 年）

来源：中国信息通信研究院

图 22 信息基础设施与计算机系统的类比

一是云计算为算力和通信网络基础设施提供了资源管理能力。操

作系统的出现帮助人们屏蔽了底层硬件，大大降低了管理这些资源的

成本。而对于信息基础设施而言，算力基础设施和通信网络基础设施

本质上就是计算和网络的规模化，要最终实现其能力的广泛输出需要

云计算这样的“操作系统”来帮助管理，其作用主要体现为：1）云计

算能够简化算力和通信网络基础设施的管理流程。传统计算机系统中，

操作系统代替用户完成了各类硬件资源的驱动和管理工作。而在信息

基础设施中，不仅需要对硬件资源进行管理，还有维护机房、基站等

物理环境的大量工作。云计算能够在基础设施之上构建统一的服务平

台，让用户无需逐个维护这些算力和网络设施，使得管理流程大大简

化。2）云计算能够协调算力和通信网络基础设施之间的协同工作。

计算机中，各类硬件设备之间的协同工作需要依赖于数据编码、指令

系统等底层技术。同样，不同信息基础设施之间的高效协同也需要专

门技术的支撑。云计算基于虚拟化等技术能够通过软件定义的方式实

51

云计算发展白皮书（2020 年）

现算力、网络等基础设施的解耦、构建和绑定，进而实现资源之间的

高效协同工作。

二是云计算为其他新技术基础设施和应用提供了部署环境和技

术支撑能力。1）云计算与人工智能、区块链等新技术基础设施融合

发展加速新技术应用。基于云环境部署新技术应用，可以利用云计算

强大的资源调度与算力整合能力，使中小企业可以快速低成本地进行

新技术的应用研发，进而将人工智能、区块链等新技术能力集成到行

业应用中，比如人工智能部署在云环境的智能云服务，区块链部署在

云环境的 BaaS（Blockchain as a Service）。人工智能、区块链等新技

术与云计算的融合不仅是在技术层面的深度结合，也是服务模式的深

度融合。2）云计算为各种应用部署运行提供了环境和接口能力。类

比于操作系统在计算机系统中为应用提供访问各种资源的接口，云计

算同样是信息基础设施与各行业应用产生交互，进而深度应用的桥梁，

一方面，云计算作为平台为各种服务的交付提供统一出口。“新基建” 对外交付的核心在于服务，在新型基础设施的建设过程中，不论是聚

焦于底层建设的网络服务商、数据中心服务商、算力服务商，还是聚

焦于上层应用的如物联网、区块链、人工智能等服务商，都需要基于

云计算这个平台去进行服务的交付。因此云计算为信息基础设施的服

务化提供了对外的“接口”，这与计算机系统中的任意服务都需要通过

操作系统与用户进行交互是相似的。另一方面，作为一种更为容易获

得的资源，云计算在普惠能力上与其他新技术相比有明显的优势，同

时在服务交付上，云计算的服务化水平要更加成熟。

52

云计算发展白皮书（2020 年）

云计算作为信息基础设施的操作系统，是通信网络、算力与新技

术基础设施进行协同配合的重要结合点，也是整合“网络”与“计算”技

术能力的平台，以云原生为核心的云计算构建面向全域数据高速互联

与算力全覆盖的操作系统架构，进一步提升算力和通信网络基础设施

能力水平，通信网络基础设施层面，对 5G 网络架构进行优化改造，

同时提升数据中心间网络联接能力；算力基础设施层面，加强多种算

力的统一调度，提升算力资源利用效率。

（二）云计算加速网络变革，推动通信网络基础设施优

化升级

通信网络基础设施包含 5G、物联网、工业互联网与卫星互联网，

覆盖不同的业务场景，对网络性能与覆盖程度要求极高。因此需要通

过优化网络架构确保网络的灵活性、智能性和可运维性，提供差异化

的网络服务，从而满足不同的业务需求。云计算是通信网络架构优化

改造的关键技术，主要体现在对 5G 网络架构改造与提升数据中心间

网络联接能力两个方面。

1.云计算对 5G 网络架构进行重塑

5G 网络架构需要满足千倍流量增长、超低时延和海量设备连接

的网络发展需要，基于云的架构是 5G 网络区别于传统网络的显著特

点，也是实现 5G 网络特性的重要技术支撑，云计算技术在 5G 网络

中的应用主要体现在三方面，分别是基于服务化的设计、容器化部署

和网络切片运营模式。

53

云计算发展白皮书（2020 年）

来源：公开资料整理

图 23 云计算对 5G 网络架构改造示意

基于服务化的设计是 5G 核心网的架构保障。服务化架构指的是

基于云原生的设计思路，将网元以软件服务的形式拆分与传统硬件设

备剥离，网元功能被拆分为细粒度的网络服务部署在云化 NFV 平台

的轻量级部署单元上，从而形成模块化、软件化的 5G 柔性网络架构，

为差异化的业务场景提供敏捷的系统架构支持。服务化架构是 5G 核

心网云化的最显著特点，也是 5G 网络满足千倍流量增长、超低时延、

海量设备连接的场景发展需要。采用容器化部署的轻型虚拟方式是

5G 网络灵活敏捷的保障。基于容器的轻型虚拟化，适用于面向上层

应用进程的环境，架构简单、部署灵活。5G 网络切片通过微服务架

构拆分后，多以轻量级的服务形式存在与云平台之中，相比于以传统

虚拟机的形式承载，使用容器部署可以在网络切片的管理、服务、度

量上更加快速的调用、拓展和迭代。以切片为单位的运营模式是 5G

网络服务的创新保障。网络切片是指运营商使用公共基础设施创建多

个虚拟的、逻辑隔离的端到端子片，每个端到端切片均由核心网、传

54

云计算发展白皮书（2020 年）

输网、无线网子切片组合而成，并通过端到端切片管理系统进行统一

管理。网络切片可以为普通消费者、应用提供商和垂直行业需求方定

制功能增强的业务专网来满足特定的云网指标，从而构建云网融合的

应用创新生态。

2.云计算提升数据中心间网络联接能力

在物联网、工业互联网等场景下，数据中心的整体规模与流量模

型与传统数据中心相比发生了巨大的变化，因此数据中心间网络必须

顺应这种变化以支持不同业务或应用。数据中心之间的互联链路除了

要满足带宽、时延等需求外，还需要满足高扩展性、灵活等需求，因

此要求不同数据中心间的互联链路是一个二层通路的同时，需要充分

融合云计算的虚拟化、弹性、灵活等特性，实现网络与云的敏捷打通，

并提升对网络资源的管理能力与使用效率。这种云与网络充分融合的

模式是支撑数据中心网络联接的关键能力，并已经成为体现云服务商

能力水平的重要指标，实现数据中心间互联的技术众多，现在主流的

技术是基于 MPLS 的二层 VPN 技术（如 VPLS）、思科主导的穿越 IP

网络传输 MAC 地址的 OTV 技术，以及快速兴起的 VxLAN+DCI 隧

道技术。

云计算对数据中心间网络的优化为不同数据中心间的互联提供

了更加平滑、扩展性更强的链路，同时屏蔽了因地域差异带来的跨数

据中心流量的流通效率与成本问题，成为了云服务商在通信网络基础

设施建设过程中必不可少的关键技术。

55

云计算发展白皮书（2020 年）

来源：公开资料整理

图 24 多个数据中心之间的网络联接

（三）云计算加强多种算力统一调度，提高算力基础设

施资源利用率

云计算作为虚拟化技术的延伸和拓展，天生就与硬件密不可分。

在新基建背景下，以数据中心和智能计算中心为代表的算力基础设施

本质上是服务器、芯片等硬件资源的集群。一方面，算力基础设施构

成了云服务的硬件基础；另一方面，云计算也反过来对它们进行管理，

通过资源整合、调度、分配等方式提高算力基础设施的整体利用效率。

来源：公开资料整理

图 25 云计算对算力基础设施的资源整合与分配

56

云计算发展白皮书（2020 年）

1.云计算在集群内部进行跨硬件的资源整合

不论是数据中心还是智能计算中心，一台服务器的 CPU、内存、

网卡等资源需要通过主板进行连接从而实现协同工作，而不同服务器

之间的协同则比较困难。因此，对于传统物理服务器而言，一台设备

空闲的 CPU 核心往往因为不能和其他服务器资源进行整合而造成浪

费。在云计算场景下，数据中心或智能计算中心内的计算、存储、网

络等资源将被整合为统一的资源池，而不同设备中原本浪费掉的资源

也能够借助云计算进行跨硬件的协同，从而实现空闲资源价值的最大

化。

同时，云计算通过使用虚拟化技术将原本不可分割的硬件设备进

行逻辑层面的化整为零。例如，一台物理服务器原本只能同时提供一

项服务，其硬件资源的利用率可能只达到 20%到 30%。而借助云计算

进行管理，则可以同时虚拟为多台逻辑服务器。每台逻辑服务器可同

时运行不同的操作系统，并且应用程序都可以在相互独立的空间内运

行而互不影响，从而显著提高工作效率。因此，云计算实现了对硬件

资源的二次分配，进一步提升了算力基础设施的资源利用效率。

2.云计算实现算力资源的弹性调度

对于算力基础设施而言，如何最大化算力资源的利用率是使用中

需要考虑的重要问题。传统场景下，构成算力的物理服务器的资源通

常是以零售的方式进行转让的。受地域等因素的限制，即使资源的所

有者暂时不需要使用算力，往往也只能够让服务器闲置。而云计算则

可以通过弹性的方式实现算力资源的灵活调度，用户能够按照实际需

57

云计算发展白皮书（2020 年）

求自由获取和释放所需要的资源。云计算实现了算力的服务化，使得

单一资源能够在短时间内得到复用，大大提升了算力基础设施的利用

率。

八、云计算发展展望

2020 年是又一个新十年的开端，无论是如火如荼的“新基建”、稳

步推进的企业数字化转型，还是突如其来的疫情，都将云计算发展推

向了一个新的高度。未来十年，云计算将进入全新发展阶段，具体表

现为：

云技术从粗放向精细转型。过去十年，云计算技术快速发展，云

的形态也在不断演进。基于传统技术栈构建的应用包含了太多开发需

求，而传统的虚拟化平台只能提供基本运行的资源，云端强大的服务

能力红利并没有完全得到释放。未来，随着云原生技术进一步成熟和

落地，用户可将应用快速构建和部署到与硬件解耦的平台上，使资源

可调度粒度越来越细、管理越来越方便、效能越来越高。

云需求从 IaaS 向 SaaS 上移。伴随企业上云进程不断深入，企业

用户对云服务的认可度逐步提升，对通过云服务进一步实现降本增效

提出了新诉求。企业用户不再满足于仅仅使用基础设施层服务（IaaS）

完成资源云化，而是期望通过应用软件层服务（SaaS）实现企业管理

和业务系统的全面云化。未来，SaaS 服务必将成为企业上云的重要

抓手，助力企业提升创新能力。

云布局从中心向边缘延伸。随着 5G、物联网等技术的快速发展

和云服务的推动使得边缘计算备受产业关注，但只有云计算与边缘计

58

云计算发展白皮书（2020 年）

算通过紧密协同才能更好地满足各种需求场景的匹配，从而最大化体

现云计算与边缘计算的应用价值。未来，随着新基建的不断落地，构

建端到端的云、网、边一体化架构将是实现全域数据高速互联、应用

整合调度分发以及计算力全覆盖的重要途径。

云安全从外延向原生转变。受传统 IT 系统建设影响，企业上云

时往往重业务而轻安全，安全建设较为滞后，导致安全体系与云上 IT

体系相对割裂，而安全体系内各产品模块间也较为松散，作用局限效

率低。未来，随着原生云安全理念的兴起，安全与云将实现深度融合，

推动云服务商提供更安全的云服务，帮助云计算客户更安全的上云。

云应用从互联网向行业生产渗透。随着全球数字经济发展的进程

不断深入，数字化发展进入了动能转换的新阶段，数字经济的发展重

心由消费互联网向产业互联网转移，数字经济正在进入一个新的时代。

未来，云计算将结合 5G、AI、大数据等技术，为传统企业由电子化

到信息化再到数字化搭建阶梯，通过其技术上的优势帮助企业在传统

业态下的设计、研发、生产、运营、管理、商业等领域进行变革与重

构，进而推动企业重新定位和改进当前的核心业务模式，完成数字化

转型。

云定位从基础资源向基建操作系统扩展。在企业数字化转型的过

程中，云计算被视为一种普惠、灵活的基础资源，随着新基建定义的

明确，云计算的定位也在不断变化，内涵也更加丰富，云计算正成为

管理算力与网络资源，并为其他新技术提供部署环境的操作系统。未

来，云计算将进一步发挥其操作系统属性，深度整合算力、网络与其

59

云计算发展白皮书（2020 年）

他新技术，推动新基建赋能产业结构不断升级。

我们相信，作为新型基础设施建设的中坚力量，云计算的发展必

将助力产业经济的高质量发展！

60

中国信息通信研究院

地址：北京市海淀区花园北路 52 号

邮政编码：100191

联系电话：010-62300072

传真：010-62304980

网址：www.caict.ac.cn

