# coding=utf-8
"""
测试Plot分析 - 非交互式版本
"""
import jieba
import os
import sys
import pandas as pd
import numpy as np
import re
from collections import defaultdict

sys.stdout.reconfigure(encoding='utf-8')

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip() for line in f if line.strip()])
    return stopwords

def clean_text(text):
    """清理文本"""
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def segment_text(text, stopwords):
    """使用jieba进行分词"""
    text = clean_text(text)
    words = jieba.cut(text, cut_all=False)
    
    filtered_words = []
    for word in words:
        word = word.strip()
        if (len(word) >= 2 and 
            word not in stopwords and
            not word.isdigit() and
            not word.isspace()):
            filtered_words.append(word)
    
    return filtered_words

def calculate_dispersion(positions, total_tokens, num_segments=10):
    """计算词语的离散度"""
    if not positions or total_tokens == 0:
        return 0.0
    
    segment_size = total_tokens / num_segments
    segment_counts = [0] * num_segments
    
    for pos in positions:
        segment_idx = min(int(pos / segment_size), num_segments - 1)
        segment_counts[segment_idx] += 1
    
    expected_freq = len(positions) / num_segments
    
    chi_square = 0
    for count in segment_counts:
        if expected_freq > 0:
            chi_square += ((count - expected_freq) ** 2) / expected_freq
    
    max_chi_square = len(positions) * (num_segments - 1)
    if max_chi_square > 0:
        dispersion = 1 - (chi_square / max_chi_square)
    else:
        dispersion = 1.0
    
    return max(0.0, min(1.0, dispersion))

def create_plot_visualization(positions, total_tokens, num_segments=50):
    """创建词语分布的可视化字符串"""
    if not positions:
        return "-" * num_segments
    
    segment_size = total_tokens / num_segments
    plot_chars = ['-'] * num_segments
    
    for pos in positions:
        segment_idx = min(int(pos / segment_size), num_segments - 1)
        plot_chars[segment_idx] = '|'
    
    return ''.join(plot_chars)

def analyze_word_distribution(folder_path, stopwords_file, target_word):
    """分析指定词语在各个文件中的分布"""
    stopwords = load_stopwords(stopwords_file)
    results = []
    file_id = 1
    
    print(f"分析词语 '{target_word}' 的分布情况...")
    
    for filename in sorted(os.listdir(folder_path)):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                words = segment_text(text, stopwords)
                total_tokens = len(words)
                
                positions = []
                for i, word in enumerate(words):
                    if word == target_word:
                        positions.append(i)
                
                frequency = len(positions)
                
                if frequency > 0:
                    dispersion = calculate_dispersion(positions, total_tokens)
                    plot_viz = create_plot_visualization(positions, total_tokens)
                    
                    results.append({
                        'Sequence': len(results) + 1,
                        'FileID': file_id,
                        'Filename': filename,
                        'TotalTokens': total_tokens,
                        'Frequency': frequency,
                        'Dispersion': dispersion,
                        'Plot': plot_viz
                    })
                
                file_id += 1
                print(f"已处理: {filename} (词数: {total_tokens}, 匹配: {frequency})")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
                file_id += 1
    
    return results

def main():
    """主函数"""
    folder_path = "../中文文本"
    stopwords_file = "../stopwords-zh.txt"
    target_word = "数据"  # 固定目标词
    
    print("=== 中文Plot分析测试 (jieba库) ===")
    
    # 执行分析
    results = analyze_word_distribution(folder_path, stopwords_file, target_word)
    
    # 显示结果
    if results:
        print(f"\n=== Plot分析结果 ===")
        print(f"{'序号':<6} {'文件ID':<8} {'文件名':<40} {'总词数':<8} {'频率':<6} {'离散度':<8}")
        print("-" * 80)
        
        for result in results[:10]:  # 显示前10个
            filename = result['Filename'][:38] + '..' if len(result['Filename']) > 40 else result['Filename']
            print(f"{result['Sequence']:<6} {result['FileID']:<8} {filename:<40} "
                  f"{result['TotalTokens']:<8} {result['Frequency']:<6} {result['Dispersion']:<8.4f}")
        
        # 保存结果
        df = pd.DataFrame(results)
        filename = f"jieba_plot_{target_word}.csv"
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"\n结果已保存至: {filename}")
        
        # 显示统计信息
        total_freq = sum(r['Frequency'] for r in results)
        avg_dispersion = np.mean([r['Dispersion'] for r in results])
        
        print(f"\n=== 统计信息 ===")
        print(f"匹配文件数: {len(results)}")
        print(f"总出现次数: {total_freq}")
        print(f"平均离散度: {avg_dispersion:.4f}")
    else:
        print(f"在语料库中未找到词语 '{target_word}'")

if __name__ == "__main__":
    main()
