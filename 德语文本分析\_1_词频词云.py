# coding=utf-8
import spacy
import pandas as pd
import sys
import os
from collections import defaultdict

sys.stdout.reconfigure(encoding='utf-8')

# 加载 spaCy 德语模型
nlp = spacy.load("de_core_news_sm")
nlp.max_length = 5000000


def process_files(folder_path):
    """
    处理文件夹中的所有txt文件，返回词频统计和文件分布信息
    """
    word_info = defaultdict(lambda: {'frequency': 0, 'files': set()})

    # 遍历文件夹中的所有txt文件
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            with open(filepath, 'r', encoding='utf-8') as f:
                text = f.read()

            # 处理文本
            doc = nlp(text)
            tokens = [token.lemma_ for token in doc
                      if not token.is_stop
                      and not token.is_punct
                      and token.is_alpha]

            # 更新统计信息
            unique_words = set(tokens)
            for word in tokens:
                word_info[word]['files'].add(filename)

            for word in tokens:
                word_info[word]['frequency'] += 1

    return word_info


def generate_report(word_info):
    """
    生成包含词频、排名、分布和文件列表的报告
    """
    # 转换为DataFrame
    data = []
    for word, info in word_info.items():
        data.append({
            'Word': word,
            'Frequency': info['frequency'],
            'Range': len(info['files']),
            'Files': ', '.join(sorted(info['files']))
        })

    df = pd.DataFrame(data)

    # 按频率排序并添加排名
    df = df.sort_values(by='Frequency', ascending=False)
    df = df.reset_index(drop=True)
    df.insert(1, 'Rank', df.index + 1)

    return df


def main():
    folder_path = r"D:\桌面\data" # 修改为你的文件夹路径
    output_file = 'word_analysis_report.csv'

    # 处理文件并生成报告
    word_info = process_files(folder_path)
    report_df = generate_report(word_info)

    # 打印前20个结果
    print(report_df.head(20))

    # 保存完整结果到CSV
    report_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n完整分析结果已保存至 {output_file}")


if __name__ == "__main__":
    main()