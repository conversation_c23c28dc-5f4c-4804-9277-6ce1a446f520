# coding=utf-8
"""
德文文本分析 - KWIC分析 (使用NLTK库)
目标：尽可能接近AntConc的分析结果
"""
import nltk
import os
import sys
import pandas as pd
import re
from collections import defaultdict

sys.stdout.reconfigure(encoding='utf-8')

# 确保NLTK数据可用
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip().lower() for line in f if line.strip()])
    return stopwords

def clean_text(text):
    """清理文本，保留原始结构用于KWIC"""
    # 移除过多的空白，但保留基本结构
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def basic_german_segment(text):
    """基础德文分词方法"""
    words = []
    # 保留标点符号的简单分词
    tokens = text.split()
    
    for token in tokens:
        # 处理标点符号
        if re.match(r'^[a-zA-ZäöüÄÖÜß]+$', token):
            words.append(token.lower())
        elif re.match(r'^[a-zA-ZäöüÄÖÜß]+[.,!?;:]$', token):
            # 分离单词和标点
            word = re.sub(r'[.,!?;:]$', '', token)
            punct = token[-1]
            if len(word) >= 2:
                words.append(word.lower())
            words.append(punct)
        else:
            # 其他情况直接添加
            clean_token = re.sub(r'[^a-zA-ZäöüÄÖÜß.,!?;:\s]', '', token)
            if clean_token.strip():
                words.append(clean_token.lower())
    
    return words

def segment_text_for_kwic(text, stopwords):
    """
    使用NLTK进行德文分词（保留更多信息用于KWIC）
    Args:
        text: 输入文本
        stopwords: 停用词集合
    Returns:
        分词后的词汇列表
    """
    # 清理文本
    text = clean_text(text)
    
    try:
        # 使用NLTK的word_tokenize进行分词
        from nltk.tokenize import word_tokenize
        tokens = word_tokenize(text, language='german')
        
        # 保留更多词汇用于KWIC上下文
        words = []
        for token in tokens:
            word = token.strip().lower()
            
            if (len(word) >= 2 and  # 至少2个字符
                not word.isspace() and  # 不是空白
                not word.isdigit() and  # 不是纯数字
                re.match(r'^[a-zA-ZäöüÄÖÜß]+$', word)):  # 只保留德文字母
                words.append(word)
                
    except Exception as e:
        print(f"NLTK分词出错: {e}")
        # 降级到基础分词方法
        words = basic_german_segment(text)
        filtered_words = []
        for word in words:
            word = word.strip().lower()
            if (len(word) >= 2 and 
                not word.isdigit() and
                not word.isspace() and
                re.match(r'^[a-zA-ZäöüÄÖÜß]+$', word)):
                filtered_words.append(word)
        words = filtered_words
    
    return words

def preprocess_folder(folder_path, stopwords_file):
    """
    预处理文件夹中的所有文件
    Args:
        folder_path: 文件夹路径
        stopwords_file: 停用词文件路径
    Returns:
        处理后的文档字典
    """
    stopwords = load_stopwords(stopwords_file)
    all_docs = {}
    word_freq = defaultdict(int)
    
    print(f"开始预处理文件夹: {folder_path}")
    
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                # 分词用于词频统计
                words = segment_text_for_kwic(text, stopwords)
                for word in words:
                    if word not in stopwords:
                        word_freq[word] += 1
                
                # 保存处理后的词汇列表用于KWIC搜索
                all_docs[filename] = words
                print(f"已处理: {filename} (词数: {len(words)})")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    print(f"预处理完成，共处理 {len(all_docs)} 个文件")
    return all_docs, word_freq

def kwic_search(all_docs, keyword, window_size=5):
    """
    执行KWIC搜索
    Args:
        all_docs: 文档字典
        keyword: 搜索关键词
        window_size: 上下文窗口大小
    Returns:
        KWIC结果列表
    """
    results = []
    
    print(f"搜索关键词: '{keyword}'，窗口大小: {window_size}")
    
    for filename, words in all_docs.items():
        # 查找关键词位置
        for i, word in enumerate(words):
            if keyword.lower() in word or word == keyword.lower():
                # 计算上下文范围
                start_idx = max(0, i - window_size)
                end_idx = min(len(words), i + window_size + 1)
                
                # 提取左上下文
                left_context = ' '.join(words[start_idx:i])
                
                # 提取右上下文
                right_context = ' '.join(words[i+1:end_idx])
                
                # 清理上下文
                left_context = left_context.strip()
                right_context = right_context.strip()
                
                results.append({
                    'file': filename,
                    'left': left_context,
                    'keyword': word,
                    'right': right_context,
                    'position': i
                })
    
    return results

def analyze_german_kwic_features(results, keyword):
    """
    分析德文KWIC的特色功能
    Args:
        results: KWIC结果列表
        keyword: 关键词
    Returns:
        德文特色分析结果
    """
    print(f"\n=== 德文KWIC特色分析 ===")
    
    # 分析复合词中的关键词
    compound_matches = []
    for result in results:
        if len(result['keyword']) > len(keyword) and keyword.lower() in result['keyword']:
            compound_matches.append(result['keyword'])
    
    if compound_matches:
        print(f"在复合词中找到 '{keyword}' (共{len(compound_matches)}个):")
        unique_compounds = list(set(compound_matches))[:10]
        for i, compound in enumerate(unique_compounds, 1):
            print(f"{i}. {compound}")
    
    # 分析上下文中的德文特色词汇
    context_words = []
    for result in results:
        context_words.extend(result['left'].split())
        context_words.extend(result['right'].split())
    
    # 找出包含变音符号的上下文词汇
    umlaut_context = [word for word in context_words 
                     if any(char in word for char in 'äöüß')]
    
    if umlaut_context:
        umlaut_freq = defaultdict(int)
        for word in umlaut_context:
            umlaut_freq[word] += 1
        
        print(f"\n上下文中的德文变音符号词汇 (前10个):")
        sorted_umlaut = sorted(umlaut_freq.items(), key=lambda x: x[1], reverse=True)
        for i, (word, freq) in enumerate(sorted_umlaut[:10], 1):
            print(f"{i}. {word} ({freq}次)")
    
    return {
        'compound_matches': compound_matches,
        'umlaut_context': umlaut_context
    }

def print_results(results, max_display=50):
    """打印KWIC结果"""
    print(f"\n=== KWIC分析结果 ===")
    print(f"{'文件':<30} {'左上下文':<30} {'关键词':<15} {'右上下文':<30}")
    print("-" * 105)
    
    for i, result in enumerate(results[:max_display]):
        filename = result['file'][:28] + '..' if len(result['file']) > 30 else result['file']
        left = result['left'][-28:] if len(result['left']) > 30 else result['left']
        keyword = result['keyword']
        right = result['right'][:28] if len(result['right']) > 30 else result['right']
        
        print(f"{filename:<30} {left:<30} {keyword:<15} {right:<30}")

def save_results(results, keyword):
    """保存KWIC结果到CSV文件"""
    if not results:
        print("没有结果可保存")
        return
    
    df = pd.DataFrame(results)
    filename = f"nltk_german_KWIC_{keyword}.csv"
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"结果已保存至: {filename}")

def main():
    """主函数"""
    # 配置路径
    folder_path = "../德语文本"
    stopwords_file = "../stopwords-de.txt"
    keyword = "china"  # 固定关键词用于测试
    window_size = 5
    
    print("=== 德文KWIC分析 (NLTK库) ===")
    print("目标：尽可能接近AntConc的分析结果")
    print("特色：德文语言特色分析\n")
    
    # 预处理文件
    all_docs, word_freq = preprocess_folder(folder_path, stopwords_file)
    
    if not all_docs:
        print("未找到有效的文档")
        return
    
    # 显示高频词供参考
    print("\n=== 高频词参考 (前20个) ===")
    top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:20]
    for word, freq in top_words:
        print(f"{word}: {freq}")
    
    # 执行搜索
    results = kwic_search(all_docs, keyword, window_size)
    
    # 显示结果
    if results:
        print(f"\n找到 {len(results)} 处匹配")
        print_results(results)
        
        # 保存结果
        save_results(results, keyword)
        
        # 德文特色分析
        german_features = analyze_german_kwic_features(results, keyword)
        
        # 显示前10个结果
        print(f"\n前10个匹配结果:")
        for i, result in enumerate(results[:10]):
            print(f"{i+1}. {result['left']} [{result['keyword']}] {result['right']}")
    else:
        print(f"未找到关键词 '{keyword}' 的匹配结果")

if __name__ == "__main__":
    main()
