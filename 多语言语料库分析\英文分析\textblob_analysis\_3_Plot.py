# coding=utf-8
"""
英文文本分析 - Plot分析 (使用TextBlob库)
目标：尽可能接近AntConc的分析结果
"""
from textblob import TextBlob
import os
import sys
import pandas as pd
import numpy as np
import re
from collections import defaultdict

sys.stdout.reconfigure(encoding='utf-8')

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip().lower() for line in f if line.strip()])
    return stopwords

def clean_text(text):
    """清理文本"""
    text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def segment_text(text, stopwords):
    """使用TextBlob进行英文分词"""
    text = clean_text(text)
    
    try:
        blob = TextBlob(text)
        
        filtered_words = []
        for word in blob.words:
            word_lower = word.lower()
            
            # 词形还原
            try:
                lemma = word.lemmatize()
                final_word = lemma.lower()
            except:
                final_word = word_lower
            
            if (len(final_word) >= 2 and 
                final_word not in stopwords and
                not final_word.isdigit() and
                final_word.isalpha()):
                filtered_words.append(final_word)
                
    except Exception as e:
        print(f"TextBlob处理出错: {e}")
        # 降级到基础分词
        words = text.lower().split()
        filtered_words = []
        for word in words:
            clean_word = re.sub(r'[^a-zA-Z]', '', word)
            if (len(clean_word) >= 2 and 
                clean_word not in stopwords and
                not clean_word.isdigit()):
                filtered_words.append(clean_word)
    
    return filtered_words

def calculate_dispersion(positions, total_tokens, num_segments=10):
    """
    计算词语的离散度
    Args:
        positions: 词语在文本中的位置列表
        total_tokens: 文本总词数
        num_segments: 分段数量
    Returns:
        离散度值 (0-1之间，1表示完全均匀分布)
    """
    if not positions or total_tokens == 0:
        return 0.0
    
    # 将文本分成若干段
    segment_size = total_tokens / num_segments
    segment_counts = [0] * num_segments
    
    # 统计每段中词语出现的次数
    for pos in positions:
        segment_idx = min(int(pos / segment_size), num_segments - 1)
        segment_counts[segment_idx] += 1
    
    # 计算期望频率
    expected_freq = len(positions) / num_segments
    
    # 计算卡方统计量
    chi_square = 0
    for count in segment_counts:
        if expected_freq > 0:
            chi_square += ((count - expected_freq) ** 2) / expected_freq
    
    # 归一化到0-1范围，值越大表示分布越不均匀
    max_chi_square = len(positions) * (num_segments - 1)
    if max_chi_square > 0:
        dispersion = 1 - (chi_square / max_chi_square)
    else:
        dispersion = 1.0
    
    return max(0.0, min(1.0, dispersion))

def create_plot_visualization(positions, total_tokens, num_segments=50):
    """
    创建词语分布的可视化字符串
    Args:
        positions: 词语位置列表
        total_tokens: 总词数
        num_segments: 可视化分段数
    Returns:
        可视化字符串
    """
    if not positions:
        return "-" * num_segments
    
    segment_size = total_tokens / num_segments
    plot_chars = ['-'] * num_segments
    
    for pos in positions:
        segment_idx = min(int(pos / segment_size), num_segments - 1)
        plot_chars[segment_idx] = '|'
    
    return ''.join(plot_chars)

def analyze_word_sentiment_distribution(folder_path, target_word):
    """
    分析目标词在不同文件中的情感分布
    Args:
        folder_path: 文件夹路径
        target_word: 目标词语
    Returns:
        情感分析结果
    """
    print(f"\n=== 词语 '{target_word}' 情感分布分析 ===")
    
    sentiment_results = []
    
    for filename in sorted(os.listdir(folder_path)):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                # 检查是否包含目标词
                if target_word.lower() in text.lower():
                    blob = TextBlob(text)
                    polarity = blob.sentiment.polarity
                    subjectivity = blob.sentiment.subjectivity
                    
                    sentiment_results.append({
                        'filename': filename,
                        'polarity': polarity,
                        'subjectivity': subjectivity
                    })
                    
                    sentiment = "正面" if polarity > 0.1 else "负面" if polarity < -0.1 else "中性"
                    print(f"{filename}: {sentiment} (极性: {polarity:.3f})")
                    
            except Exception as e:
                print(f"分析文件 {filename} 时出错: {e}")
    
    if sentiment_results:
        avg_polarity = sum(s['polarity'] for s in sentiment_results) / len(sentiment_results)
        print(f"\n包含 '{target_word}' 的文件平均情感极性: {avg_polarity:.3f}")
    
    return sentiment_results

def analyze_word_distribution(folder_path, stopwords_file, target_word):
    """
    分析指定词语在各个文件中的分布
    Args:
        folder_path: 文件夹路径
        stopwords_file: 停用词文件路径
        target_word: 目标词语
    Returns:
        分析结果列表
    """
    stopwords = load_stopwords(stopwords_file)
    results = []
    file_id = 1
    
    print(f"分析词语 '{target_word}' 的分布情况...")
    
    for filename in sorted(os.listdir(folder_path)):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                # 分词
                words = segment_text(text, stopwords)
                total_tokens = len(words)
                
                # 查找目标词语的位置
                positions = []
                for i, word in enumerate(words):
                    if word.lower() == target_word.lower():
                        positions.append(i)
                
                frequency = len(positions)
                
                if frequency > 0:
                    # 计算离散度
                    dispersion = calculate_dispersion(positions, total_tokens)
                    
                    # 创建分布图
                    plot_viz = create_plot_visualization(positions, total_tokens)
                    
                    results.append({
                        'Sequence': len(results) + 1,
                        'FileID': file_id,
                        'Filename': filename,
                        'TotalTokens': total_tokens,
                        'Frequency': frequency,
                        'Dispersion': dispersion,
                        'Plot': plot_viz
                    })
                
                file_id += 1
                print(f"已处理: {filename} (词数: {total_tokens}, 匹配: {frequency})")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
                file_id += 1
    
    return results

def print_results(results):
    """打印分析结果"""
    if not results:
        print("没有找到匹配的结果")
        return
    
    print(f"\n=== Plot分析结果 ===")
    print(f"{'序号':<6} {'文件ID':<8} {'文件名':<40} {'总词数':<8} {'频率':<6} {'离散度':<8}")
    print("-" * 80)
    
    for result in results:
        filename = result['Filename'][:38] + '..' if len(result['Filename']) > 40 else result['Filename']
        print(f"{result['Sequence']:<6} {result['FileID']:<8} {filename:<40} "
              f"{result['TotalTokens']:<8} {result['Frequency']:<6} {result['Dispersion']:<8.4f}")
    
    print(f"\n=== 分布图示例 (前5个文件) ===")
    for i, result in enumerate(results[:5]):
        print(f"{result['Filename'][:30]}:")
        print(f"  {result['Plot']}")
        print()

def save_results(results, target_word):
    """保存结果到CSV文件"""
    if not results:
        print("没有结果可保存")
        return
    
    df = pd.DataFrame(results)
    filename = f"textblob_plot_{target_word}.csv"
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"结果已保存至: {filename}")

def main():
    """主函数"""
    # 配置路径
    folder_path = "../英文文本"
    stopwords_file = "../stopwords-en.txt"
    target_word = "china"  # 固定目标词
    
    print("=== 英文Plot分析 (TextBlob库) ===")
    print("目标：尽可能接近AntConc的分析结果")
    print("特色：包含情感分析功能\n")
    
    # 执行分析
    results = analyze_word_distribution(folder_path, stopwords_file, target_word)
    
    # 显示结果
    if results:
        print_results(results)
        
        # 显示统计信息
        total_freq = sum(r['Frequency'] for r in results)
        avg_dispersion = np.mean([r['Dispersion'] for r in results])
        
        print(f"\n=== 统计信息 ===")
        print(f"匹配文件数: {len(results)}")
        print(f"总出现次数: {total_freq}")
        print(f"平均离散度: {avg_dispersion:.4f}")
        
        # TextBlob特色功能：情感分析
        sentiment_results = analyze_word_sentiment_distribution(folder_path, target_word)
        
        # 保存结果
        save_results(results, target_word)
    else:
        print(f"在语料库中未找到词语 '{target_word}'")

if __name__ == "__main__":
    main()
