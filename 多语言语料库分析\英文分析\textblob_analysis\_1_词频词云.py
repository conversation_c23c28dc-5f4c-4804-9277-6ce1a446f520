# coding=utf-8
"""
英文文本分析 - 词频分析 (使用TextBlob库)
目标：尽可能接近AntConc的分析结果
"""
from textblob import TextBlob
import pandas as pd
import sys
import os
from collections import defaultdict, Counter
import re

sys.stdout.reconfigure(encoding='utf-8')

def load_stopwords(stopwords_file):
    """
    加载停用词列表
    Args:
        stopwords_file: 停用词文件路径
    Returns:
        停用词集合
    """
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip().lower() for line in f if line.strip()])
    
    # 添加标点符号作为停用词
    punctuation = set(['.', ',', '!', '?', ';', ':', '(', ')', '[', ']', '{', '}', 
                      '<', '>', '/', '\\', '|', '-', '_', '+', '=', '*', '&', '^', 
                      '%', '$', '#', '@', '~', '`', '"', "'"])
    stopwords.update(punctuation)
    
    return stopwords

def clean_text(text):
    """清理文本，移除特殊字符和多余空白"""
    # 移除特殊字符，保留英文字母、数字
    text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
    # 移除多余空白
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def segment_text(text, stopwords):
    """
    使用TextBlob进行英文分词和词形还原
    Args:
        text: 输入文本
        stopwords: 停用词集合
    Returns:
        分词后的词汇列表
    """
    # 清理文本
    text = clean_text(text)
    
    try:
        # 使用TextBlob处理
        blob = TextBlob(text)
        
        # 获取词汇并进行词形还原
        filtered_words = []
        for word in blob.words:
            # 转换为小写
            word_lower = word.lower()
            
            # 词形还原
            try:
                lemma = word.lemmatize()
                final_word = lemma.lower()
            except:
                final_word = word_lower
            
            # 过滤条件
            if (len(final_word) >= 2 and  # 至少2个字符
                final_word not in stopwords and
                not final_word.isdigit() and  # 不是纯数字
                final_word.isalpha()):  # 只保留字母
                filtered_words.append(final_word)
                
    except Exception as e:
        print(f"TextBlob处理出错: {e}")
        # 降级到基础分词
        words = text.lower().split()
        filtered_words = []
        for word in words:
            clean_word = re.sub(r'[^a-zA-Z]', '', word)
            if (len(clean_word) >= 2 and 
                clean_word not in stopwords and
                not clean_word.isdigit()):
                filtered_words.append(clean_word)
    
    return filtered_words

def process_files(folder_path, stopwords_file):
    """
    处理文件夹中的所有文本文件
    Args:
        folder_path: 文件夹路径
        stopwords_file: 停用词文件路径
    Returns:
        词频统计字典
    """
    stopwords = load_stopwords(stopwords_file)
    word_freq = Counter()
    file_count = 0
    total_words = 0
    
    print(f"开始处理文件夹: {folder_path}")
    print("使用TextBlob库进行分词和词形还原")
    
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                # 分词
                words = segment_text(text, stopwords)
                
                # 统计词频
                word_freq.update(words)
                total_words += len(words)
                file_count += 1
                
                print(f"已处理: {filename} (词数: {len(words)})")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    print(f"\n处理完成:")
    print(f"- 文件数量: {file_count}")
    print(f"- 总词数: {total_words}")
    print(f"- 不重复词数: {len(word_freq)}")
    
    return word_freq

def generate_report(word_freq):
    """
    生成词频分析报告
    Args:
        word_freq: 词频统计Counter对象
    Returns:
        DataFrame格式的报告
    """
    # 转换为列表并排序
    word_list = [(word, freq) for word, freq in word_freq.most_common()]
    
    # 计算百分比
    total_words = sum(freq for _, freq in word_list)
    
    report_data = []
    for rank, (word, freq) in enumerate(word_list, 1):
        percentage = (freq / total_words) * 100
        report_data.append({
            'Rank': rank,
            'Word': word,
            'Frequency': freq,
            'Percentage': round(percentage, 4)
        })
    
    return pd.DataFrame(report_data)

def analyze_sentiment_overview(folder_path):
    """
    使用TextBlob进行情感分析概览
    Args:
        folder_path: 文件夹路径
    Returns:
        情感分析结果
    """
    print("\n=== TextBlob情感分析概览 ===")
    
    sentiment_scores = []
    
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                blob = TextBlob(text)
                polarity = blob.sentiment.polarity
                subjectivity = blob.sentiment.subjectivity
                
                sentiment_scores.append({
                    'file': filename,
                    'polarity': polarity,
                    'subjectivity': subjectivity
                })
                
                # 情感分类
                if polarity > 0.1:
                    sentiment = "正面"
                elif polarity < -0.1:
                    sentiment = "负面"
                else:
                    sentiment = "中性"
                
                print(f"{filename}: {sentiment} (极性: {polarity:.3f}, 主观性: {subjectivity:.3f})")
                
            except Exception as e:
                print(f"分析文件 {filename} 时出错: {e}")
    
    if sentiment_scores:
        avg_polarity = sum(s['polarity'] for s in sentiment_scores) / len(sentiment_scores)
        avg_subjectivity = sum(s['subjectivity'] for s in sentiment_scores) / len(sentiment_scores)
        
        print(f"\n整体情感分析:")
        print(f"- 平均极性: {avg_polarity:.3f} ({'正面' if avg_polarity > 0 else '负面' if avg_polarity < 0 else '中性'})")
        print(f"- 平均主观性: {avg_subjectivity:.3f}")
    
    return sentiment_scores

def main():
    """主函数"""
    # 配置路径
    folder_path = "../英文文本"  # 英文文本文件夹
    stopwords_file = "../stopwords-en.txt"  # 英文停用词文件
    output_file = 'textblob_word_frequency_report.csv'
    
    print("=== 英文词频分析 (TextBlob库) ===")
    print("目标：尽可能接近AntConc的分析结果")
    print("特色：包含情感分析功能\n")
    
    # 处理文件并生成报告
    word_freq = process_files(folder_path, stopwords_file)
    
    if not word_freq:
        print("未找到有效的词汇数据")
        return
    
    report_df = generate_report(word_freq)
    
    # 显示前20个结果
    print("\n=== 前20个高频词 ===")
    print(report_df.head(20).to_string(index=False))
    
    # 保存完整结果到CSV
    report_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n完整分析结果已保存至: {output_file}")
    
    # 显示统计信息
    print(f"\n=== 统计信息 ===")
    print(f"总词汇数: {len(report_df)}")
    print(f"总频次: {report_df['Frequency'].sum()}")
    print(f"平均频次: {report_df['Frequency'].mean():.2f}")
    
    # TextBlob特色功能：情感分析
    sentiment_results = analyze_sentiment_overview(folder_path)
    
    # 显示分词方法说明
    print(f"\n=== TextBlob特色功能 ===")
    print("1. 简单易用的API")
    print("2. 内置词形还原功能")
    print("3. 情感分析功能")
    print("4. 词性标注")
    print("5. 名词短语提取")

if __name__ == "__main__":
    main()
