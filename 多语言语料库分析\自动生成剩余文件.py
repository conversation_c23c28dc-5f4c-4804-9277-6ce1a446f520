# coding=utf-8
"""
自动生成剩余的多语言语料库分析文件
"""
import os

def create_test_files():
    """创建测试文件"""
    
    # 英文NLTK的KWIC分析
    english_kwic = '''# coding=utf-8
"""
英文文本分析 - KWIC分析 (使用NLTK库)
"""
import nltk
import os
import sys
import pandas as pd
import re
from collections import defaultdict
from nltk.tokenize import word_tokenize

sys.stdout.reconfigure(encoding='utf-8')

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip().lower() for line in f if line.strip()])
    return stopwords

def kwic_search(all_docs, keyword, window_size=5):
    """执行KWIC搜索"""
    results = []
    
    for filename, text in all_docs.items():
        tokens = word_tokenize(text.lower())
        
        for i, token in enumerate(tokens):
            if keyword.lower() in token or token == keyword.lower():
                start_idx = max(0, i - window_size)
                end_idx = min(len(tokens), i + window_size + 1)
                
                left_context = ' '.join(tokens[start_idx:i])
                right_context = ' '.join(tokens[i+1:end_idx])
                
                results.append({
                    'file': filename,
                    'left': left_context,
                    'keyword': token,
                    'right': right_context,
                    'position': i
                })
    
    return results

def main():
    """主函数"""
    folder_path = "../英文文本"
    stopwords_file = "../stopwords-en.txt"
    keyword = "china"  # 固定关键词
    
    print("=== 英文KWIC分析 (NLTK库) ===")
    
    all_docs = {}
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            with open(filepath, 'r', encoding='utf-8') as f:
                all_docs[filename] = f.read()
    
    results = kwic_search(all_docs, keyword)
    
    if results:
        df = pd.DataFrame(results)
        filename = f"nltk_KWIC_{keyword}.csv"
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"找到 {len(results)} 处匹配，结果已保存至: {filename}")
    else:
        print(f"未找到关键词 '{keyword}' 的匹配结果")

if __name__ == "__main__":
    main()'''
    
    with open('多语言语料库分析/英文分析/nltk_analysis/_2_KWIC.py', 'w', encoding='utf-8') as f:
        f.write(english_kwic)
    
    # 德文spaCy的KWIC分析
    german_kwic = '''# coding=utf-8
"""
德文文本分析 - KWIC分析 (使用spaCy库)
"""
import spacy
import os
import sys
import pandas as pd
import re
from collections import defaultdict

sys.stdout.reconfigure(encoding='utf-8')

try:
    nlp = spacy.load("de_core_news_sm")
    nlp.max_length = 5000000
except OSError:
    print("请先安装德文模型: python -m spacy download de_core_news_sm")
    sys.exit(1)

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip().lower() for line in f if line.strip()])
    return stopwords

def kwic_search(all_docs, keyword, window_size=5):
    """执行KWIC搜索"""
    results = []
    
    for filename, text in all_docs.items():
        doc = nlp(text)
        tokens = [token.text for token in doc]
        
        for i, token_text in enumerate(tokens):
            if keyword.lower() in token_text.lower() or token_text.lower() == keyword.lower():
                start_idx = max(0, i - window_size)
                end_idx = min(len(tokens), i + window_size + 1)
                
                left_context = ''.join(tokens[start_idx:i])
                right_context = ''.join(tokens[i+1:end_idx])
                
                results.append({
                    'file': filename,
                    'left': left_context.strip(),
                    'keyword': token_text,
                    'right': right_context.strip(),
                    'position': i
                })
    
    return results

def main():
    """主函数"""
    folder_path = "../德语文本"
    stopwords_file = "../stopwords-de.txt"
    keyword = "china"  # 固定关键词
    
    print("=== 德文KWIC分析 (spaCy库) ===")
    
    all_docs = {}
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            with open(filepath, 'r', encoding='utf-8') as f:
                all_docs[filename] = f.read()
    
    results = kwic_search(all_docs, keyword)
    
    if results:
        df = pd.DataFrame(results)
        filename = f"spacy_KWIC_{keyword}.csv"
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"找到 {len(results)} 处匹配，结果已保存至: {filename}")
    else:
        print(f"未找到关键词 '{keyword}' 的匹配结果")

if __name__ == "__main__":
    main()'''
    
    with open('多语言语料库分析/德文分析/spacy_analysis/_2_KWIC.py', 'w', encoding='utf-8') as f:
        f.write(german_kwic)
    
    print("已创建测试文件")

def create_visualization_files():
    """创建可视化文件"""
    
    # 英文NLTK可视化
    english_vis = '''# coding=utf-8
"""
英文文本分析 - 可视化 (使用NLTK库)
"""
import os
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import WordCloud, Bar, Tab
from pyecharts.components import Table

class EnglishNLTKVisualizer:
    """英文文本可视化类 - NLTK库版本"""

    def __init__(self):
        self.tab = Tab()

    def add_title(self, title, subtitle=""):
        return opts.TitleOpts(title=title, subtitle=subtitle, pos_left="center")

    def visualize_word_frequency(self, csv_file):
        """可视化词频分析结果"""
        if not os.path.exists(csv_file):
            return None

        df = pd.read_csv(csv_file)
        words_freq = list(zip(df['Word'].tolist(), df['Frequency'].tolist()))

        wordcloud = (
            WordCloud()
            .add("", words_freq[:100], word_size_range=[15, 80])
            .set_global_opts(title_opts=self.add_title("英文词频分析 - 词云图 (NLTK库)"))
        )

        top_20 = df.head(20)
        bar = (
            Bar()
            .add_xaxis(top_20['Word'].tolist())
            .add_yaxis("词频", top_20['Frequency'].tolist())
            .set_global_opts(title_opts=self.add_title("英文词频分析 - 前20高频词 (NLTK库)"))
        )

        return wordcloud, bar

    def generate_visualization(self):
        """生成完整的可视化报告"""
        print("=== 英文文本分析可视化 (NLTK库) ===")
        
        # 创建主页
        table_data = [["词频分析", "使用NLTK库进行英文分词和词形还原"]]
        table = Table()
        table.add(["分析类型", "说明"], table_data)
        self.tab.add(table, "首页")

        # 词频分析
        if os.path.exists('nltk_word_frequency_report.csv'):
            result = self.visualize_word_frequency('nltk_word_frequency_report.csv')
            if result:
                wordcloud, bar = result
                self.tab.add(wordcloud, "词频分析-词云图")
                self.tab.add(bar, "词频分析-柱状图")

        # 渲染
        output_file = "英文文本分析可视化_NLTK库.html"
        self.tab.render(output_file)
        print(f"可视化报告已生成: {output_file}")

def main():
    visualizer = EnglishNLTKVisualizer()
    visualizer.generate_visualization()

if __name__ == "__main__":
    main()'''
    
    with open('多语言语料库分析/英文分析/nltk_analysis/_7_可视化.py', 'w', encoding='utf-8') as f:
        f.write(english_vis)
    
    # 德文spaCy可视化
    german_vis = '''# coding=utf-8
"""
德文文本分析 - 可视化 (使用spaCy库)
"""
import os
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import WordCloud, Bar, Tab
from pyecharts.components import Table

class GermanSpacyVisualizer:
    """德文文本可视化类 - spaCy库版本"""

    def __init__(self):
        self.tab = Tab()

    def add_title(self, title, subtitle=""):
        return opts.TitleOpts(title=title, subtitle=subtitle, pos_left="center")

    def visualize_word_frequency(self, csv_file):
        """可视化词频分析结果"""
        if not os.path.exists(csv_file):
            return None

        df = pd.read_csv(csv_file)
        words_freq = list(zip(df['Word'].tolist(), df['Frequency'].tolist()))

        wordcloud = (
            WordCloud()
            .add("", words_freq[:100], word_size_range=[15, 80])
            .set_global_opts(title_opts=self.add_title("德文词频分析 - 词云图 (spaCy库)"))
        )

        top_20 = df.head(20)
        bar = (
            Bar()
            .add_xaxis(top_20['Word'].tolist())
            .add_yaxis("词频", top_20['Frequency'].tolist())
            .set_global_opts(title_opts=self.add_title("德文词频分析 - 前20高频词 (spaCy库)"))
        )

        return wordcloud, bar

    def generate_visualization(self):
        """生成完整的可视化报告"""
        print("=== 德文文本分析可视化 (spaCy库) ===")
        
        # 创建主页
        table_data = [["词频分析", "使用spaCy库进行德文分词和词形还原"]]
        table = Table()
        table.add(["分析类型", "说明"], table_data)
        self.tab.add(table, "首页")

        # 词频分析
        if os.path.exists('spacy_word_frequency_report.csv'):
            result = self.visualize_word_frequency('spacy_word_frequency_report.csv')
            if result:
                wordcloud, bar = result
                self.tab.add(wordcloud, "词频分析-词云图")
                self.tab.add(bar, "词频分析-柱状图")

        # 渲染
        output_file = "德文文本分析可视化_spaCy库.html"
        self.tab.render(output_file)
        print(f"可视化报告已生成: {output_file}")

def main():
    visualizer = GermanSpacyVisualizer()
    visualizer.generate_visualization()

if __name__ == "__main__":
    main()'''
    
    with open('多语言语料库分析/德文分析/spacy_analysis/_7_可视化.py', 'w', encoding='utf-8') as f:
        f.write(german_vis)
    
    print("已创建可视化文件")

def main():
    """主函数"""
    print("开始创建剩余的分析文件...")
    create_test_files()
    create_visualization_files()
    print("文件创建完成！")

if __name__ == "__main__":
    main()
