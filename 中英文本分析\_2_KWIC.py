# coding=utf-8
import jieba
import os
import sys
from collections import defaultdict
import pandas as pd
import nltk
from nltk.tokenize import word_tokenize

sys.stdout.reconfigure(encoding='utf-8')
nltk.download('punkt')

# 全局词频统计
word_freq = defaultdict(int)


def load_stopwords(stopwords_file=None):
    """
    加载停用词列表
    """
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip() for line in f])
    stopwords.update(
        ['，', '。', '！', '？', '、', '；', '：', '"', '"', ''', ''', '（', '）', '【', '】', '.', ',', '!', '?', ';', ':', '(',
         ')', '[', ']'])
    return stopwords


def is_chinese(text):
    """判断文本是否主要为中文"""
    for char in text:
        if '\u4e00' <= char <= '\u9fa5':
            return True
    return False


def preprocess_folder(folder_path, stopwords_file=None):
    """
    预处理文件夹并统计全局词频
    """
    all_texts = {}
    stopwords = load_stopwords(stopwords_file)

    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            with open(filepath, 'r', encoding='utf-8') as f:
                text = f.read()

            # 分句
            sentences = []
            for sent in text.split('。'):
                if sent.strip():
                    # 根据文本语言类型选择分词方法
                    if is_chinese(sent):
                        tokens = [word for word in jieba.cut(sent)
                                  if word not in stopwords and len(word.strip()) > 0]
                    else:
                        tokens = [word.lower() for word in word_tokenize(sent)
                                  if word.lower() not in [sw.lower() for sw in stopwords]
                                  and len(word.strip()) > 0]
                    if tokens:
                        sentences.append(tokens)
                        # 统计全局词频
                        for token in tokens:
                            word_freq[token] += 1

            all_texts[filename] = sentences

    return all_texts


def get_context_words(tokens, center_index, window_size):
    """
    获取指定窗口大小的上下文词列表
    """
    left_start = max(0, center_index - window_size)
    left = tokens[left_start:center_index]

    right_end = min(len(tokens), center_index + window_size + 1)
    right = tokens[center_index + 1:right_end]

    return left, right


def generate_sort_key(right_context):
    """
    生成排序键：根据右上下文的词频生成优先级元组
    """
    return tuple(word_freq.get(word, 0) for word in right_context)


def kwic_search(all_texts, keyword, window_size=5):
    """
    执行KWIC搜索
    """
    results = []

    for filename, sentences in all_texts.items():
        # 在句子级别遍历
        for sent_tokens in sentences:
            for i, token in enumerate(sent_tokens):
                # 精确匹配关键词
                if token == keyword:
                    left, right = get_context_words(sent_tokens, i, window_size)

                    # 构建结果条目
                    entry = {
                        'file': filename,
                        'left': ' '.join(left),
                        'keyword': token,
                        'right': ' '.join(right),
                        'sort_key': generate_sort_key(right)
                    }
                    results.append(entry)

    # 按右上下文的词频排序（降序）
    results.sort(key=lambda x: x['sort_key'], reverse=True)
    return results


def print_results(results, max_lines=50):
    """
    格式化输出结果
    """
    print("\n{:<20} | {:<40} | {:<20} | {}".format(
        'File', 'Left Context', 'Keyword', 'Right Context'))
    print("-" * 120)

    for item in results[:max_lines]:
        print("{:<20} | {:<40} | {:<20} | {}".format(
            item['file'],
            item['left'][-40:],  # 截取最后40字符保持对齐
            item['keyword'],
            item['right'][:40]))  # 截取前40字符


def main():
    folder_path = r"英文新闻"
    stopwords_file = r"stopwords-zh.txt"  # 可选：指定停用词文件路径
    all_texts = preprocess_folder(folder_path, stopwords_file)

    # 交互式输入
    keyword = input("请输入要搜索的关键词：").strip()
    window_size = int(input("请输入上下文窗口大小：").strip())

    # 执行搜索
    results = kwic_search(all_texts, keyword, window_size)

    # 显示结果
    if results:
        print(f"\n找到 {len(results)} 处匹配，显示前50条：")
        print_results(results)

        # 保存完整结果
        save = input("\n是否保存完整结果到CSV？(y/n): ").lower()
        if save == 'y':
            df = pd.DataFrame(results)
            df.to_csv(f"KWIC_{keyword}.csv", index=False, columns=['file', 'left', 'keyword', 'right'],
                      encoding='utf-8-sig')
            print("已保存结果文件")
    else:
        print("未找到匹配结果")


if __name__ == "__main__":
    main()