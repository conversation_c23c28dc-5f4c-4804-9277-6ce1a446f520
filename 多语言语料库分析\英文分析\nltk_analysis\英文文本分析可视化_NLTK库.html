<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts-wordcloud.min.js"></script>

    
</head>
<body >
            <style>
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
        }

        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 12px 16px;
            transition: 0.3s;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #ccc;
        }

        .chart-container {
            display: block;
        }

        .chart-container:nth-child(n+2) {
            display: none;
        }
    </style>
    <div class="tab">
            <button class="tablinks" onclick="showChart(event, '15f20e397eef4dc891ac2dc096f2e458')">首页</button>
            <button class="tablinks" onclick="showChart(event, '97308a52659745c79be2c945bb21762d')">词频分析-词云图</button>
            <button class="tablinks" onclick="showChart(event, '9500253a15bf44aeb168e2861d858755')">词频分析-柱状图</button>
            <button class="tablinks" onclick="showChart(event, '70cc1f3fa02343fb95d117fd1021102c')">KWIC分析</button>
    </div>

    <div class="box">
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="15f20e397eef4dc891ac2dc096f2e458" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>分析类型</th>
            <th>说明</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>词频分析</td>
            <td>使用NLTK库进行英文分词和词形还原</td>
        </tr>
        <tr>
            <td>KWIC分析</td>
            <td>关键词上下文索引，基于NLTK分词结果</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="97308a52659745c79be2c945bb21762d" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('97308a52659745c79be2c945bb21762d').style.width = document.getElementById('97308a52659745c79be2c945bb21762d').parentNode.clientWidth + 'px';
        var chart_97308a52659745c79be2c945bb21762d = echarts.init(
            document.getElementById('97308a52659745c79be2c945bb21762d'), 'white', {renderer: 'canvas'});
        var option_97308a52659745c79be2c945bb21762d = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "wordCloud",
            "shape": "circle",
            "rotationRange": [
                -90,
                90
            ],
            "rotationStep": 45,
            "girdSize": 20,
            "sizeRange": [
                15,
                80
            ],
            "data": [
                {
                    "name": "china",
                    "value": 45,
                    "textStyle": {
                        "color": "rgb(59,58,67)"
                    }
                },
                {
                    "name": "trump",
                    "value": 33,
                    "textStyle": {
                        "color": "rgb(63,1,44)"
                    }
                },
                {
                    "name": "chinese",
                    "value": 25,
                    "textStyle": {
                        "color": "rgb(1,122,63)"
                    }
                },
                {
                    "name": "global",
                    "value": 19,
                    "textStyle": {
                        "color": "rgb(15,42,85)"
                    }
                },
                {
                    "name": "tariff",
                    "value": 15,
                    "textStyle": {
                        "color": "rgb(136,110,0)"
                    }
                },
                {
                    "name": "president",
                    "value": 14,
                    "textStyle": {
                        "color": "rgb(153,101,81)"
                    }
                },
                {
                    "name": "tiktok",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(84,130,50)"
                    }
                },
                {
                    "name": "beijing",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(20,26,18)"
                    }
                },
                {
                    "name": "washington",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(86,145,123)"
                    }
                },
                {
                    "name": "empire",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(115,127,95)"
                    }
                },
                {
                    "name": "american",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(20,65,20)"
                    }
                },
                {
                    "name": "trade",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(1,6,119)"
                    }
                },
                {
                    "name": "cent",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(136,122,116)"
                    }
                },
                {
                    "name": "power",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(134,29,154)"
                    }
                },
                {
                    "name": "war",
                    "value": 10,
                    "textStyle": {
                        "color": "rgb(117,95,121)"
                    }
                },
                {
                    "name": "donald",
                    "value": 9,
                    "textStyle": {
                        "color": "rgb(115,126,73)"
                    }
                },
                {
                    "name": "america",
                    "value": 9,
                    "textStyle": {
                        "color": "rgb(54,86,150)"
                    }
                },
                {
                    "name": "country",
                    "value": 8,
                    "textStyle": {
                        "color": "rgb(66,140,100)"
                    }
                },
                {
                    "name": "deal",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(99,100,114)"
                    }
                },
                {
                    "name": "life",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(156,96,9)"
                    }
                },
                {
                    "name": "month",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(2,21,115)"
                    }
                },
                {
                    "name": "house",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(61,158,23)"
                    }
                },
                {
                    "name": "economic",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(45,124,81)"
                    }
                },
                {
                    "name": "time",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(30,0,101)"
                    }
                },
                {
                    "name": "security",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(133,66,70)"
                    }
                },
                {
                    "name": "firm",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(112,129,65)"
                    }
                },
                {
                    "name": "call",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(36,137,14)"
                    }
                },
                {
                    "name": "economy",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(63,75,131)"
                    }
                },
                {
                    "name": "return",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(132,47,11)"
                    }
                },
                {
                    "name": "day",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(95,4,76)"
                    }
                },
                {
                    "name": "policy",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(59,53,45)"
                    }
                },
                {
                    "name": "administration",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(99,76,134)"
                    }
                },
                {
                    "name": "white",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(70,60,85)"
                    }
                },
                {
                    "name": "united",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(108,22,124)"
                    }
                },
                {
                    "name": "foreign",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(33,76,140)"
                    }
                },
                {
                    "name": "tech",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(40,61,127)"
                    }
                },
                {
                    "name": "company",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(90,74,6)"
                    }
                },
                {
                    "name": "military",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(139,55,48)"
                    }
                },
                {
                    "name": "diplomatic",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(65,140,94)"
                    }
                },
                {
                    "name": "century",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(110,55,94)"
                    }
                },
                {
                    "name": "bank",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(49,115,99)"
                    }
                },
                {
                    "name": "price",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(138,115,10)"
                    }
                },
                {
                    "name": "wang",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(133,112,69)"
                    }
                },
                {
                    "name": "told",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(95,62,40)"
                    }
                },
                {
                    "name": "investment",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(134,113,28)"
                    }
                },
                {
                    "name": "people",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(89,77,111)"
                    }
                },
                {
                    "name": "international",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(48,63,141)"
                    }
                },
                {
                    "name": "top",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(60,127,67)"
                    }
                },
                {
                    "name": "sale",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(25,84,45)"
                    }
                },
                {
                    "name": "range",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(4,150,126)"
                    }
                },
                {
                    "name": "canada",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(4,62,60)"
                    }
                },
                {
                    "name": "union",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(88,38,14)"
                    }
                },
                {
                    "name": "threat",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(58,46,79)"
                    }
                },
                {
                    "name": "talk",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(88,38,151)"
                    }
                },
                {
                    "name": "export",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(127,80,95)"
                    }
                },
                {
                    "name": "including",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(7,80,29)"
                    }
                },
                {
                    "name": "product",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(30,30,92)"
                    }
                },
                {
                    "name": "maga",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(76,139,112)"
                    }
                },
                {
                    "name": "retreat",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(149,67,40)"
                    }
                },
                {
                    "name": "xi",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(118,18,43)"
                    }
                },
                {
                    "name": "photo",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(157,17,48)"
                    }
                },
                {
                    "name": "border",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(36,2,33)"
                    }
                },
                {
                    "name": "dollar",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(112,105,111)"
                    }
                },
                {
                    "name": "expectancy",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(84,136,36)"
                    }
                },
                {
                    "name": "trap",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(145,86,90)"
                    }
                },
                {
                    "name": "mineral",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(130,127,55)"
                    }
                },
                {
                    "name": "metal",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(108,26,71)"
                    }
                },
                {
                    "name": "bytedance",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(89,43,75)"
                    }
                },
                {
                    "name": "sell",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(72,107,55)"
                    }
                },
                {
                    "name": "set",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(67,150,28)"
                    }
                },
                {
                    "name": "ground",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(130,23,42)"
                    }
                },
                {
                    "name": "reporter",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(149,128,63)"
                    }
                },
                {
                    "name": "late",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(113,140,147)"
                    }
                },
                {
                    "name": "remain",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(72,157,79)"
                    }
                },
                {
                    "name": "operation",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(67,3,117)"
                    }
                },
                {
                    "name": "held",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(86,157,136)"
                    }
                },
                {
                    "name": "wednesday",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(132,64,88)"
                    }
                },
                {
                    "name": "appeared",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(110,33,134)"
                    }
                },
                {
                    "name": "largest",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(3,159,82)"
                    }
                },
                {
                    "name": "import",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(137,13,146)"
                    }
                },
                {
                    "name": "week",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(64,44,48)"
                    }
                },
                {
                    "name": "counterpart",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(98,78,145)"
                    }
                },
                {
                    "name": "statement",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(139,42,11)"
                    }
                },
                {
                    "name": "common",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(99,24,19)"
                    }
                },
                {
                    "name": "european",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(121,14,160)"
                    }
                },
                {
                    "name": "control",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(87,57,133)"
                    }
                },
                {
                    "name": "analyst",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(126,64,7)"
                    }
                },
                {
                    "name": "tuesday",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(89,79,125)"
                    }
                },
                {
                    "name": "entity",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(40,25,159)"
                    }
                },
                {
                    "name": "list",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(139,154,64)"
                    }
                },
                {
                    "name": "trading",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(61,129,3)"
                    }
                },
                {
                    "name": "data",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(4,85,52)"
                    }
                },
                {
                    "name": "service",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(37,130,85)"
                    }
                },
                {
                    "name": "windmill",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(26,6,51)"
                    }
                },
                {
                    "name": "history",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(77,137,143)"
                    }
                },
                {
                    "name": "decline",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(7,54,4)"
                    }
                },
                {
                    "name": "major",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(81,110,3)"
                    }
                },
                {
                    "name": "challenge",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(31,37,45)"
                    }
                },
                {
                    "name": "financial",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(60,59,24)"
                    }
                },
                {
                    "name": "mid",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(81,117,55)"
                    }
                }
            ],
            "drawOutOfBound": false,
            "textStyle": {
                "emphasis": {}
            }
        }
    ],
    "legend": [
        {
            "data": [],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u8bcd\u9891\u5206\u6790 - \u8bcd\u4e91\u56fe (NLTK\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_97308a52659745c79be2c945bb21762d.setOption(option_97308a52659745c79be2c945bb21762d);
    </script>
                <div id="9500253a15bf44aeb168e2861d858755" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('9500253a15bf44aeb168e2861d858755').style.width = document.getElementById('9500253a15bf44aeb168e2861d858755').parentNode.clientWidth + 'px';
        var chart_9500253a15bf44aeb168e2861d858755 = echarts.init(
            document.getElementById('9500253a15bf44aeb168e2861d858755'), 'white', {renderer: 'canvas'});
        var option_9500253a15bf44aeb168e2861d858755 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u8bcd\u9891",
            "legendHoverLink": true,
            "data": [
                45,
                33,
                25,
                19,
                15,
                14,
                12,
                12,
                12,
                12,
                11,
                11,
                11,
                11,
                10,
                9,
                9,
                8,
                7,
                7
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u8bcd\u9891"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "china",
                "trump",
                "chinese",
                "global",
                "tariff",
                "president",
                "tiktok",
                "beijing",
                "washington",
                "empire",
                "american",
                "trade",
                "cent",
                "power",
                "war",
                "donald",
                "america",
                "country",
                "deal",
                "life"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u8bcd\u9891\u5206\u6790 - \u524d20\u9ad8\u9891\u8bcd (NLTK\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_9500253a15bf44aeb168e2861d858755.setOption(option_9500253a15bf44aeb168e2861d858755);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="70cc1f3fa02343fb95d117fd1021102c" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>左上下文</th>
            <th>关键词</th>
            <th>右上下文</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>imports and a demand that</td>
            <td>china</td>
            <td>sell the us arm of</td>
        </tr>
        <tr>
            <td>platform , a unit of</td>
            <td>china</td>
            <td>’ s bytedance . without</td>
        </tr>
        <tr>
            <td>tariffs ” in return for</td>
            <td>china</td>
            <td>agreeing to a tiktok sale</td>
        </tr>
        <tr>
            <td>they can agree on is</td>
            <td>china</td>
            <td>and the threat they see</td>
        </tr>
        <tr>
            <td>2.0 tech export controls against</td>
            <td>china</td>
            <td>. us president donald trump</td>
        </tr>
        <tr>
            <td>buying us-origin products to support</td>
            <td>china</td>
            <td>’ s development of quantum</td>
        </tr>
        <tr>
            <td>now confronted by a resurgent</td>
            <td>china</td>
            <td>– its most significant economic</td>
        </tr>
        <tr>
            <td>, as xi ’ s</td>
            <td>china</td>
            <td>emerges as a major global</td>
        </tr>
        <tr>
            <td>, as xi ’ s</td>
            <td>china</td>
            <td>emerges as a major global</td>
        </tr>
        <tr>
            <td>people ’ s republic of</td>
            <td>china</td>
            <td>and the subsequent material contributions</td>
        </tr>
        <tr>
            <td>reforms co-produced the return of</td>
            <td>china</td>
            <td>, after two centuries of</td>
        </tr>
        <tr>
            <td>us , technically speaking ,</td>
            <td>china</td>
            <td>is no longer merely a</td>
        </tr>
        <tr>
            <td>i explain at length in</td>
            <td>china</td>
            <td>’ s galaxy empire (</td>
        </tr>
        <tr>
            <td>2024 ) , is that</td>
            <td>china</td>
            <td>is rapidly becoming an empire</td>
        </tr>
        <tr>
            <td>orientalist ignorance and denial of</td>
            <td>china</td>
            <td>’ s imperial ambitions are</td>
        </tr>
        <tr>
            <td>the world are chinese .</td>
            <td>china</td>
            <td>has outflanked bodies such as</td>
        </tr>
        <tr>
            <td>asian infrastructure investment bank .</td>
            <td>china</td>
            <td>is spearheading the global rebellion</td>
        </tr>
        <tr>
            <td>topped the us dollar in</td>
            <td>china</td>
            <td>’ s cross-border transactions .</td>
        </tr>
        <tr>
            <td>trade surplus since 1975 –</td>
            <td>china</td>
            <td>is the largest trading country</td>
        </tr>
        <tr>
            <td>to “ decouple ” from</td>
            <td>china</td>
            <td>by applying tariff penalties ,</td>
        </tr>
        <tr>
            <td>and other chinese companies ,</td>
            <td>china</td>
            <td>’ s economy – unlike</td>
        </tr>
        <tr>
            <td>battery manufacturer durapower holdings .</td>
            <td>china</td>
            <td>, meanwhile , produces one-third</td>
        </tr>
        <tr>
            <td>korea and britain combined .</td>
            <td>china</td>
            <td>is the european union ’</td>
        </tr>
        <tr>
            <td>are actively drawing closer to</td>
            <td>china</td>
            <td>. in mid-2023 , the</td>
        </tr>
        <tr>
            <td>surpassed the us dollar in</td>
            <td>china</td>
            <td>’ s cross-border transactions for</td>
        </tr>
        <tr>
            <td>surpassed the us dollar in</td>
            <td>china</td>
            <td>’ s cross-border transactions for</td>
        </tr>
        <tr>
            <td>shifts are also happening in</td>
            <td>china</td>
            <td>in matters of everyday life</td>
        </tr>
        <tr>
            <td>expectancy is even higher among</td>
            <td>china</td>
            <td>’ s 400-million strong middle</td>
        </tr>
        <tr>
            <td>the past two decades .</td>
            <td>china</td>
            <td>now produces more stem graduates</td>
        </tr>
        <tr>
            <td>for war – often frames</td>
            <td>china-us</td>
            <td>tensions and conflict as inevitable</td>
        </tr>
        <tr>
            <td>dialogue with graham allison on</td>
            <td>china-us</td>
            <td>relations , held last month</td>
        </tr>
        <tr>
            <td>war – can america and</td>
            <td>china</td>
            <td>escape thucydides ’ s trap</td>
        </tr>
        <tr>
            <td>rewrite the pattern . that</td>
            <td>china</td>
            <td>’ s economy achieved 5</td>
        </tr>
        <tr>
            <td>per cent of americans view</td>
            <td>china</td>
            <td>unfavourably , trump praised chinese</td>
        </tr>
        <tr>
            <td>president donald trump could visit</td>
            <td>china</td>
            <td>as early as next month</td>
        </tr>
        <tr>
            <td>have been around trump visiting</td>
            <td>china</td>
            <td>, according to sources .</td>
        </tr>
        <tr>
            <td>through drastic changes ” .</td>
            <td>china</td>
            <td>hits back at trump with</td>
        </tr>
        <tr>
            <td>strategic minerals have soared in</td>
            <td>china</td>
            <td>over the past year ,</td>
        </tr>
        <tr>
            <td>with prices rising rapidly since</td>
            <td>china</td>
            <td>began restricting exports of the</td>
        </tr>
        <tr>
            <td>than 21.8 per cent in</td>
            <td>china</td>
            <td>, while prices in rotterdam</td>
        </tr>
        <tr>
            <td>and many countries , including</td>
            <td>china</td>
            <td>and the united states ,</td>
        </tr>
        <tr>
            <td>nan</td>
            <td>china</td>
            <td>’ s top diplomat has</td>
        </tr>
        <tr>
            <td>an irresponsible big power .</td>
            <td>china</td>
            <td>’ s top diplomat wang</td>
        </tr>
        <tr>
            <td>“ two sessions ” ,</td>
            <td>china</td>
            <td>’ s annual parliamentary gathering</td>
        </tr>
        <tr>
            <td>or worse ? ” “</td>
            <td>china</td>
            <td>will definitely take countermeasures in</td>
        </tr>
    </tbody>
</table>
        </div>

    </div>

    <script>
    </script>
    <script>
        (function() {
            containers = document.getElementsByClassName("chart-container");
            if(containers.length > 0) {
                containers[0].style.display = "block";
            }
        })()

        function showChart(evt, chartID) {
            let containers = document.getElementsByClassName("chart-container");
            for (let i = 0; i < containers.length; i++) {
                containers[i].style.display = "none";
            }

            let tablinks = document.getElementsByClassName("tablinks");
            for (let i = 0; i < tablinks.length; i++) {
                tablinks[i].className = "tablinks";
            }

            document.getElementById(chartID).style.display = "block";
            evt.currentTarget.className += " active";
        }
    </script>
</body>
</html>
