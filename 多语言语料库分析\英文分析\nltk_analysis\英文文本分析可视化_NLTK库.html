<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts-wordcloud.min.js"></script>

    
</head>
<body >
            <style>
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
        }

        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 12px 16px;
            transition: 0.3s;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #ccc;
        }

        .chart-container {
            display: block;
        }

        .chart-container:nth-child(n+2) {
            display: none;
        }
    </style>
    <div class="tab">
            <button class="tablinks" onclick="showChart(event, 'f158b101eb524080bca038cf42bc5d50')">首页</button>
            <button class="tablinks" onclick="showChart(event, '9158baaf6f6f48529169d91860deb4bd')">词频分析-词云图</button>
            <button class="tablinks" onclick="showChart(event, '1a2bc0f74d83453997eab86f33509701')">词频分析-柱状图</button>
            <button class="tablinks" onclick="showChart(event, 'e1a37df7aa20416a988eab2b774376a2')">KWIC分析</button>
            <button class="tablinks" onclick="showChart(event, '1ed2c3de0eda480eb98d0d66349a4d84')">Plot分析-频率图</button>
            <button class="tablinks" onclick="showChart(event, 'bd8e7ca652724335bdc4041f982b7e2f')">Plot分析-详细数据</button>
            <button class="tablinks" onclick="showChart(event, '853fea83359b4b2bb20d4f10421b083b')">词簇分析-频率图</button>
            <button class="tablinks" onclick="showChart(event, '30a18d4f96114e10b4d2776a11e13fe6')">词簇分析-详细数据</button>
            <button class="tablinks" onclick="showChart(event, '3d8b0eaefccf480091c1741c01d41de5')">搭配分析-频率图</button>
            <button class="tablinks" onclick="showChart(event, '04515a61c82243338a24fba22d521c89')">搭配分析-详细数据</button>
    </div>

    <div class="box">
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="f158b101eb524080bca038cf42bc5d50" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>分析类型</th>
            <th>说明</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>词频分析</td>
            <td>使用NLTK库进行英文分词和词形还原</td>
        </tr>
        <tr>
            <td>KWIC分析</td>
            <td>关键词上下文索引，基于NLTK分词结果</td>
        </tr>
        <tr>
            <td>Plot分析</td>
            <td>词语在文件中的分布情况分析</td>
        </tr>
        <tr>
            <td>词簇分析</td>
            <td>分析词语簇的分布和频率</td>
        </tr>
        <tr>
            <td>搭配分析</td>
            <td>分析词语的常见搭配，计算互信息和似然比</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="9158baaf6f6f48529169d91860deb4bd" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('9158baaf6f6f48529169d91860deb4bd').style.width = document.getElementById('9158baaf6f6f48529169d91860deb4bd').parentNode.clientWidth + 'px';
        var chart_9158baaf6f6f48529169d91860deb4bd = echarts.init(
            document.getElementById('9158baaf6f6f48529169d91860deb4bd'), 'white', {renderer: 'canvas'});
        var option_9158baaf6f6f48529169d91860deb4bd = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "wordCloud",
            "shape": "circle",
            "rotationRange": [
                -90,
                90
            ],
            "rotationStep": 45,
            "girdSize": 20,
            "sizeRange": [
                15,
                80
            ],
            "data": [
                {
                    "name": "china",
                    "value": 45,
                    "textStyle": {
                        "color": "rgb(3,65,158)"
                    }
                },
                {
                    "name": "trump",
                    "value": 33,
                    "textStyle": {
                        "color": "rgb(35,71,137)"
                    }
                },
                {
                    "name": "chinese",
                    "value": 25,
                    "textStyle": {
                        "color": "rgb(148,34,109)"
                    }
                },
                {
                    "name": "global",
                    "value": 19,
                    "textStyle": {
                        "color": "rgb(88,97,156)"
                    }
                },
                {
                    "name": "tariff",
                    "value": 15,
                    "textStyle": {
                        "color": "rgb(138,69,30)"
                    }
                },
                {
                    "name": "president",
                    "value": 14,
                    "textStyle": {
                        "color": "rgb(105,119,24)"
                    }
                },
                {
                    "name": "tiktok",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(125,30,133)"
                    }
                },
                {
                    "name": "beijing",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(64,28,3)"
                    }
                },
                {
                    "name": "washington",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(110,95,83)"
                    }
                },
                {
                    "name": "empire",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(148,131,63)"
                    }
                },
                {
                    "name": "american",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(39,132,68)"
                    }
                },
                {
                    "name": "trade",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(88,16,58)"
                    }
                },
                {
                    "name": "cent",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(135,127,118)"
                    }
                },
                {
                    "name": "power",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(97,79,47)"
                    }
                },
                {
                    "name": "war",
                    "value": 10,
                    "textStyle": {
                        "color": "rgb(118,74,95)"
                    }
                },
                {
                    "name": "donald",
                    "value": 9,
                    "textStyle": {
                        "color": "rgb(70,4,119)"
                    }
                },
                {
                    "name": "america",
                    "value": 9,
                    "textStyle": {
                        "color": "rgb(82,8,46)"
                    }
                },
                {
                    "name": "country",
                    "value": 8,
                    "textStyle": {
                        "color": "rgb(24,89,159)"
                    }
                },
                {
                    "name": "deal",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(113,18,117)"
                    }
                },
                {
                    "name": "life",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(153,52,38)"
                    }
                },
                {
                    "name": "month",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(130,52,89)"
                    }
                },
                {
                    "name": "house",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(33,45,116)"
                    }
                },
                {
                    "name": "economic",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(17,24,102)"
                    }
                },
                {
                    "name": "time",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(146,39,155)"
                    }
                },
                {
                    "name": "security",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(44,126,107)"
                    }
                },
                {
                    "name": "firm",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(149,152,60)"
                    }
                },
                {
                    "name": "call",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(149,33,88)"
                    }
                },
                {
                    "name": "economy",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(20,14,52)"
                    }
                },
                {
                    "name": "return",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(77,156,120)"
                    }
                },
                {
                    "name": "day",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(128,22,61)"
                    }
                },
                {
                    "name": "policy",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(56,11,141)"
                    }
                },
                {
                    "name": "administration",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(15,29,155)"
                    }
                },
                {
                    "name": "white",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(132,1,160)"
                    }
                },
                {
                    "name": "united",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(112,155,52)"
                    }
                },
                {
                    "name": "foreign",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(106,42,94)"
                    }
                },
                {
                    "name": "tech",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(134,111,150)"
                    }
                },
                {
                    "name": "company",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(48,3,88)"
                    }
                },
                {
                    "name": "military",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(160,115,59)"
                    }
                },
                {
                    "name": "diplomatic",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(157,98,6)"
                    }
                },
                {
                    "name": "century",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(25,13,118)"
                    }
                },
                {
                    "name": "bank",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(35,10,52)"
                    }
                },
                {
                    "name": "price",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(111,78,154)"
                    }
                },
                {
                    "name": "wang",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(102,123,65)"
                    }
                },
                {
                    "name": "told",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(18,34,91)"
                    }
                },
                {
                    "name": "investment",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(140,124,50)"
                    }
                },
                {
                    "name": "people",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(156,125,28)"
                    }
                },
                {
                    "name": "international",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(145,147,115)"
                    }
                },
                {
                    "name": "top",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(136,109,2)"
                    }
                },
                {
                    "name": "sale",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(50,145,45)"
                    }
                },
                {
                    "name": "range",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(32,36,135)"
                    }
                },
                {
                    "name": "canada",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(109,120,17)"
                    }
                },
                {
                    "name": "union",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(106,94,152)"
                    }
                },
                {
                    "name": "threat",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(70,116,39)"
                    }
                },
                {
                    "name": "talk",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(128,97,54)"
                    }
                },
                {
                    "name": "export",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(139,149,125)"
                    }
                },
                {
                    "name": "including",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(110,84,53)"
                    }
                },
                {
                    "name": "product",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(12,148,100)"
                    }
                },
                {
                    "name": "maga",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(27,101,89)"
                    }
                },
                {
                    "name": "retreat",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(141,17,129)"
                    }
                },
                {
                    "name": "xi",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(79,57,121)"
                    }
                },
                {
                    "name": "photo",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(117,0,29)"
                    }
                },
                {
                    "name": "border",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(86,152,78)"
                    }
                },
                {
                    "name": "dollar",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(34,124,140)"
                    }
                },
                {
                    "name": "expectancy",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(21,77,113)"
                    }
                },
                {
                    "name": "trap",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(63,81,5)"
                    }
                },
                {
                    "name": "mineral",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(63,45,27)"
                    }
                },
                {
                    "name": "metal",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(69,20,118)"
                    }
                },
                {
                    "name": "bytedance",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(107,112,40)"
                    }
                },
                {
                    "name": "sell",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(54,51,19)"
                    }
                },
                {
                    "name": "set",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(85,24,28)"
                    }
                },
                {
                    "name": "ground",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(76,101,152)"
                    }
                },
                {
                    "name": "reporter",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(27,69,104)"
                    }
                },
                {
                    "name": "late",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(143,118,51)"
                    }
                },
                {
                    "name": "remain",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(8,18,51)"
                    }
                },
                {
                    "name": "operation",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(121,28,156)"
                    }
                },
                {
                    "name": "held",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(155,44,13)"
                    }
                },
                {
                    "name": "wednesday",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(141,32,153)"
                    }
                },
                {
                    "name": "appeared",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(49,125,81)"
                    }
                },
                {
                    "name": "largest",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(32,138,12)"
                    }
                },
                {
                    "name": "import",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(81,39,132)"
                    }
                },
                {
                    "name": "week",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(150,30,97)"
                    }
                },
                {
                    "name": "counterpart",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(6,115,159)"
                    }
                },
                {
                    "name": "statement",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(14,32,116)"
                    }
                },
                {
                    "name": "common",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(94,129,33)"
                    }
                },
                {
                    "name": "european",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(61,59,117)"
                    }
                },
                {
                    "name": "control",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(108,53,75)"
                    }
                },
                {
                    "name": "analyst",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(72,2,89)"
                    }
                },
                {
                    "name": "tuesday",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(69,113,122)"
                    }
                },
                {
                    "name": "entity",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(74,93,0)"
                    }
                },
                {
                    "name": "list",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(37,129,42)"
                    }
                },
                {
                    "name": "trading",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(60,70,145)"
                    }
                },
                {
                    "name": "data",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(31,58,83)"
                    }
                },
                {
                    "name": "service",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(23,146,148)"
                    }
                },
                {
                    "name": "windmill",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(102,156,109)"
                    }
                },
                {
                    "name": "history",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(117,7,112)"
                    }
                },
                {
                    "name": "decline",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(32,81,154)"
                    }
                },
                {
                    "name": "major",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(27,12,112)"
                    }
                },
                {
                    "name": "challenge",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(30,53,80)"
                    }
                },
                {
                    "name": "financial",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(68,127,93)"
                    }
                },
                {
                    "name": "mid",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(95,96,62)"
                    }
                }
            ],
            "drawOutOfBound": false,
            "textStyle": {
                "emphasis": {}
            }
        }
    ],
    "legend": [
        {
            "data": [],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u8bcd\u9891\u5206\u6790 - \u8bcd\u4e91\u56fe (NLTK\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_9158baaf6f6f48529169d91860deb4bd.setOption(option_9158baaf6f6f48529169d91860deb4bd);
    </script>
                <div id="1a2bc0f74d83453997eab86f33509701" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('1a2bc0f74d83453997eab86f33509701').style.width = document.getElementById('1a2bc0f74d83453997eab86f33509701').parentNode.clientWidth + 'px';
        var chart_1a2bc0f74d83453997eab86f33509701 = echarts.init(
            document.getElementById('1a2bc0f74d83453997eab86f33509701'), 'white', {renderer: 'canvas'});
        var option_1a2bc0f74d83453997eab86f33509701 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u8bcd\u9891",
            "legendHoverLink": true,
            "data": [
                45,
                33,
                25,
                19,
                15,
                14,
                12,
                12,
                12,
                12,
                11,
                11,
                11,
                11,
                10,
                9,
                9,
                8,
                7,
                7
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u8bcd\u9891"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "china",
                "trump",
                "chinese",
                "global",
                "tariff",
                "president",
                "tiktok",
                "beijing",
                "washington",
                "empire",
                "american",
                "trade",
                "cent",
                "power",
                "war",
                "donald",
                "america",
                "country",
                "deal",
                "life"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u8bcd\u9891\u5206\u6790 - \u524d20\u9ad8\u9891\u8bcd (NLTK\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_1a2bc0f74d83453997eab86f33509701.setOption(option_1a2bc0f74d83453997eab86f33509701);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="e1a37df7aa20416a988eab2b774376a2" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>左上下文</th>
            <th>关键词</th>
            <th>右上下文</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>imports and a demand that</td>
            <td>china</td>
            <td>sell the us arm of</td>
        </tr>
        <tr>
            <td>platform , a unit of</td>
            <td>china</td>
            <td>’ s bytedance . without</td>
        </tr>
        <tr>
            <td>tariffs ” in return for</td>
            <td>china</td>
            <td>agreeing to a tiktok sale</td>
        </tr>
        <tr>
            <td>they can agree on is</td>
            <td>china</td>
            <td>and the threat they see</td>
        </tr>
        <tr>
            <td>2.0 tech export controls against</td>
            <td>china</td>
            <td>. us president donald trump</td>
        </tr>
        <tr>
            <td>buying us-origin products to support</td>
            <td>china</td>
            <td>’ s development of quantum</td>
        </tr>
        <tr>
            <td>now confronted by a resurgent</td>
            <td>china</td>
            <td>– its most significant economic</td>
        </tr>
        <tr>
            <td>, as xi ’ s</td>
            <td>china</td>
            <td>emerges as a major global</td>
        </tr>
        <tr>
            <td>, as xi ’ s</td>
            <td>china</td>
            <td>emerges as a major global</td>
        </tr>
        <tr>
            <td>people ’ s republic of</td>
            <td>china</td>
            <td>and the subsequent material contributions</td>
        </tr>
        <tr>
            <td>reforms co-produced the return of</td>
            <td>china</td>
            <td>, after two centuries of</td>
        </tr>
        <tr>
            <td>us , technically speaking ,</td>
            <td>china</td>
            <td>is no longer merely a</td>
        </tr>
        <tr>
            <td>i explain at length in</td>
            <td>china</td>
            <td>’ s galaxy empire (</td>
        </tr>
        <tr>
            <td>2024 ) , is that</td>
            <td>china</td>
            <td>is rapidly becoming an empire</td>
        </tr>
        <tr>
            <td>orientalist ignorance and denial of</td>
            <td>china</td>
            <td>’ s imperial ambitions are</td>
        </tr>
        <tr>
            <td>the world are chinese .</td>
            <td>china</td>
            <td>has outflanked bodies such as</td>
        </tr>
        <tr>
            <td>asian infrastructure investment bank .</td>
            <td>china</td>
            <td>is spearheading the global rebellion</td>
        </tr>
        <tr>
            <td>topped the us dollar in</td>
            <td>china</td>
            <td>’ s cross-border transactions .</td>
        </tr>
        <tr>
            <td>trade surplus since 1975 –</td>
            <td>china</td>
            <td>is the largest trading country</td>
        </tr>
        <tr>
            <td>to “ decouple ” from</td>
            <td>china</td>
            <td>by applying tariff penalties ,</td>
        </tr>
        <tr>
            <td>and other chinese companies ,</td>
            <td>china</td>
            <td>’ s economy – unlike</td>
        </tr>
        <tr>
            <td>battery manufacturer durapower holdings .</td>
            <td>china</td>
            <td>, meanwhile , produces one-third</td>
        </tr>
        <tr>
            <td>korea and britain combined .</td>
            <td>china</td>
            <td>is the european union ’</td>
        </tr>
        <tr>
            <td>are actively drawing closer to</td>
            <td>china</td>
            <td>. in mid-2023 , the</td>
        </tr>
        <tr>
            <td>surpassed the us dollar in</td>
            <td>china</td>
            <td>’ s cross-border transactions for</td>
        </tr>
        <tr>
            <td>surpassed the us dollar in</td>
            <td>china</td>
            <td>’ s cross-border transactions for</td>
        </tr>
        <tr>
            <td>shifts are also happening in</td>
            <td>china</td>
            <td>in matters of everyday life</td>
        </tr>
        <tr>
            <td>expectancy is even higher among</td>
            <td>china</td>
            <td>’ s 400-million strong middle</td>
        </tr>
        <tr>
            <td>the past two decades .</td>
            <td>china</td>
            <td>now produces more stem graduates</td>
        </tr>
        <tr>
            <td>for war – often frames</td>
            <td>china-us</td>
            <td>tensions and conflict as inevitable</td>
        </tr>
        <tr>
            <td>dialogue with graham allison on</td>
            <td>china-us</td>
            <td>relations , held last month</td>
        </tr>
        <tr>
            <td>war – can america and</td>
            <td>china</td>
            <td>escape thucydides ’ s trap</td>
        </tr>
        <tr>
            <td>rewrite the pattern . that</td>
            <td>china</td>
            <td>’ s economy achieved 5</td>
        </tr>
        <tr>
            <td>per cent of americans view</td>
            <td>china</td>
            <td>unfavourably , trump praised chinese</td>
        </tr>
        <tr>
            <td>president donald trump could visit</td>
            <td>china</td>
            <td>as early as next month</td>
        </tr>
        <tr>
            <td>have been around trump visiting</td>
            <td>china</td>
            <td>, according to sources .</td>
        </tr>
        <tr>
            <td>through drastic changes ” .</td>
            <td>china</td>
            <td>hits back at trump with</td>
        </tr>
        <tr>
            <td>strategic minerals have soared in</td>
            <td>china</td>
            <td>over the past year ,</td>
        </tr>
        <tr>
            <td>with prices rising rapidly since</td>
            <td>china</td>
            <td>began restricting exports of the</td>
        </tr>
        <tr>
            <td>than 21.8 per cent in</td>
            <td>china</td>
            <td>, while prices in rotterdam</td>
        </tr>
        <tr>
            <td>and many countries , including</td>
            <td>china</td>
            <td>and the united states ,</td>
        </tr>
        <tr>
            <td>nan</td>
            <td>china</td>
            <td>’ s top diplomat has</td>
        </tr>
        <tr>
            <td>an irresponsible big power .</td>
            <td>china</td>
            <td>’ s top diplomat wang</td>
        </tr>
        <tr>
            <td>“ two sessions ” ,</td>
            <td>china</td>
            <td>’ s annual parliamentary gathering</td>
        </tr>
        <tr>
            <td>or worse ? ” “</td>
            <td>china</td>
            <td>will definitely take countermeasures in</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="1ed2c3de0eda480eb98d0d66349a4d84" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('1ed2c3de0eda480eb98d0d66349a4d84').style.width = document.getElementById('1ed2c3de0eda480eb98d0d66349a4d84').parentNode.clientWidth + 'px';
        var chart_1ed2c3de0eda480eb98d0d66349a4d84 = echarts.init(
            document.getElementById('1ed2c3de0eda480eb98d0d66349a4d84'), 'white', {renderer: 'canvas'});
        var option_1ed2c3de0eda480eb98d0d66349a4d84 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u8bcd\u9891",
            "legendHoverLink": true,
            "data": [
                3,
                1,
                2,
                23,
                5,
                3,
                4,
                4
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u8bcd\u9891"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "10.txt",
                "2.txt",
                "3.txt",
                "4.txt",
                "5.txt",
                "6.txt",
                "7.txt",
                "8.txt"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u8bcd\u8bed\u5206\u5e03\u5206\u6790 - \u6587\u4ef6\u9891\u7387\u5206\u5e03 (NLTK\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_1ed2c3de0eda480eb98d0d66349a4d84.setOption(option_1ed2c3de0eda480eb98d0d66349a4d84);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="bd8e7ca652724335bdc4041f982b7e2f" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>序号</th>
            <th>文件ID</th>
            <th>文件名</th>
            <th>总词数</th>
            <th>频率</th>
            <th>离散度</th>
            <th>分布图</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>1</td>
            <td>2</td>
            <td>10.txt</td>
            <td>114</td>
            <td>3</td>
            <td>0.7407</td>
            <td>-----------------|---|----------|-----------------</td>
        </tr>
        <tr>
            <td>2</td>
            <td>3</td>
            <td>2.txt</td>
            <td>102</td>
            <td>1</td>
            <td>0.0000</td>
            <td>-------------------------------------------|------</td>
        </tr>
        <tr>
            <td>3</td>
            <td>4</td>
            <td>3.txt</td>
            <td>87</td>
            <td>2</td>
            <td>0.5556</td>
            <td>-------|-----------------------|------------------</td>
        </tr>
        <tr>
            <td>4</td>
            <td>5</td>
            <td>4.txt</td>
            <td>598</td>
            <td>23</td>
            <td>0.9620</td>
            <td>--|----------|-|---|||||--|-||||||--||--|||--|--|-</td>
        </tr>
        <tr>
            <td>5</td>
            <td>6</td>
            <td>5.txt</td>
            <td>166</td>
            <td>5</td>
            <td>0.8889</td>
            <td>---|--------|--------------|--|-------------|-----</td>
        </tr>
        <tr>
            <td>6</td>
            <td>7</td>
            <td>6.txt</td>
            <td>72</td>
            <td>3</td>
            <td>0.7407</td>
            <td>--|----------------|------------|-----------------</td>
        </tr>
        <tr>
            <td>7</td>
            <td>8</td>
            <td>7.txt</td>
            <td>99</td>
            <td>4</td>
            <td>0.8333</td>
            <td>--|--------------------------|-----|-----------|--</td>
        </tr>
        <tr>
            <td>8</td>
            <td>9</td>
            <td>8.txt</td>
            <td>122</td>
            <td>4</td>
            <td>0.8333</td>
            <td>|-------------------|--------|-----|--------------</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="853fea83359b4b2bb20d4f10421b083b" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('853fea83359b4b2bb20d4f10421b083b').style.width = document.getElementById('853fea83359b4b2bb20d4f10421b083b').parentNode.clientWidth + 'px';
        var chart_853fea83359b4b2bb20d4f10421b083b = echarts.init(
            document.getElementById('853fea83359b4b2bb20d4f10421b083b'), 'white', {renderer: 'canvas'});
        var option_853fea83359b4b2bb20d4f10421b083b = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u9891\u7387",
            "legendHoverLink": true,
            "data": [
                3,
                2,
                2
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u9891\u7387"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "dollar china",
                "return china",
                "xi china"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u8bcd\u7c07\u5206\u6790 - \u9891\u7387\u5206\u5e03 (NLTK\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_853fea83359b4b2bb20d4f10421b083b.setOption(option_853fea83359b4b2bb20d4f10421b083b);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="30a18d4f96114e10b4d2776a11e13fe6" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>词簇</th>
            <th>排名</th>
            <th>频率</th>
            <th>范围</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>dollar china</td>
            <td>1</td>
            <td>3</td>
            <td>1</td>
        </tr>
        <tr>
            <td>return china</td>
            <td>2</td>
            <td>2</td>
            <td>2</td>
        </tr>
        <tr>
            <td>xi china</td>
            <td>3</td>
            <td>2</td>
            <td>1</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="3d8b0eaefccf480091c1741c01d41de5" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('3d8b0eaefccf480091c1741c01d41de5').style.width = document.getElementById('3d8b0eaefccf480091c1741c01d41de5').parentNode.clientWidth + 'px';
        var chart_3d8b0eaefccf480091c1741c01d41de5 = echarts.init(
            document.getElementById('3d8b0eaefccf480091c1741c01d41de5'), 'white', {renderer: 'canvas'});
        var option_3d8b0eaefccf480091c1741c01d41de5 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u603b\u9891\u7387",
            "legendHoverLink": true,
            "data": [
                8,
                7,
                6,
                4,
                4,
                4,
                4,
                4,
                4,
                4,
                4,
                4,
                4,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u603b\u9891\u7387"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "trump",
                "empire",
                "global",
                "chinese",
                "tariff",
                "power",
                "photo",
                "border",
                "mid",
                "renminbi",
                "dollar",
                "cent",
                "price",
                "president",
                "retreat",
                "diplomatic",
                "maga",
                "country",
                "rapidly",
                "time",
                "cross",
                "transaction",
                "surplus",
                "economy",
                "surpassed",
                "life",
                "war",
                "american",
                "popular",
                "social",
                "sale",
                "tiktok",
                "return",
                "threat",
                "international",
                "export",
                "control",
                "donald",
                "product",
                "restoration",
                "xi",
                "emerges",
                "major",
                "player",
                "people",
                "contribution",
                "sweeping",
                "reform",
                "prominence",
                "explain",
                "length",
                "galaxy",
                "bank",
                "investment",
                "trade",
                "trading",
                "half",
                "union",
                "produce",
                "japan",
                "germany",
                "south",
                "india",
                "image",
                "expectancy",
                "destined",
                "thucydides",
                "trap",
                "allison",
                "month",
                "america",
                "predict",
                "doom",
                "source",
                "discussion",
                "washington",
                "strategic",
                "mineral",
                "metal",
                "top",
                "diplomat",
                "pressure"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u642d\u914d\u5206\u6790 - \u9891\u7387\u5206\u5e03 (NLTK\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_3d8b0eaefccf480091c1741c01d41de5.setOption(option_3d8b0eaefccf480091c1741c01d41de5);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="04515a61c82243338a24fba22d521c89" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>搭配词</th>
            <th>排名</th>
            <th>左频率</th>
            <th>右频率</th>
            <th>总频率</th>
            <th>范围</th>
            <th>互信息</th>
            <th>似然比</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>trump</td>
            <td>1</td>
            <td>4</td>
            <td>4</td>
            <td>8</td>
            <td>4</td>
            <td>3.0269</td>
            <td>33.5699</td>
        </tr>
        <tr>
            <td>empire</td>
            <td>2</td>
            <td>2</td>
            <td>5</td>
            <td>7</td>
            <td>1</td>
            <td>4.2937</td>
            <td>41.6667</td>
        </tr>
        <tr>
            <td>global</td>
            <td>3</td>
            <td>1</td>
            <td>5</td>
            <td>6</td>
            <td>1</td>
            <td>3.4084</td>
            <td>28.3501</td>
        </tr>
        <tr>
            <td>chinese</td>
            <td>4</td>
            <td>3</td>
            <td>1</td>
            <td>4</td>
            <td>3</td>
            <td>2.4275</td>
            <td>13.4608</td>
        </tr>
        <tr>
            <td>tariff</td>
            <td>5</td>
            <td>1</td>
            <td>3</td>
            <td>4</td>
            <td>3</td>
            <td>3.1645</td>
            <td>17.5475</td>
        </tr>
        <tr>
            <td>power</td>
            <td>6</td>
            <td>3</td>
            <td>1</td>
            <td>4</td>
            <td>2</td>
            <td>3.6119</td>
            <td>20.0287</td>
        </tr>
        <tr>
            <td>photo</td>
            <td>7</td>
            <td>0</td>
            <td>4</td>
            <td>4</td>
            <td>1</td>
            <td>5.0713</td>
            <td>28.1215</td>
        </tr>
        <tr>
            <td>border</td>
            <td>8</td>
            <td>1</td>
            <td>3</td>
            <td>4</td>
            <td>1</td>
            <td>5.0713</td>
            <td>28.1215</td>
        </tr>
        <tr>
            <td>mid</td>
            <td>9</td>
            <td>3</td>
            <td>1</td>
            <td>4</td>
            <td>1</td>
            <td>5.4864</td>
            <td>30.4230</td>
        </tr>
        <tr>
            <td>renminbi</td>
            <td>10</td>
            <td>3</td>
            <td>1</td>
            <td>4</td>
            <td>1</td>
            <td>5.4864</td>
            <td>30.4230</td>
        </tr>
        <tr>
            <td>dollar</td>
            <td>11</td>
            <td>3</td>
            <td>1</td>
            <td>4</td>
            <td>1</td>
            <td>5.0713</td>
            <td>28.1215</td>
        </tr>
        <tr>
            <td>cent</td>
            <td>12</td>
            <td>2</td>
            <td>2</td>
            <td>4</td>
            <td>2</td>
            <td>3.6119</td>
            <td>20.0287</td>
        </tr>
        <tr>
            <td>price</td>
            <td>13</td>
            <td>3</td>
            <td>1</td>
            <td>4</td>
            <td>1</td>
            <td>4.7494</td>
            <td>26.3363</td>
        </tr>
        <tr>
            <td>president</td>
            <td>14</td>
            <td>1</td>
            <td>2</td>
            <td>3</td>
            <td>3</td>
            <td>2.8490</td>
            <td>11.8485</td>
        </tr>
        <tr>
            <td>retreat</td>
            <td>15</td>
            <td>3</td>
            <td>0</td>
            <td>3</td>
            <td>1</td>
            <td>4.6563</td>
            <td>19.3650</td>
        </tr>
        <tr>
            <td>diplomatic</td>
            <td>16</td>
            <td>1</td>
            <td>2</td>
            <td>3</td>
            <td>2</td>
            <td>4.3344</td>
            <td>18.0262</td>
        </tr>
        <tr>
            <td>maga</td>
            <td>17</td>
            <td>3</td>
            <td>0</td>
            <td>3</td>
            <td>1</td>
            <td>4.6563</td>
            <td>19.3650</td>
        </tr>
        <tr>
            <td>country</td>
            <td>18</td>
            <td>1</td>
            <td>2</td>
            <td>3</td>
            <td>2</td>
            <td>3.6563</td>
            <td>15.2061</td>
        </tr>
        <tr>
            <td>rapidly</td>
            <td>19</td>
            <td>1</td>
            <td>2</td>
            <td>3</td>
            <td>2</td>
            <td>5.6563</td>
            <td>23.5239</td>
        </tr>
        <tr>
            <td>time</td>
            <td>20</td>
            <td>1</td>
            <td>2</td>
            <td>3</td>
            <td>1</td>
            <td>4.0713</td>
            <td>16.9322</td>
        </tr>
        <tr>
            <td>cross</td>
            <td>21</td>
            <td>0</td>
            <td>3</td>
            <td>3</td>
            <td>1</td>
            <td>5.0713</td>
            <td>21.0911</td>
        </tr>
        <tr>
            <td>transaction</td>
            <td>22</td>
            <td>0</td>
            <td>3</td>
            <td>3</td>
            <td>1</td>
            <td>5.0713</td>
            <td>21.0911</td>
        </tr>
        <tr>
            <td>surplus</td>
            <td>23</td>
            <td>2</td>
            <td>1</td>
            <td>3</td>
            <td>1</td>
            <td>5.6563</td>
            <td>23.5239</td>
        </tr>
        <tr>
            <td>economy</td>
            <td>24</td>
            <td>0</td>
            <td>3</td>
            <td>3</td>
            <td>2</td>
            <td>4.3344</td>
            <td>18.0262</td>
        </tr>
        <tr>
            <td>surpassed</td>
            <td>25</td>
            <td>2</td>
            <td>1</td>
            <td>3</td>
            <td>1</td>
            <td>5.6563</td>
            <td>23.5239</td>
        </tr>
        <tr>
            <td>life</td>
            <td>26</td>
            <td>2</td>
            <td>1</td>
            <td>3</td>
            <td>2</td>
            <td>3.8490</td>
            <td>16.0073</td>
        </tr>
        <tr>
            <td>war</td>
            <td>27</td>
            <td>3</td>
            <td>0</td>
            <td>3</td>
            <td>2</td>
            <td>3.3344</td>
            <td>13.8673</td>
        </tr>
        <tr>
            <td>american</td>
            <td>28</td>
            <td>1</td>
            <td>2</td>
            <td>3</td>
            <td>3</td>
            <td>3.1969</td>
            <td>13.2954</td>
        </tr>
        <tr>
            <td>popular</td>
            <td>29</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>social</td>
            <td>30</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>sale</td>
            <td>31</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>tiktok</td>
            <td>32</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>2.4864</td>
            <td>6.8937</td>
        </tr>
        <tr>
            <td>return</td>
            <td>33</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>3.7494</td>
            <td>10.3956</td>
        </tr>
        <tr>
            <td>threat</td>
            <td>34</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>2</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>international</td>
            <td>35</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>2</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>export</td>
            <td>36</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>2</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>control</td>
            <td>37</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>4.4864</td>
            <td>12.4389</td>
        </tr>
        <tr>
            <td>donald</td>
            <td>38</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>2</td>
            <td>2.9014</td>
            <td>8.0444</td>
        </tr>
        <tr>
            <td>product</td>
            <td>39</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>2</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>restoration</td>
            <td>40</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>xi</td>
            <td>41</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>emerges</td>
            <td>42</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>major</td>
            <td>43</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.4864</td>
            <td>12.4389</td>
        </tr>
        <tr>
            <td>player</td>
            <td>44</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>people</td>
            <td>45</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>contribution</td>
            <td>46</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>sweeping</td>
            <td>47</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>reform</td>
            <td>48</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>prominence</td>
            <td>49</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>explain</td>
            <td>50</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>length</td>
            <td>51</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>galaxy</td>
            <td>52</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>bank</td>
            <td>53</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>3.7494</td>
            <td>10.3956</td>
        </tr>
        <tr>
            <td>investment</td>
            <td>54</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>trade</td>
            <td>55</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>2</td>
            <td>2.6119</td>
            <td>7.2418</td>
        </tr>
        <tr>
            <td>trading</td>
            <td>56</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.4864</td>
            <td>12.4389</td>
        </tr>
        <tr>
            <td>half</td>
            <td>57</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>union</td>
            <td>58</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>produce</td>
            <td>59</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>japan</td>
            <td>60</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>germany</td>
            <td>61</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>4.4864</td>
            <td>12.4389</td>
        </tr>
        <tr>
            <td>south</td>
            <td>62</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>india</td>
            <td>63</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>image</td>
            <td>64</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>expectancy</td>
            <td>65</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>destined</td>
            <td>66</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>thucydides</td>
            <td>67</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>4.4864</td>
            <td>12.4389</td>
        </tr>
        <tr>
            <td>trap</td>
            <td>68</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>allison</td>
            <td>69</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>4.4864</td>
            <td>12.4389</td>
        </tr>
        <tr>
            <td>month</td>
            <td>70</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>2</td>
            <td>3.4864</td>
            <td>9.6663</td>
        </tr>
        <tr>
            <td>america</td>
            <td>71</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>2.9014</td>
            <td>8.0444</td>
        </tr>
        <tr>
            <td>predict</td>
            <td>72</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>doom</td>
            <td>73</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0713</td>
            <td>16.8333</td>
        </tr>
        <tr>
            <td>source</td>
            <td>74</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.4864</td>
            <td>12.4389</td>
        </tr>
        <tr>
            <td>discussion</td>
            <td>75</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>washington</td>
            <td>76</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>2.4864</td>
            <td>6.8937</td>
        </tr>
        <tr>
            <td>strategic</td>
            <td>77</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
        <tr>
            <td>mineral</td>
            <td>78</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>metal</td>
            <td>79</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>top</td>
            <td>80</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.0713</td>
            <td>11.2882</td>
        </tr>
        <tr>
            <td>diplomat</td>
            <td>81</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.4864</td>
            <td>12.4389</td>
        </tr>
        <tr>
            <td>pressure</td>
            <td>82</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>5.0713</td>
            <td>14.0607</td>
        </tr>
    </tbody>
</table>
        </div>

    </div>

    <script>
    </script>
    <script>
        (function() {
            containers = document.getElementsByClassName("chart-container");
            if(containers.length > 0) {
                containers[0].style.display = "block";
            }
        })()

        function showChart(evt, chartID) {
            let containers = document.getElementsByClassName("chart-container");
            for (let i = 0; i < containers.length; i++) {
                containers[i].style.display = "none";
            }

            let tablinks = document.getElementsByClassName("tablinks");
            for (let i = 0; i < tablinks.length; i++) {
                tablinks[i].className = "tablinks";
            }

            document.getElementById(chartID).style.display = "block";
            evt.currentTarget.className += " active";
        }
    </script>
</body>
</html>
