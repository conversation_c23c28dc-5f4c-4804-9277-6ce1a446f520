# coding=utf-8
"""
中文文本分析 - 可视化 (使用spaCy库)
目标：尽可能接近AntConc的分析结果
"""
import os
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import WordCloud, Bar, Line, Tab
from pyecharts.globals import ThemeType
from pyecharts.components import Table


class ChineseSpacyVisualizer:
    """中文文本可视化类 - spaCy库版本"""

    def __init__(self):
        """初始化可视化器"""
        self.tab = Tab()
        self.theme = ThemeType.LIGHT

    def add_title(self, title, subtitle=""):
        """添加标题配置"""
        return opts.TitleOpts(
            title=title,
            subtitle=subtitle,
            pos_left="center"
        )

    def visualize_word_frequency(self, csv_file):
        """可视化词频分析结果"""
        if not os.path.exists(csv_file):
            print(f"文件不存在: {csv_file}")
            return None

        df = pd.read_csv(csv_file)
        words_freq = list(zip(df['Word'].tolist(), df['Frequency'].tolist()))

        # 1. 词云图
        wordcloud = (
            WordCloud()
            .add(
                "",
                words_freq[:100],  # 展示前100个词
                word_size_range=[15, 80],
                textstyle_opts=opts.TextStyleOpts(font_family="Microsoft YaHei")
            )
            .set_global_opts(
                title_opts=self.add_title("中文词频分析 - 词云图 (spaCy库)"),
                tooltip_opts=opts.TooltipOpts(is_show=True)
            )
        )

        # 2. 高频词柱状图
        top_20 = df.head(20)
        bar = (
            Bar()
            .add_xaxis(top_20['Word'].tolist())
            .add_yaxis(
                "词频",
                top_20['Frequency'].tolist(),
                label_opts=opts.LabelOpts(position="top")
            )
            .set_global_opts(
                title_opts=self.add_title("中文词频分析 - 前20高频词 (spaCy库)"),
                xaxis_opts=opts.AxisOpts(
                    axislabel_opts=opts.LabelOpts(rotate=45),
                    interval=0
                ),
                datazoom_opts=[opts.DataZoomOpts()],
                tooltip_opts=opts.TooltipOpts(is_show=True)
            )
        )

        return wordcloud, bar

    def visualize_kwic(self, csv_file):
        """可视化KWIC分析结果"""
        if not os.path.exists(csv_file):
            print(f"文件不存在: {csv_file}")
            return None

        df = pd.read_csv(csv_file)

        # 去重处理
        df['context'] = df['left'] + '|' + df['right']
        df = df.drop_duplicates(subset=['context'])
        df = df.drop('context', axis=1)

        # 创建表格数据
        table_data = []
        for _, row in df.iterrows():
            table_data.append([row['left'], row['keyword'], row['right']])

        headers = ["左上下文", "关键词", "右上下文"]
        table = Table()
        table.add(headers, table_data[:50])  # 显示前50条结果
        table.set_global_opts(
            title_opts=self.add_title(f"中文KWIC分析结果 - spaCy库（去重后共{len(df)}条）")
        )

        return table

    def visualize_plot(self, csv_file):
        """可视化Plot分析结果"""
        if not os.path.exists(csv_file):
            print(f"文件不存在: {csv_file}")
            return None

        df = pd.read_csv(csv_file)

        # 1. 创建频率分布图
        bar = (
            Bar()
            .add_xaxis(df['Filename'].tolist())
            .add_yaxis(
                "词频",
                df['Frequency'].tolist(),
                label_opts=opts.LabelOpts(position="top")
            )
            .set_global_opts(
                title_opts=self.add_title("中文词语分布分析 - 文件频率分布 (spaCy库)"),
                xaxis_opts=opts.AxisOpts(
                    axislabel_opts=opts.LabelOpts(rotate=45),
                    interval=0
                ),
                datazoom_opts=[opts.DataZoomOpts()],
                tooltip_opts=opts.TooltipOpts(is_show=True)
            )
        )

        # 2. 创建离散度分布图
        line = (
            Line()
            .add_xaxis(df['Filename'].tolist())
            .add_yaxis(
                "离散度",
                df['Dispersion'].tolist(),
                is_smooth=True,
                label_opts=opts.LabelOpts(is_show=False)
            )
            .set_global_opts(
                title_opts=self.add_title("中文词语分布分析 - 离散度分布 (spaCy库)"),
                xaxis_opts=opts.AxisOpts(
                    axislabel_opts=opts.LabelOpts(rotate=45),
                    interval=0
                ),
                yaxis_opts=opts.AxisOpts(min_=0, max_=1),
                datazoom_opts=[opts.DataZoomOpts()],
                tooltip_opts=opts.TooltipOpts(is_show=True)
            )
        )

        # 3. 创建数据表格
        table_data = []
        for _, row in df.iterrows():
            table_data.append([
                str(row['Sequence']),
                str(row['FileID']),
                row['Filename'],
                str(row['TotalTokens']),
                str(row['Frequency']),
                f"{row['Dispersion']:.4f}",
                row['Plot']
            ])

        headers = ["序号", "文件ID", "文件名", "总词数", "频率", "离散度", "分布图"]
        table = Table()
        table.add(headers, table_data)
        table.set_global_opts(
            title_opts=self.add_title("中文词语分布分析 - 详细数据 (spaCy库)")
        )

        return bar, line, table

    def visualize_clusters(self, csv_file):
        """可视化词簇分析结果"""
        if not os.path.exists(csv_file):
            print(f"文件不存在: {csv_file}")
            return None

        df = pd.read_csv(csv_file)

        # 1. 创建词簇频率分布图
        bar = (
            Bar()
            .add_xaxis(df['Cluster'].tolist())
            .add_yaxis(
                "频率",
                df['Frequency'].tolist(),
                label_opts=opts.LabelOpts(position="top")
            )
            .set_global_opts(
                title_opts=self.add_title("中文词簇分析 - 频率分布 (spaCy库)"),
                xaxis_opts=opts.AxisOpts(
                    axislabel_opts=opts.LabelOpts(rotate=45),
                    interval=0
                ),
                datazoom_opts=[opts.DataZoomOpts()],
                tooltip_opts=opts.TooltipOpts(is_show=True)
            )
        )

        # 2. 创建数据表格
        table_data = []
        for _, row in df.iterrows():
            table_data.append([
                row['Cluster'],
                str(row['Rank']),
                str(row['Frequency']),
                str(row['Range'])
            ])

        headers = ["词簇", "排名", "频率", "范围"]
        table = Table()
        table.add(headers, table_data)
        table.set_global_opts(
            title_opts=self.add_title("中文词簇分析 - 详细数据 (spaCy库)")
        )

        return bar, table

    def visualize_collocate(self, csv_file):
        """可视化搭配分析结果"""
        if not os.path.exists(csv_file):
            print(f"文件不存在: {csv_file}")
            return None

        df = pd.read_csv(csv_file)

        # 1. 创建搭配词频率分布图
        bar = (
            Bar()
            .add_xaxis(df['collocate'].tolist())
            .add_yaxis(
                "总频率",
                df['total_freq'].tolist(),
                label_opts=opts.LabelOpts(position="top")
            )
            .set_global_opts(
                title_opts=self.add_title("中文搭配分析 - 频率分布 (spaCy库)"),
                xaxis_opts=opts.AxisOpts(
                    axislabel_opts=opts.LabelOpts(rotate=45),
                    interval=0
                ),
                datazoom_opts=[opts.DataZoomOpts()],
                tooltip_opts=opts.TooltipOpts(is_show=True)
            )
        )

        # 2. 创建互信息分布图
        line = (
            Line()
            .add_xaxis(df['collocate'].tolist())
            .add_yaxis(
                "互信息",
                df['mi_score'].tolist(),
                is_smooth=True,
                label_opts=opts.LabelOpts(is_show=False)
            )
            .set_global_opts(
                title_opts=self.add_title("中文搭配分析 - 互信息分布 (spaCy库)"),
                xaxis_opts=opts.AxisOpts(
                    axislabel_opts=opts.LabelOpts(rotate=45),
                    interval=0
                ),
                datazoom_opts=[opts.DataZoomOpts()],
                tooltip_opts=opts.TooltipOpts(is_show=True)
            )
        )

        # 3. 创建数据表格
        table_data = []
        for _, row in df.iterrows():
            table_data.append([
                row['collocate'],
                str(row['rank']),
                str(row['freqL']),
                str(row['freqR']),
                str(row['total_freq']),
                str(row['range']),
                f"{row['mi_score']:.4f}",
                f"{row['likelihood']:.4f}"
            ])

        headers = ["搭配词", "排名", "左频率", "右频率", "总频率", "范围", "互信息", "似然比"]
        table = Table()
        table.add(headers, table_data)
        table.set_global_opts(
            title_opts=self.add_title("中文搭配分析 - 详细数据 (spaCy库)")
        )

        return bar, line, table

    def create_homepage(self):
        """创建主页"""
        table_data = [
            ["词频分析", "使用spaCy库进行中文分词和词性标注"],
            ["KWIC分析", "关键词上下文索引，基于spaCy神经网络分词"],
            ["词语分布分析", "分析词语在不同文件中的分布情况"],
            ["词簇分析", "分析词语簇的分布和频率"],
            ["搭配分析", "分析词语的常见搭配，计算互信息和似然比"],
            ["技术特点", "基于神经网络的现代NLP库，支持词性标注"]
        ]

        headers = ["分析类型", "说明 (spaCy库实现)"]
        table = Table()
        table.add(headers, table_data)
        table.set_global_opts(
            title_opts=self.add_title("中文文本分析可视化导航 - spaCy库版本", 
                                    "基于spaCy库的中文文本分析结果（使用zh_core_web_sm模型）")
        )

        return table

    def generate_visualization(self):
        """生成完整的可视化报告"""
        print("=== 中文文本分析可视化 (spaCy库) ===")
        
        # 首页
        homepage_table = self.create_homepage()
        self.tab.add(homepage_table, "首页")

        # 查找CSV文件并添加可视化
        csv_files = {
            'word_freq': 'spacy_word_frequency_report.csv',
            'kwic': None,
            'plot': None,
            'cluster': None,
            'collocate': None
        }

        # 动态查找CSV文件
        for filename in os.listdir('.'):
            if filename.startswith('spacy_KWIC_') and filename.endswith('.csv'):
                csv_files['kwic'] = filename
            elif filename.startswith('spacy_plot_') and filename.endswith('.csv'):
                csv_files['plot'] = filename
            elif filename.startswith('spacy_cluster_') and filename.endswith('.csv'):
                csv_files['cluster'] = filename
            elif filename.startswith('spacy_collocate_') and filename.endswith('.csv'):
                csv_files['collocate'] = filename

        # 添加各种分析的可视化
        if csv_files['word_freq'] and os.path.exists(csv_files['word_freq']):
            word_freq_result = self.visualize_word_frequency(csv_files['word_freq'])
            if word_freq_result:
                wordcloud, bar = word_freq_result
                self.tab.add(wordcloud, "词频分析-词云图")
                self.tab.add(bar, "词频分析-柱状图")

        if csv_files['kwic']:
            kwic_table = self.visualize_kwic(csv_files['kwic'])
            if kwic_table:
                self.tab.add(kwic_table, "KWIC分析")

        if csv_files['plot']:
            plot_result = self.visualize_plot(csv_files['plot'])
            if plot_result:
                plot_bar, plot_line, plot_table = plot_result
                self.tab.add(plot_bar, "词语分布-频率图")
                self.tab.add(plot_line, "词语分布-离散度图")
                self.tab.add(plot_table, "词语分布-详细数据")

        if csv_files['cluster']:
            cluster_result = self.visualize_clusters(csv_files['cluster'])
            if cluster_result:
                cluster_bar, cluster_table = cluster_result
                self.tab.add(cluster_bar, "词簇分析-频率图")
                self.tab.add(cluster_table, "词簇分析-详细数据")

        if csv_files['collocate']:
            collocate_result = self.visualize_collocate(csv_files['collocate'])
            if collocate_result:
                collocate_bar, collocate_line, collocate_table = collocate_result
                self.tab.add(collocate_bar, "搭配分析-频率图")
                self.tab.add(collocate_line, "搭配分析-互信息图")
                self.tab.add(collocate_table, "搭配分析-详细数据")

        # 渲染HTML文件
        output_file = "中文文本分析可视化_spaCy库.html"
        self.tab.render(output_file)
        print(f"可视化报告已生成: {output_file}")

def main():
    """主函数"""
    visualizer = ChineseSpacyVisualizer()
    visualizer.generate_visualization()

if __name__ == "__main__":
    main()
