目录

1 引言

2 智能切片需求和应用场景

3 智能切片标准研究

4 基于数据分析的智能切片架构及流程

5 智能切片的关键挑战

6 智能切片商用模式探讨

7 总结与展望

8 缩略语

9 主要贡献单位

P1

P2

P5

P9

P15

P20

P22

P23

P24

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

1 引言

5G作为数字化社会的关键基础设施，不仅服务于个人用户，

还需要满足各行各业数字化转型的需求。对于5G业务ITU提出三

大应用场景：增强型移动宽带、超可靠低时延通信和大规模机器

类通信，3GPP协议已经定义通过3种类型的网络切片支持，从而

避免每种业务都新建独立网络造成的建网成本巨大和制约业务发

展的问题，同时网络切片之间的隔离也保证了网络的安全性。网

络切片的引入给网络带来了极大的灵活性，主要体现在切片可按

需定制、实时部署、动态保障。为了实现这些功能，需要引入专

门的管理网元来实现切片实例的全生命周期管理，因此，又给网

络带来管理和运维的复杂性，使得运营商面对的是一个高度复杂

的移动通信网络。如果网络切片的智能化程度不够，使得网络切

片无法根据用户的特殊需求进行切片定制，运营商通过网络切片

进行业务创新就会受限。

本白皮书在分析智能切片需求的基础上，结合主要标准组织

关于网络切片智能化的研究现状，提出了智能切片的整体架构和

业务流程，重点关注数据分析在智能切片中的作用，并进一步探

讨智能切片在实施过程中的关键挑战和智能切片的商用模式。本

白皮书拟服务于运营商后续基于AI构筑5G网络切片灵活调整的能

力，以适应5G网络发展、匹配垂直行业需求，实现拓扑灵活可配

置、资源专属可保障的智能网络。

1

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

2 智能切片需求和应用场景

（1）租户需求

租户和运营商协商网络切片的订购信息，运营商根

据该订购信息提供满足租户要求的端到端切片。对于有明

确运营时间要求的业务，运营商提供的网络切片需同时满

足租户对切片建立、修改、删除的时间要求。运营商可以

通过一定的交互方式（比如交互界面），提供自动化、一

站式的切片订购渠道。切片租户可以设置并更新切片订购

信息，包含切片类型、接入用户信息、切片容量、业务信

息、QoS信息等；切片租户可以查询并监控所订购切片的

运行情况和切片预测信息，如接入用户数量、用户分布区

域信息、QoS保障情况、异常情况预测等；切片租户也可

以根据切片运行情况、自身业务数据的反馈，以及智能分

析系统反馈的信息来确定是否修改切片的订购信息，如切

片容量更新、业务信息更新、QoS更新等。

运营商可以为切片租户提供差异化SLA服务，同时也

可以结合垂直行业应用，为切片租户部署切片。终端用户

只要访问此类应用，就自动享受绑定的切片服务。为提升

切片的用户体验，运营商可以通过人工智能技术对切片租

户提供智能化客服和切片订购引导，帮助其选择并生成最

优最合适的定制化网络切片服务。

（2）运营商服务需求

运营商网络通过接收租户需求，根据需求选择合适的

切片，并且分解为对子切片的需求，然后再把分解指标下

发到接入网、传输网和核心网各域。E2E切片的实现不仅

2

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

需要端到端的管理，还需要从物理层到资源层到切片层到

应用层跨各层次的关联管理，给网络管理和运维带来很大

挑战。

在5G全云化网络架构基础上，通过引入已经成熟的

以机器学习为核心的AI技术，可以从两个方面实现切片网

络的智能化。一方面实现切片的智能化部署，利用AI训

练平台，对网络数据进行分析，输出切片管理策略规则或

者切片优化部署模板；另一方面实现切片的智能化调度管

理，根据AI训练平台输出的切片管理策略，自动化执行策

略，实现切片的故障自愈和自优化。AI的引入能够帮助运

营商实现切片灵活性和切片管理复杂度之间的最佳平衡。

（3）典型应用场景

在业务层面，智能化网络切片主要针对如下的业务场

景需求：

a) 所需网络资源和其他应用的资源隔离；

b) 具有一定典型性及应用规模；

c) 部署实例需要按需动态调整配置。

典型的应用场景包括：

•赛事/大型活动业务

此类业务场景包括赛事活动、大规模群体活动、节日

庆典现场等。该类业务场景的特点是在活动举办期间，活

动现场人群密集、通信需求瞬时爆发、网络资源需要按需

调整。而且，除了活动的直播等通信需求外，针对特定的

赛事、活动，根据活动内容和规模的差异，通信网络还可

能同时肩负活动期间的现场组织、协调以及公共安全等不

同类型的临时通信任务的保障工作。

3

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

•工业巡检

工业生产环境中，传统的人工巡检方式存在巡检效率

低、巡检数据统计不完善等问题，通过将AR技术和无人

机/无线机器人技术结合，可以大幅的提升生产环境巡检

过程的效率和自动化水平。工业巡检具备周期性、路线固

定的特点，运营商可以为特定行业的工业巡检需求生成专

用的网络切片，并通过持续的智能化分析和训练形成包含

巡检时段、巡检路径等特性在内的的巡检模型，从而提供

有针对性的高质量业务保障。

4

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

3 智能切片标准研究

3.1 网络切片标准化

5G网络切片的标准化工作主要由3GPP制定。此外，

NGMN、ITU、ETSI、IETF和GSMA等标准组织也对网络切

片的需求和商业模式等方向展开了相关的研究。

3GPP SA1提出了网络切片的需求，通过需求分析，指出

网络切片可以让运营商根据用户需求提供按需定制的逻辑网

络，为5G多场景下的需求提供解决方案。3GPP SA2研究端

到端网络切片系统设计，定义了网络切片的相关概念和切片

控制流程，包括网络切片标识、网络切片接入与选择、切片

会话隔离、切片移动性管理、支持漫游等；针对ITU提出的三

个5G典型应用场景，定义了不同的标准化切片/业务类型，如

图1所示。

图1 5G网络切片

5

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

另外，网络切片的管理和调度方法由3GPP SA5工作组负

责，定义了网络切片管理的信息模型和服务，包括生命周期和配

置管理服务、故障检测服务和性能保障服务，但并未定义明确的

功能实体，图2所示的各管理功能仅为示例。

图2 网络切片管理示意图

3.2 智能切片标准进展

目前3GPP、ETSI、ITU-T、CCSA等国际国内主要标准化

组织均有将网络切片和智能化相关联的研究工作，本章节重点介

绍各标准组织网络切片智能化研究的进展。

3.2.1 3GPP标准研究进展

3GPP SA2制定的全新5G核心网架构中引入了新的网络功能

NWDAF，用于收集、分析网络数据，以及向其他的网络功能提

供数据分析结果信息。NWDAF提供的数据分析结果主要包含切

片负载、业务体验、网络性能等信息。

针对智能切片，NWDAF分析得到切片中某个业务的用户业

务体验（包括业务级平均业务体验、切片中针对某个业务的用户

级业务体验），并将该信息反馈给切片管理系统，以便切片管理

6

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

系统调整各域的切片资源配置。智能切片在3GPP R17版本会进

一步研究。

除此之外， SA5和RAN3也同步做了相应的工作。

•SA5：切片管理系统性能保障

SA5已经定义了切片信息模型，包含切片租户的切片SLA要

求，以及用于监测和评估切片SLA的KPI。后续3GPP SA5会研

究切片管理系统如何利用数据分析功能（如MDAF），将SLA转

换成相应的KPI及配置需求，并参考各数据分析功能相关输入，

对网络切片性能进行监控。

•RAN3：基于切片管理系统的切片资源配置进行切片资源

隔离

目前，RAN可以基于SA5定义的接入网侧资源配置参数进行无

线资源隔离，但是RAN侧如何隔离属于实现范围，标准未定义。

RAN侧提供切片空口资源的保障方式，可以有以下两种：

• 完全占有，即该切片的空口资源不可以被其他切片共享；

• 动态调配，类似于QoS参数中的GFBR，业务需要时一定

会满足，不需要时，可以供其他切片共享使用。 

3.2.2 ETSI标准研究进展

欧洲电信标准化协会ETSI对智能切片展开研究，成立了ENI

和ZSM工作组，分别研究切片智能化和切片自动化。

ENI（Experiential Networked Intelligence）工作组于2017

年2月成立，致力于利用AI等技术实现智能业务部署、智能策略

控制、智能资源管理、智能监控与智能分析预测等功能，解决网

络SDN/NFV化、网络切片化后引入的复杂度问题，提高运营商

网络部署和运营的体验。在该工作组发布的用例规范中，包括了

弹性资源管理和编排，保障业务需求及智能网络切片管理等智能

切片相关的用例；其发布的网络切片生命周期管理POC项目，通

过AI预测承载网络流量，给出网络资源的智能调整策略，实现网

7

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

络切片的动态创建、监控和修改。

ZSM(Zero touch network & Service Management)工作

组于2018年1月成立。ZSM将定义一个新的面向未来的端到端架

构以满足未来网络和服务需要的灵活、高效、定性管理和自动化

需求，目标是所有操作过程和任务(例如发布、配置、保障、优

化)100%自动化。目前已经发布的文档包括场景、需求和参考架

构。关于网络切片的研究正在进行，预计2019年Q3完成。另外，

ZSM将开始5G网络切片管理研究。

3.2.3 ITU-T 标准研究进展

ITU-T的第13研究组（Study Group，SG13）主要研究

未来网络、云计算和可信网络架构，并于2017年11月成立了未

来网络机器学习（Machine Learning for Future Network，

ML5G）焦点组，主要研究未来网络的机器学习技术，当前已输

出了统一的逻辑架构。该架构与3GPP SA2定义的NWDAF的数

据收集、分析、反馈模型类似。

另外，关于AI辅助的网络切片需求分析立项在2019年3月份

会议上已经通过，该立项以对网络切片的运维管理优化和满足用

户业务体验需求为目的，讨论基于智能化的网络切片的需求及网

络架构。该组后续会重点关注智能化研究，尤其是智能化切片的

研究。

3.2.4 CCSA标准研究进展

中国通信标准化协会CCSA关于切片的研究包括TC3的承载

IP网络切片技术研究、TC6的传送网网络切片技术研究、TC7的

通信网切片管理技术研究和TC5的核心网切片场景及关键技术研

究、核心网切片的安全技术研究。除此之外，TC5的5G核心网智

能切片的应用研究课题正在研究阶段，重点关注核心网切片的智

能化。

8

IMT-2020 (5G) IMT-2020 (5G)推进组 推进组

基于AI的智能切片管理和协同 基于AI的智能切片管理和协同白皮书 白皮书

4 基于数据分析的智能切片架构及流程

4.1 智能切片总体架构

智能切片在5G网络切片系统中引入AI分析系统，该系统以

租户需求数据、网络切片运行数据等作为数据源，通过智能分析

算法计算得出能够匹配租户业务需求的网络能力，进而动态调整

网络切片的服务能力。

智能切片总体架构如图3所示，包括：租户（运营商自身业

务或第三方业务）、切片管理系统（包括网管和其他管理系统）

以及包含智能分析系统的5G网络。智能分析系统作为桥接运营商

网络与切片租户的媒介，借助于成熟的人工智能或者机器学习技

术，可以实现网络切片的体验评估、网络切片的资源配置优化等

能力。

图3 智能切片总体架构

9

IMT-2020 (5G) IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

切片在创建阶段、运行阶段和更新阶段均需要切片管理系

统和智能分析系统交互，以便确定资源配置信息或获取切片运行

动态情况以确定是否进行资源调整。切片状态变迁和切片管理系

统、智能分析系统间的交互示意如图4：

图4 切片状态变迁及与周边系统交互示意图

• 切片创建阶段：切片租户根据自身的业务需求与运营商进

行切片的SLA协商，并且向切片管理系统进行切片的订购。切片

管理系统和智能分析系统交互确定切片的资源配置信息，以便完

成5G网络切片的创建。

• 切片运行阶段：借助智能分析系统以及海量网络切片数

据，切片管理系统可以评估切片SLA要求或者切片策略信息的满

足情况，针对网络切片初始配置不合理的情形进行更新。

切片租户可以查询和监控切片的运行状态，比如：切片的用

户接入数量、切片用户的区域分布情况以及切片的QoS保障情况

等。切片租户可以接收到切片运行的预测信息，比如：在未来某

个时间段内切片运行异常情况的预测信息等。

• 切片更新阶段：切片租户根据自身业务数据的反馈以及

查询到的切片运行状况，向切片管理系统申请修改切片的订购信

息；或智能分析系统向切片管理系统反馈切片运行状态分析结

果，以便切片管理系统完成切片资源更新。

10

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

4.2 智能切片业务流程

4.2.1 切片创建流程

对于需要新创建一个切片的场景，切片管理系统根据租户

对切片的SLA要求（如切片列表、PLMN列表、最大用户数、切

片服务区域、切片端到端时延、切片中终端的移动等级、切片资

源共享等级）进行各域（接入网、传输网、核心网等）的SLA分

解，进而进行各域资源配置，包括带宽、时延等。相对于核心网

和传输网，RAN侧存在资源共享，面临更大的挑战。针对RAN

侧新建切片的资源配置示例如下：

• 保障空口资源：15%，即如果新建切片需要15%的空口资

源，RAN一定会保障，在RAN侧资源大于等于15%时切片才会

创建成功。

• 最大空口资源：70%，即如果空口资源有剩余，新建切片

最大可以占用70%的空口资源。

另外，针对已存切片，考虑租户已和运营商签署SLA，所以

新建切片应避免影响已存切片。

切片创建流程如图5所示：

图5 切片创建流程

11

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

4.2.2 切片运行流程

切片创建成功后，当前网络可以实时监控切片运行状态的

KPI，比如资源的使用率、负载、切片用户数等，然而缺乏实时

监控切片中的业务体验，无法有效评估整个切片在运行态的切片

SLA。

借助3GPP引入的NWDAF网元，基于收集的网络数据和业务

体验数据进行业务模型训练，从而可以实时监控切片中的业务体

办公室 验，如图6所示。

图6 基于NWDAF的切片模型训练

针对切片的某个业务，NWDAF可以从切片管理系统以及

核心网网元收集网络数据。此外，经过与切片租户的协商，

NWDAF也可以从切片租户的应用收集属于该切片的业务体验数

据（该数据必须是经过切片租户特定处理的，不涉及用户隐私和

业务特性的非敏感数据），其中：

1）从切片管理系统获取的网络数据为网管数据，可以包括终

端级别的路测数据、网元级别的性能数据、网络级别的KPI数据。

12

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

2）从核心网网元收集的网络数据为非网管数据，比如QoS 

flow级数据（比如流速率、时延）、终端级数据（比如终端位

置）等。

基于这些数据，NWDAF采用机器学习算法训练业务体验模

型，用于表征业务体验与来自切片管理系统或者核心网网元的网

络数据的关系。以线性回归（Linear Regression，LR）机器学

习算法为例，业务模型可以表征为：

h(x)=w0x0+w1x1+w2x2+w3x3+w4x4+w5x5…+wDxD

其中，

• h(x)表示业务体验，即Service MOS。

• xi

(i=0,1,2,…,D)表示网络数据，包括网管数据和非网管

数据，D为网络数据的维度。

• wi

(i=0,1,2,…,D)为每个网络数据影响业务体验的权重，

D为权重的维度。

注1：3GPP SA2已经定义了NWDAF和切片管理系统交互的

相关功能，而3GPP SA5 对二者之间的接口及数据传送相关功能

还有待研究讨论。

注2：目前业务体验模型主要用于测量、预测业务体验，至

于如何识别业务体验差是哪个域（接入网、传输网、核心网）引

起的，在3GPP R16版本并没有讨论，后续会继续进行相关研究

和分析。

4.2.3 切片更新流程

切片运行过程中，考虑到无线信道的变化、切片话务模型的

变化、切片用户数的变化，切片的初始资源配置可能无法在切片

的整个生命周期中始终满足切片SLA要求，网络需要具备智能调

整切片资源的能力。

13

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

如图7，切片管理系统可以根据从NWDAF获取到的切片业

务体验以及切片运行态的KPI等信息评估切片租户的SLA满足情

况，以便切片管理系统相应地更新切片资源配置（比如针对核心

网资源可以通过NFV扩缩容的方式）：

- 如果当前网络资源超额满足切片SLA要求，则切片管理系

统可以降低切片的资源配置；

- 如果当前网络资源不能满足切片SLA要求，则切片管理系

统可以增加切片的资源配置。

例如：新建切片的保障空口资源15%和最大空口资源70%，

经过切片管理系统分析后无法满足切片SLA要求，切片管理系统

增加无线侧资源配置，保障空口资源从15%提高到20%，最大空

口资源从70%提高到80%，切片管理系统将新的资源配置下发到

无线侧。

图7 切片更新流程

14

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

5 智能切片的关键挑战

5.1 RAN侧的挑战

从切片管理的角度看，资源隔离是网络切片的重要特性，能

够使一个切片的故障、拥塞，不会影响另一个网络切片的工作。

虽然无线资源可以通过资源独占的方式实现完全的隔离，然而考

虑到无线资源受频率、用户业务分布与密度，以及资源利用效

率、网络管理复杂度等因素的影响，需结合切片的业务体验，以

动态调度的方式让不同的切片共享无线资源以实现资源利用的最

优化，如图8所示，切片管理系统下发最大空口资源和保障空口

资源（根据签约及付费计算而来）给RAN侧，RAN侧在保障空

口资源及最大空口资源范围内，可以根据资源实际使用情况动态

进行调整。例如：根据当前资源实际使用情况以及历史同期数据

的规律，并考虑一定的资源预留（比如20%）做为下一期的资源

分配策略。在切片整个运行过程中，切片管理系统可以根据用户

签约更新等情况调整下发到RAN侧的最大空口资源和保障空口资

源参数，RAN侧再进一步做出相应的调整。因此，如何高效地利

用空口资源是RAN侧实现切片的首要挑战，在R17版本会继续进

行研究。

图8 RAN切片资源动态调整

15

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

此外，当用户发生漫游或切换时，如何通过必要的切片管理

策略以及无线侧移动性管理手段，降低无线侧切片部署对用户业

务连续性的影响，也是未来部署和使用网络切片在RAN侧可能遇

到的技术挑战。

5.2 AI平台和分析系统

随着计算机处理能力的不断提高以及云存储领域的最新发

展，AI技术在网络智能运维、网络智能运营、云视频监控、视

频智能广告等多方面的应用印证了在电信领域引入AI系统的必

要性。

网络切片作为5G的一项关键技术，通过逻辑专网服务垂直

行业，它可灵活的为切片用户提供按需定制、实时部署、动态保

障、安全隔离等能力。跟传统网络相比，网络切片不仅带来了灵

活性，同时也带来了管理和运维方面的复杂性，比如需要引入专

门的管理网元来实现切片实例化全生命周期管理，还需要硬件、

资源、切片部署、应用的多维度关联管理。所以基于人工智能来

增强切片自动化的能力是不可避免的发展趋势。

当前标准中已定义一些网络数据分析功能，如核心网的

NWDAF和网管的MDAF，均可理解为AI分析系统的一个智能分

析模块，为网络切片的优化部署及用户体验保障进行协同处理。

16

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

但如何将AI分析有效地引入到智能切片仍需要进一步分析，比

如图9中，AI分析模型中的分类算法、聚类算法、回归算法、推

荐算法或其他算法哪种更适合于切片服务能力的分析。而且目前

针对智能化的网络应用仍停留在理论分析层面，缺乏实际应用验

证，只有充分的测试验证和经验积累，才能真正使人工智能更好

地应用在智能切片领域。

图9 AI平台和分析系统

17

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

5.3 切片智能部署

端到端网络切片部署是由切片管理系统将切片租户需求分解

为无线、传输、核心网各域的网络配置参数。由于网络配置参数

包含QoS相关参数（时延、速率、丢包率、抖动等）、容量相关

参数（用户数、激活用户数）、业务相关参数（覆盖区域、应用

场景、安全隔离）等众多内容，因此，如何合理分解配置参数将

直接影响切片能否满足切片租户的需求。端到端SLA参数分解如

图10所示：

图10 端到端SLA参数分解

在切片部署中引入智能化分析，基于大数据分析和人工智能

特征挖掘技术，根据切片模板信息和实际关联的云网资源信息以

及配置参数等上下文参数，结合无线、传输、核心网等切片实例

SLA测量数据进行分析，给出最合理的无线、传输、核心网子切

片模型以及部署资源需求、配置参数推荐，能最大化匹配租户需

求和提升网络资源使用效率。

18

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

5.4 智能切片标准化增强

3GPP SA2 R16版本eNA项目分析了网络自动化不同的应用

场景和需求，定义了服务调用者如何调用NWDAF服务以获取切

片QoE等信息，以及NWDAF如何从5G网络功能、网管、应用获

取原始数据，单设备商已经能够实现智能切片的部署需求。然而

5G切片是端到端的网络，包括多个设备商的设备和相关的管理系

统，因此，异设备商之间的设备互通需要在标准层面进一步明确

定义。

网络中仍然有很多对于NWDAF进行模型训练有益的数据尚

未定义收集方法和接口，例如终端的数据、UPF的数据、传输的

数据以及MANO的数据等，标准化需要定义如何从这些来源收集

相关数据。

另外，3GPP SA2 R17版本立项的增强NWDAF功能会覆盖

更多的应用场景，例如智慧城市、无线网络节能等。针对这些新

场景，如何从RAN和网管获取数据，以及多NWDAF实例部署

时，不同NWDAF之间如何协同，也需要进一步研究。

图11为eNA项目在3GPP SA2 R16已完成的研究和R17待研

究的内容概括。

图11 3GPP SA2 eNA项目研究进展

19

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

6 智能切片商用模式探讨

在数据分析和网络智能的帮助下，运营商能够精确监控和调

整各个切片的服务质量，合理利用资源为更广泛的行业提供具有

定制SLA的5G服务，进而扩展服务市场。切片租户也可以从中受

益，在确保自身业务体验的同时，借助智能化的切片服务在新兴

市场中创造商业价值。

利用智能切片，运营商与切片租户可以通过三种模式实现双

赢：

• 智能切片商用模式1-标准化智能切片

运营商根据业务需求、用户状况（如用户数、用户分布）、

网络资源、趋势预测等因素统筹分析，提供一定数量的标准化切

片，满足不同的功能要求和性能要求，供租户选择租用。运营商

可以根据各因素的变化情况，对所提供的标准化切片的类型、容

量、数量等进行智能调整。标准化切片有利于运营商充分利用网

络资源，简化或省略个性化调试，降低管理和运维成本；有利于

租户自主选择切片，成熟的标准化切片可以降低租用价格。

• 智能切片商用模式2-定制化智能切片

运营商针对特定租户提供定制化切片。租户的需求千差万

别，难以通过标准化切片完全满足。运营商可以为租户创建定制

化专有切片，根据租户个性化需求和网络状况等，智能配置构成

通信服务的所有组件（如通信带宽、安全模型等）；并可自适应

动态调整，例如动态扩缩容，进一步适配满足租户SLA的需求。

定制化切片，有利于运营商适应各种用户需求，与租户制定合适

20

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

的SLA；有利于租户准确地获得所需的功能和性能，个性化需求

得到更好满足。

• 智能切片商用模式3-开放化智能切片

运营商可以向租户提供工具，例如向第三方提供API，使客

户端可以创建更多的功能。运营商无需为租户开发定制化切片，

可将网络能力在不同粒度模块化并开放，最大限度地避免网络资

源的浪费。同时租户可以按需自主开发，按需租用模块，将网络

性能调整到与他们支付的SLA相匹配的水平，并有利于集成原有

的私有网络和业务流程。

21

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

7 总结与展望

5G网络将支持大量来自垂直行业的多样化业务场景，例如

智能电网、智能工厂、高清视频、远程医疗、自动驾驶和增强现

实等，这些业务场景通常具有不同的通信需求，比如在移动性、

计费、安全、策略控制、时延和可靠性等方面要求各不相同。网

络切片通过对功能、性能、连接关系、运维等的灵活设计，可以

为不同的业务或用户群提供差异化的网络服务，有效解决差异化

SLA与建网成本之间的矛盾。

网络切片作为5G网络中的重要特征，在增加了网络灵活性的

同时也增加了网络管理和运维的复杂性，将已广泛应用于电信业

的AI技术引入网络切片，可以从切片租户订购、运营商切片服务

等方面提升网络切片智能化水平，实现智能化保障的闭环，使切

片的管理从被动防御走向主动优化，从静态配置走向动态调整，

最终提升运维效率、资源效率、客户体验。

目前端到端智能切片的研究仍处于起步阶段，尚有许多关键

技术有待解决，IMT-2020（5G）推进组网络技术工作组愿意与

5G产业相关组织、ICT企业、科研机构和高校等加强合作，共同

进行智能化切片的关键技术研究、标准推进和技术验证，促进网

络智能化的进一步发展。

22

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

8 缩略语

3GPP the 3rd Generation Partner Project 第三代合作伙伴计划

AI Artificial Intelligence 人工智能

AR Augmented Reality 现实增强

CSMF Communication Service Management Function 通信服务管理功能

E2E End to End 端到端

eNA enablers for Network Automation 使能网络自动化

GFBR Guaranteed Flow Bit Rate 保证流比特率

ICT Information and Communication Technology 信息和通信技术

KPI Key Performance Indicator 关键性能指标

MDAF Management Data Analytics Function 管理数据分析功能

NFV Network Function Virtualization 网络功能虚拟化

NSMF Network Slice Management Function 网络切片管理功能

NWDAF Network Data Analytics Function 网络数据分析功能

PLMN Public Land Mobile Network 公共陆地移动网络

QoE Quality of Experience 质量的体验

QoS Quality of Service 服务质量

RAN Radio Access Network 无线接入网

SDN Software Defined Network 软件定义网络

SLA Service-Level Agreement 服务等级协议

缩写 中文全称 英文全称

23

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

9 主要贡献单位

24

IMT-2020 (5G)推进组

基于AI的智能切片管理和协同白皮书

