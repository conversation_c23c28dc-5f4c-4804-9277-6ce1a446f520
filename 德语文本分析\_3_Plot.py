# coding=utf-8
import os
import sys
import spacy
import numpy as np
from collections import defaultdict
from concurrent.futures import ProcessPoolExecutor

sys.stdout.reconfigure(encoding='utf-8')

nlp = spacy.load("de_core_news_sm")
nlp.max_length = 5000000


class FileProfile:
    __slots__ = ['file_id', 'filename', 'total_tokens', 'token_positions', 'segments']

    def __init__(self, file_id, filename):
        self.file_id = file_id
        self.filename = filename
        self.total_tokens = 0
        self.token_positions = defaultdict(list)  # {lemma: [positions]}
        self.segments = defaultdict(list)  # {lemma: [segment_counts]}


def process_file(args):
    """并行处理单个文件"""
    file_id, filepath, num_segments = args
    filename = os.path.basename(filepath)
    profile = FileProfile(file_id, filename)

    with open(filepath, 'r', encoding='utf-8') as f:
        text = f.read()

    doc = nlp(text)
    tokens = [t for t in doc if t.is_alpha and not t.is_punct and not t.is_stop]
    profile.total_tokens = len(tokens)

    # 分段落统计
    segment_size = max(1, len(tokens) // num_segments)
    for i in range(num_segments):
        start = i * segment_size
        end = start + segment_size if i < num_segments - 1 else len(tokens)

        for pos, token in enumerate(tokens[start:end], start=start):
            lemma = token.lemma_.lower()
            profile.token_positions[lemma].append(pos)
            profile.segments[lemma].append(i)

    return profile


def calculate_dispersion(segments, num_segments):
    """计算离散度（变异系数）"""
    if not segments:
        return 0.0
    counts = np.bincount(segments, minlength=num_segments)
    std = np.std(counts)
    mean = np.mean(counts)
    return std / mean if mean != 0 else 0.0


def generate_plot(total_tokens, positions, plot_width=50):
    """生成文本分布图"""
    if total_tokens == 0 or not positions:
        return ''

    plot = [' '] * plot_width
    for pos in positions:
        idx = int((pos / total_tokens) * plot_width)
        idx = min(max(idx, 0), plot_width - 1)
        plot[idx] = '│'

    return ''.join(plot)


def plot_analysis(folder_path, target_word, num_segments=10, plot_width=50):
    """执行完整分析流程"""
    files = sorted([f for f in os.listdir(folder_path) if f.endswith('.txt')],
                   key=lambda x: x.lower())

    # 并行处理文件
    with ProcessPoolExecutor() as executor:
        args = [(i, os.path.join(folder_path, f), num_segments)
                for i, f in enumerate(files)]
        profiles = list(executor.map(process_file, args))

    # 生成结果
    results = []
    target = target_word.lower()
    for profile in profiles:
        positions = profile.token_positions.get(target, [])
        segments = profile.segments.get(target, [])

        results.append({
            'Sequence': profile.file_id + 1,
            'FileID': profile.file_id,
            'Filename': profile.filename,
            'TotalTokens': profile.total_tokens,
            'Frequency': len(positions),
            'Dispersion': calculate_dispersion(segments, num_segments),
            'Plot': generate_plot(profile.total_tokens, positions, plot_width)
        })

    return sorted(results, key=lambda x: x['FileID'])


def print_results(results):
    """格式化打印结果"""
    header = ("{:<8} {:<6} {:<20} {:<12} {:<10} {:<10} {:<50}"
              .format('Seq', 'FileID', 'Filename', 'TotalTokens', 'Freq', 'Dispersion', 'Plot'))
    print(header)
    print("-" * len(header))

    for row in results:
        print("{:<8} {:<6} {:<20} {:<12} {:<10} {:<10.3f} {}".format(
            row['Sequence'],
            row['FileID'],
            row['Filename'][:18],
            row['TotalTokens'],
            row['Frequency'],
            row['Dispersion'],
            row['Plot']
        ))


def main():
    folder_path = r"D:\桌面\data"
    target_word = input("请输入要分析的关键词：").strip()

    results = plot_analysis(folder_path, target_word)
    print_results(results)

    # 保存CSV
    save = input("是否保存结果到CSV文件？(y/n): ").lower()
    if save == 'y':
        import pandas as pd
        df = pd.DataFrame(results)
        df.to_csv(f"plot_{target_word}.csv", index=False, encoding='utf-8-sig')
        print("结果已保存至 plot_{}.csv".format(target_word))


if __name__ == "__main__":
    main()