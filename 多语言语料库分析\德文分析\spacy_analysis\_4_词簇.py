# coding=utf-8
"""
德文文本分析 - 词簇分析 (使用spaCy库)
目标：尽可能接近AntConc的分析结果
"""
import spacy
import os
import sys
import csv
import re
from collections import defaultdict

sys.stdout.reconfigure(encoding='utf-8')

# 加载德文模型
try:
    nlp = spacy.load("de_core_news_sm")
    nlp.max_length = 5000000
    print("成功加载spaCy德文模型")
except OSError:
    print("请先安装德文模型: python -m spacy download de_core_news_sm")
    sys.exit(1)

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip().lower() for line in f if line.strip()])
    return stopwords

def clean_text(text):
    """清理文本"""
    text = re.sub(r'[^a-zA-ZäöüßÄÖÜ0-9\s]', ' ', text)
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def segment_text(text, stopwords):
    """使用spaCy进行德文分词"""
    text = clean_text(text)
    
    doc = nlp(text)
    
    filtered_words = []
    for token in doc:
        word = token.text.strip().lower()
        lemma = token.lemma_.strip().lower()
        
        final_word = lemma if lemma and lemma != '-PRON-' else word
        
        if (len(final_word) >= 2 and 
            final_word not in stopwords and
            not token.is_stop and
            not token.is_punct and
            not token.is_space and
            not final_word.isdigit() and
            token.pos_ not in ['PUNCT', 'SPACE', 'X'] and
            token.is_alpha):
            filtered_words.append(final_word)
    
    return filtered_words

class GermanSpacyClusterProcessor:
    def __init__(self, folder_path, stopwords_file):
        self.folder_path = folder_path
        self.files = sorted([f for f in os.listdir(folder_path) if f.endswith('.txt')])
        self.file_index = {filename: idx for idx, filename in enumerate(self.files)}
        self.cluster_cache = defaultdict(lambda: {'count': 0, 'files': set()})
        self.stopwords = load_stopwords(stopwords_file)
        
    def extract_clusters(self, target_word, position='L', length=2):
        """
        提取词簇
        Args:
            target_word: 目标词
            position: 位置 ('L'=左, 'R'=右, 'C'=中心)
            length: 词簇长度
        Returns:
            词簇统计结果
        """
        print(f"提取词簇: 目标词='{target_word}', 位置={position}, 长度={length}")
        
        cluster_stats = defaultdict(lambda: {'count': 0, 'files': set()})
        
        for filename in self.files:
            filepath = os.path.join(self.folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                # 分词
                words = segment_text(text, self.stopwords)
                
                # 查找目标词并提取词簇
                for i, word in enumerate(words):
                    if word.lower() == target_word.lower():
                        cluster = self._extract_cluster_at_position(words, i, position, length)
                        if cluster:
                            cluster_stats[cluster]['count'] += 1
                            cluster_stats[cluster]['files'].add(filename)
                            
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
        
        return cluster_stats
    
    def _extract_cluster_at_position(self, words, target_index, position, length):
        """在指定位置提取词簇"""
        if position == 'L':  # 左侧词簇
            start_idx = max(0, target_index - length + 1)
            end_idx = target_index + 1
        elif position == 'R':  # 右侧词簇
            start_idx = target_index
            end_idx = min(len(words), target_index + length)
        elif position == 'C':  # 中心词簇
            half_length = length // 2
            start_idx = max(0, target_index - half_length)
            end_idx = min(len(words), target_index + half_length + 1)
        else:
            return None
        
        if end_idx - start_idx >= length:
            cluster_words = words[start_idx:end_idx]
            return ' '.join(cluster_words)
        
        return None
    
    def analyze_german_cluster_features(self, cluster_stats, target_word):
        """
        分析德文词簇的特色功能
        Args:
            cluster_stats: 词簇统计数据
            target_word: 目标词
        Returns:
            德文特色分析结果
        """
        print(f"\n=== 德文词簇特色分析 ===")
        
        # 分析包含变音符号的词簇
        umlaut_clusters = []
        compound_clusters = []
        preposition_clusters = []
        
        for cluster, stats in cluster_stats.items():
            if stats['count'] >= 2:  # 只分析频率>=2的词簇
                # 检查变音符号
                if any(char in cluster for char in 'äöüß'):
                    umlaut_clusters.append((cluster, stats['count']))
                
                # 检查复合词（长词）
                words_in_cluster = cluster.split()
                for word in words_in_cluster:
                    if len(word) >= 8:  # 德文复合词通常较长
                        compound_clusters.append((cluster, stats['count'], word))
                        break
                
                # 检查德文介词
                german_prepositions = ['von', 'mit', 'in', 'auf', 'für', 'zu', 'bei', 'nach', 'über', 'unter']
                for prep in german_prepositions:
                    if prep in cluster.split():
                        preposition_clusters.append((cluster, stats['count'], prep))
                        break
        
        # 显示变音符号词簇
        if umlaut_clusters:
            umlaut_clusters.sort(key=lambda x: x[1], reverse=True)
            print(f"包含德文变音符号的词簇 (共{len(umlaut_clusters)}个):")
            for i, (cluster, freq) in enumerate(umlaut_clusters[:10], 1):
                print(f"{i}. '{cluster}' ({freq}次)")
        
        # 显示复合词词簇
        if compound_clusters:
            compound_clusters.sort(key=lambda x: x[1], reverse=True)
            print(f"\n包含德文复合词的词簇 (共{len(compound_clusters)}个):")
            for i, (cluster, freq, compound) in enumerate(compound_clusters[:10], 1):
                print(f"{i}. '{cluster}' ({freq}次) - 复合词: {compound}")
        
        # 显示介词词簇
        if preposition_clusters:
            preposition_clusters.sort(key=lambda x: x[1], reverse=True)
            print(f"\n包含德文介词的词簇 (共{len(preposition_clusters)}个):")
            for i, (cluster, freq, prep) in enumerate(preposition_clusters[:10], 1):
                print(f"{i}. '{cluster}' ({freq}次) - 介词: {prep}")
        
        return {
            'umlaut_clusters': umlaut_clusters,
            'compound_clusters': compound_clusters,
            'preposition_clusters': preposition_clusters
        }
    
    def generate_report(self, cluster_stats, min_frequency=2):
        """
        生成词簇分析报告
        Args:
            cluster_stats: 词簇统计数据
            min_frequency: 最小频率阈值
        Returns:
            报告列表
        """
        # 过滤低频词簇
        filtered_clusters = {
            cluster: stats for cluster, stats in cluster_stats.items()
            if stats['count'] >= min_frequency
        }
        
        # 按频率排序
        sorted_clusters = sorted(
            filtered_clusters.items(),
            key=lambda x: x[1]['count'],
            reverse=True
        )
        
        report = []
        for rank, (cluster, stats) in enumerate(sorted_clusters, 1):
            report.append({
                'Cluster': cluster,
                'Rank': rank,
                'Frequency': stats['count'],
                'Range': len(stats['files']),
                'Files': '; '.join(sorted(stats['files']))
            })
        
        return report
    
    def print_report(self, report, max_display=20):
        """打印词簇分析报告"""
        if not report:
            print("没有找到符合条件的词簇")
            return
        
        print(f"\n=== 词簇分析结果 (显示前{min(max_display, len(report))}个) ===")
        print(f"{'排名':<6} {'词簇':<40} {'频率':<8} {'范围':<8}")
        print("-" * 70)
        
        for item in report[:max_display]:
            cluster = item['Cluster'][:38] + '..' if len(item['Cluster']) > 40 else item['Cluster']
            print(f"{item['Rank']:<6} {cluster:<40} {item['Frequency']:<8} {item['Range']:<8}")
    
    def save_report(self, report, target_word, position, length):
        """保存词簇分析报告"""
        if not report:
            print("没有结果可保存")
            return
        
        filename = f"spacy_german_cluster_{target_word}_{position}_{length}.csv"
        with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.DictWriter(f, fieldnames=['Cluster', 'Rank', 'Frequency', 'Range', 'Files'])
            writer.writeheader()
            writer.writerows(report)
        
        print(f"结果已保存至: {filename}")
    
    def run_analysis(self, target_word="china", position='L', length=2, min_freq=2):
        """运行词簇分析"""
        print("=== 德文词簇分析 (spaCy库) ===")
        print("目标：尽可能接近AntConc的分析结果")
        print("特色：德文语言特色分析\n")
        
        print(f"分析参数:")
        print(f"- 目标词: {target_word}")
        print(f"- 位置: {position}")
        print(f"- 长度: {length}")
        print(f"- 最小频率: {min_freq}")
        
        # 执行分析
        print(f"\n开始分析...")
        cluster_stats = self.extract_clusters(target_word, position, length)
        
        if not cluster_stats:
            print(f"未找到包含词语 '{target_word}' 的词簇")
            return
        
        # 生成报告
        report = self.generate_report(cluster_stats, min_freq)
        
        if report:
            self.print_report(report)
            
            print(f"\n=== 统计信息 ===")
            print(f"总词簇数: {len(report)}")
            print(f"总频次: {sum(item['Frequency'] for item in report)}")
            
            # 德文特色分析
            german_features = self.analyze_german_cluster_features(cluster_stats, target_word)
            
            # 保存结果
            self.save_report(report, target_word, position, length)
        else:
            print(f"未找到频率 >= {min_freq} 的词簇")

def main():
    """主函数"""
    folder_path = "../德语文本"
    stopwords_file = "../stopwords-de.txt"
    
    processor = GermanSpacyClusterProcessor(folder_path, stopwords_file)
    processor.run_analysis()

if __name__ == "__main__":
    main()
