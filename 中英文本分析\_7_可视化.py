# coding=utf-8
import os
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import WordCloud, Bar, Line, Tab
from pyecharts.globals import ThemeType
from pyecharts.components import Table


class TextVisualizer:
    """文本可视化类 - 多页面版本"""

    def __init__(self):
        """初始化可视化器"""
        self.tab = Tab()
        self.theme = ThemeType.LIGHT

    def add_title(self, title, subtitle=""):
        """添加标题配置"""
        return opts.TitleOpts(
            title=title,
            subtitle=subtitle,
            pos_left="center"
        )

    def visualize_word_frequency(self, csv_file):
        """
        可视化词频分析结果
        - 词云图
        - 前20高频词柱状图
        Args:
            csv_file: 词频分析结果CSV文件路径
        Returns:
            tuple: (词云图, 柱状图)
        """
        if not os.path.exists(csv_file):
            print(f"文件不存在: {csv_file}")
            return None

        df = pd.read_csv(csv_file)
        words_freq = list(zip(df['Word'].tolist(), df['Frequency'].tolist()))

        # 1. 词云图
        wordcloud = (
            WordCloud()
            .add(
                "",
                words_freq[:100],  # 展示前100个词
                word_size_range=[15, 80],
                textstyle_opts=opts.TextStyleOpts(font_family="Microsoft YaHei")
            )
            .set_global_opts(
                title_opts=self.add_title("词频分析 - 词云图"),
                tooltip_opts=opts.TooltipOpts(is_show=True)
            )
        )

        # 2. 高频词柱状图
        top_20 = df.head(20)
        bar = (
            Bar()
            .add_xaxis(top_20['Word'].tolist())
            .add_yaxis(
                "词频",
                top_20['Frequency'].tolist(),
                label_opts=opts.LabelOpts(position="top")
            )
            .set_global_opts(
                title_opts=self.add_title("词频分析 - 前20高频词"),
                xaxis_opts=opts.AxisOpts(
                    axislabel_opts=opts.LabelOpts(rotate=45),
                    interval=0
                ),
                datazoom_opts=[opts.DataZoomOpts()],
                tooltip_opts=opts.TooltipOpts(is_show=True)
            )
        )

        return wordcloud, bar

    def visualize_kwic(self, csv_file):
        """
        可视化KWIC分析结果
        - 直接展示关键词的上下文
        - 对相同的上下文进行去重
        Args:
            csv_file: KWIC分析结果CSV文件路径
        Returns:
            Table: KWIC表格
        """
        if not os.path.exists(csv_file):
            print(f"文件不存在: {csv_file}")
            return None

        df = pd.read_csv(csv_file)

        # 去重处理
        # 1. 将left和right列组合成一个字符串作为去重依据
        df['context'] = df['left'] + '|' + df['right']
        df = df.drop_duplicates(subset=['context'])
        df = df.drop('context', axis=1)  # 删除临时列

        # 创建表格数据
        table_data = []
        for _, row in df.iterrows():
            table_data.append([row['left'], row['keyword'], row['right']])

        # 使用Table组件展示KWIC结果
        headers = ["左上下文", "关键词", "右上下文"]
        table = Table()

        # 设置表格样式
        table.add(headers, table_data[:])  # 显示前50条结果
        table.set_global_opts(
            title_opts=self.add_title(f"KWIC分析结果（去重后共{len(df)}条）")
        )

        return table

    def visualize_plot(self, csv_file):
        """
        可视化Plot分析结果
        Args:
            csv_file: Plot分析结果CSV文件路径
        Returns:
            tuple: (柱状图, 表格)
        """
        if not os.path.exists(csv_file):
            print(f"文件不存在: {csv_file}")
            return None

        df = pd.read_csv(csv_file)

        # 1. 创建频率分布图
        bar = (
            Bar()
            .add_xaxis(df['Filename'].tolist())
            .add_yaxis(
                "词频",
                df['Frequency'].tolist(),
                label_opts=opts.LabelOpts(position="top")
            )
            .set_global_opts(
                title_opts=self.add_title("词语分布分析 - 文件频率分布"),
                xaxis_opts=opts.AxisOpts(
                    axislabel_opts=opts.LabelOpts(rotate=45),
                    interval=0
                ),
                datazoom_opts=[opts.DataZoomOpts()],
                tooltip_opts=opts.TooltipOpts(is_show=True)
            )
        )

        # 2. 创建数据表格
        table_data = []
        for _, row in df.iterrows():
            table_data.append([
                str(row['Sequence']),
                str(row['FileID']),
                row['Filename'],
                str(row['TotalTokens']),
                str(row['Frequency']),
                f"{row['Dispersion']:.4f}",
                row['Plot']
            ])

        headers = ["序号", "文件ID", "文件名", "总词数", "频率", "离散度", "分布图"]
        table = Table()
        table.add(headers, table_data)
        table.set_global_opts(
            title_opts=self.add_title("词语分布分析 - 详细数据")
        )

        return bar, table

    def visualize_clusters(self, csv_file):
        """
        可视化词簇分析结果
        Args:
            csv_file: 词簇分析结果CSV文件路径
        Returns:
            tuple: (柱状图, 表格)
        """
        if not os.path.exists(csv_file):
            print(f"文件不存在: {csv_file}")
            return None

        df = pd.read_csv(csv_file)

        # 1. 创建词簇频率分布图
        bar = (
            Bar()
            .add_xaxis(df['Cluster'].tolist())
            .add_yaxis(
                "频率",
                df['Frequency'].tolist(),
                label_opts=opts.LabelOpts(position="top")
            )
            .set_global_opts(
                title_opts=self.add_title("词簇分析 - 频率分布"),
                xaxis_opts=opts.AxisOpts(
                    axislabel_opts=opts.LabelOpts(rotate=45),
                    interval=0
                ),
                datazoom_opts=[opts.DataZoomOpts()],
                tooltip_opts=opts.TooltipOpts(is_show=True)
            )
        )

        # 2. 创建数据表格
        table_data = []
        for _, row in df.iterrows():
            table_data.append([
                row['Cluster'],
                str(row['Rank']),
                str(row['Frequency']),
                str(row['Range'])
            ])

        headers = ["词簇", "排名", "频率", "范围"]
        table = Table()
        table.add(headers, table_data)
        table.set_global_opts(
            title_opts=self.add_title("词簇分析 - 详细数据")
        )

        return bar, table

    def visualize_collocate(self, csv_file):
        """
        可视化搭配分析结果
        Args:
            csv_file: 搭配分析结果CSV文件路径
        Returns:
            tuple: (柱状图, 表格)
        """
        if not os.path.exists(csv_file):
            print(f"文件不存在: {csv_file}")
            return None

        df = pd.read_csv(csv_file)

        # 1. 创建搭配词频率分布图
        bar = (
            Bar()
            .add_xaxis(df['collocate'].tolist())
            .add_yaxis(
                "频率",
                df['freqL'].tolist(),  # 假设使用 freqL 作为频率数据，可按需修改
                label_opts=opts.LabelOpts(position="top")
            )
            .set_global_opts(
                title_opts=self.add_title("搭配分析 - 频率分布"),
                xaxis_opts=opts.AxisOpts(
                    axislabel_opts=opts.LabelOpts(rotate=45),
                    interval=0
                ),
                datazoom_opts=[opts.DataZoomOpts()],
                tooltip_opts=opts.TooltipOpts(is_show=True)
            )
        )

        # 2. 创建数据表格
        table_data = []
        for _, row in df.iterrows():
            table_data.append([
                row['collocate'],
                str(row['rank']),
                str(row['freqL']),
                str(row['freqR']),
                str(row['range']),
                str(row['likelihood']),
                str(row['effect'])
            ])

        headers = ["搭配词", "排名", "左频率", "右频率", "范围", "似然性", "效应"]
        table = Table()
        table.add(headers, table_data)
        table.set_global_opts(
            title_opts=self.add_title("搭配分析 - 详细数据")
        )

        return bar, table

    def create_homepage(self):
        """创建主页
        Returns:
            Table: 主页表格
        """
        # 创建简单的导航指南
        table_data = [
            ["词频分析", "查看文本中词语出现的频率、词云图等"],
            ["KWIC分析", "关键词上下文索引，查看特定词语的上下文环境"],
            ["词语分布分析", "分析词语在不同文件中的分布情况"],
            ["词簇分析", "分析词语簇的分布和频率"],
            ["搭配分析", "分析词语的常见搭配"]
        ]

        headers = ["分析类型", "说明"]
        table = Table()
        table.add(headers, table_data)
        table.set_global_opts(
            title_opts=self.add_title("中文文本分析可视化导航", "请点击上方标签页查看各项分析结果")
        )

        return table

    def add_all_visualizations(self, word_freq_file, kwic_file, plot_file, cluster_file, collocate_file):
        """添加所有可视化到Tab"""
        # 首页
        homepage_table = self.create_homepage()
        self.tab.add(homepage_table, "首页")

        # 词频分析
        word_freq_result = self.visualize_word_frequency(word_freq_file)
        if word_freq_result:
            wordcloud, bar = word_freq_result
            self.tab.add(wordcloud, "词频分析-词云图")
            self.tab.add(bar, "词频分析-柱状图")

        # KWIC分析
        kwic_table = self.visualize_kwic(kwic_file)
        if kwic_table:
            self.tab.add(kwic_table, "KWIC分析")

        # 词语分布分析
        plot_result = self.visualize_plot(plot_file)
        if plot_result:
            plot_bar, plot_table = plot_result
            self.tab.add(plot_bar, "词语分布-频率图")
            self.tab.add(plot_table, "词语分布-详细数据")

        # 词簇分析
        cluster_result = self.visualize_clusters(cluster_file)
        if cluster_result:
            cluster_bar, cluster_table = cluster_result
            self.tab.add(cluster_bar, "词簇分析-频率图")
            self.tab.add(cluster_table, "词簇分析-详细数据")

        # 搭配分析
        collocate_result = self.visualize_collocate(collocate_file)
        if collocate_result:
            collocate_bar, collocate_table = collocate_result
            self.tab.add(collocate_bar, "搭配分析-频率图")
            self.tab.add(collocate_table, "搭配分析-详细数据")

    def render(self, output_file="text_visualization.html"):
        """渲染所有图表到HTML文件"""
        self.tab.render(output_file)


def main():
    """主函数"""
    visualizer = TextVisualizer()

    # 设置输入文件路径
    word_freq_file = "mixed_language_word_analysis_report.csv"
    kwic_file = "KWIC_china.csv"
    plot_file = "plot_china.csv"
    cluster_file = "cluster_china_L_3.csv"
    collocate_file = "collocate_china.csv"

    # 生成可视化
    print("开始生成多页面可视化...")

    # 添加所有可视化到Tab
    visualizer.add_all_visualizations(
        word_freq_file,
        kwic_file,
        plot_file,
        cluster_file,
        collocate_file
    )

    # 保存结果
    output_file = "混合语言文本分析可视化.html"
    print(f"\n正在生成可视化报告: {output_file}")
    visualizer.render(output_file)
    print("多页面可视化报告生成完成！")


if __name__ == "__main__":
    main()