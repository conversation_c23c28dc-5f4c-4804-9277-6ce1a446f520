# coding=utf-8
"""
批量生成多语言语料库分析代码
"""
import os

def create_chinese_spacy_files():
    """创建中文spaCy分析的剩余文件"""
    
    # _3_Plot.py
    plot_code = '''# coding=utf-8
"""
中文文本分析 - Plot分析 (使用spaCy库)
目标：尽可能接近AntConc的分析结果
"""
import spacy
import os
import sys
import pandas as pd
import numpy as np
import re
from collections import defaultdict

sys.stdout.reconfigure(encoding='utf-8')

try:
    nlp = spacy.load("zh_core_web_sm")
except OSError:
    print("请先安装中文模型: python -m spacy download zh_core_web_sm")
    sys.exit(1)

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip() for line in f if line.strip()])
    return stopwords

def segment_text(text, stopwords):
    """使用spaCy进行分词"""
    doc = nlp(text)
    filtered_words = []
    for token in doc:
        word = token.lemma_.strip() if token.lemma_ and token.lemma_ != '-PRON-' else token.text.strip()
        if (len(word) >= 2 and 
            word not in stopwords and
            not token.is_stop and
            not token.is_punct and
            not token.is_space and
            not word.isdigit()):
            filtered_words.append(word)
    return filtered_words

def calculate_dispersion(positions, total_tokens, num_segments=10):
    """计算词语的离散度"""
    if not positions or total_tokens == 0:
        return 0.0
    
    segment_size = total_tokens / num_segments
    segment_counts = [0] * num_segments
    
    for pos in positions:
        segment_idx = min(int(pos / segment_size), num_segments - 1)
        segment_counts[segment_idx] += 1
    
    expected_freq = len(positions) / num_segments
    chi_square = 0
    for count in segment_counts:
        if expected_freq > 0:
            chi_square += ((count - expected_freq) ** 2) / expected_freq
    
    max_chi_square = len(positions) * (num_segments - 1)
    if max_chi_square > 0:
        dispersion = 1 - (chi_square / max_chi_square)
    else:
        dispersion = 1.0
    
    return max(0.0, min(1.0, dispersion))

def create_plot_visualization(positions, total_tokens, num_segments=50):
    """创建词语分布的可视化字符串"""
    if not positions:
        return "-" * num_segments
    
    segment_size = total_tokens / num_segments
    plot_chars = ['-'] * num_segments
    
    for pos in positions:
        segment_idx = min(int(pos / segment_size), num_segments - 1)
        plot_chars[segment_idx] = '|'
    
    return ''.join(plot_chars)

def analyze_word_distribution(folder_path, stopwords_file, target_word):
    """分析指定词语在各个文件中的分布"""
    stopwords = load_stopwords(stopwords_file)
    results = []
    file_id = 1
    
    print(f"分析词语 '{target_word}' 的分布情况...")
    
    for filename in sorted(os.listdir(folder_path)):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                words = segment_text(text, stopwords)
                total_tokens = len(words)
                
                positions = []
                for i, word in enumerate(words):
                    if word == target_word:
                        positions.append(i)
                
                frequency = len(positions)
                
                if frequency > 0:
                    dispersion = calculate_dispersion(positions, total_tokens)
                    plot_viz = create_plot_visualization(positions, total_tokens)
                    
                    results.append({
                        'Sequence': len(results) + 1,
                        'FileID': file_id,
                        'Filename': filename,
                        'TotalTokens': total_tokens,
                        'Frequency': frequency,
                        'Dispersion': dispersion,
                        'Plot': plot_viz
                    })
                
                file_id += 1
                print(f"已处理: {filename} (词数: {total_tokens}, 匹配: {frequency})")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
                file_id += 1
    
    return results

def main():
    """主函数"""
    folder_path = "../中文文本"
    stopwords_file = "../stopwords-zh.txt"
    target_word = "数据"  # 固定目标词
    
    print("=== 中文Plot分析 (spaCy库) ===")
    
    results = analyze_word_distribution(folder_path, stopwords_file, target_word)
    
    if results:
        df = pd.DataFrame(results)
        filename = f"spacy_plot_{target_word}.csv"
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"结果已保存至: {filename}")
        
        total_freq = sum(r['Frequency'] for r in results)
        avg_dispersion = np.mean([r['Dispersion'] for r in results])
        print(f"匹配文件数: {len(results)}, 总出现次数: {total_freq}, 平均离散度: {avg_dispersion:.4f}")
    else:
        print(f"在语料库中未找到词语 '{target_word}'")

if __name__ == "__main__":
    main()'''
    
    with open('多语言语料库分析/中文分析/spacy_analysis/_3_Plot.py', 'w', encoding='utf-8') as f:
        f.write(plot_code)
    
    print("已创建 spaCy _3_Plot.py")

def create_chinese_nltk_files():
    """创建中文NLTK分析文件"""
    
    # 创建目录
    os.makedirs('多语言语料库分析/中文分析/nltk_analysis', exist_ok=True)
    
    # _1_词频词云.py
    freq_code = '''# coding=utf-8
"""
中文文本分析 - 词频分析 (使用NLTK库)
目标：尽可能接近AntConc的分析结果
"""
import nltk
import pandas as pd
import sys
import os
from collections import defaultdict, Counter
import re

sys.stdout.reconfigure(encoding='utf-8')

# 下载必要的NLTK数据
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip() for line in f if line.strip()])
    
    punctuation = set(['，', '。', '！', '？', '、', '；', '：', '"', '"', ''', ''', 
                      '（', '）', '【', '】', '.', ',', '!', '?', ';', ':', '(', ')', 
                      '[', ']', '{', '}', '<', '>', '/', '\\\\', '|', '-', '_', '+', 
                      '=', '*', '&', '^', '%', '$', '#', '@', '~', '`'])
    stopwords.update(punctuation)
    return stopwords

def chinese_word_segment(text):
    """简单的中文分词（基于字符和标点）"""
    # 使用正则表达式进行简单的中文分词
    # 这是一个简化版本，实际应用中建议使用专业的中文分词库
    words = []
    current_word = ""
    
    for char in text:
        if '\\u4e00' <= char <= '\\u9fa5':  # 中文字符
            current_word += char
        else:
            if current_word:
                words.append(current_word)
                current_word = ""
            if char.strip() and not char in '，。！？、；：""''（）【】':
                words.append(char)
    
    if current_word:
        words.append(current_word)
    
    return words

def segment_text(text, stopwords):
    """使用NLTK进行文本分词"""
    # 对于中文，NLTK的分词效果有限，这里使用简单的字符级分词
    words = chinese_word_segment(text)
    
    filtered_words = []
    for word in words:
        word = word.strip()
        if (len(word) >= 2 and 
            word not in stopwords and
            not word.isdigit() and
            not word.isspace()):
            filtered_words.append(word)
    
    return filtered_words

def process_files(folder_path, stopwords_file):
    """处理文件夹中的所有文本文件"""
    stopwords = load_stopwords(stopwords_file)
    word_freq = Counter()
    file_count = 0
    total_words = 0
    
    print(f"开始处理文件夹: {folder_path}")
    
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                words = segment_text(text, stopwords)
                word_freq.update(words)
                total_words += len(words)
                file_count += 1
                
                print(f"已处理: {filename} (词数: {len(words)})")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    print(f"\\n处理完成: 文件数量: {file_count}, 总词数: {total_words}, 不重复词数: {len(word_freq)}")
    return word_freq

def generate_report(word_freq):
    """生成词频分析报告"""
    word_list = [(word, freq) for word, freq in word_freq.most_common()]
    total_words = sum(freq for _, freq in word_list)
    
    report_data = []
    for rank, (word, freq) in enumerate(word_list, 1):
        percentage = (freq / total_words) * 100
        report_data.append({
            'Rank': rank,
            'Word': word,
            'Frequency': freq,
            'Percentage': round(percentage, 4)
        })
    
    return pd.DataFrame(report_data)

def main():
    """主函数"""
    folder_path = "../中文文本"
    stopwords_file = "../stopwords-zh.txt"
    output_file = 'nltk_word_frequency_report.csv'
    
    print("=== 中文词频分析 (NLTK库) ===")
    
    word_freq = process_files(folder_path, stopwords_file)
    
    if not word_freq:
        print("未找到有效的词汇数据")
        return
    
    report_df = generate_report(word_freq)
    
    print("\\n=== 前20个高频词 ===")
    print(report_df.head(20).to_string(index=False))
    
    report_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\\n完整分析结果已保存至: {output_file}")

if __name__ == "__main__":
    main()'''
    
    with open('多语言语料库分析/中文分析/nltk_analysis/_1_词频词云.py', 'w', encoding='utf-8') as f:
        f.write(freq_code)
    
    print("已创建 NLTK 中文分析文件")

def create_english_nltk_files():
    """创建英文NLTK分析文件"""
    
    # 创建目录
    os.makedirs('多语言语料库分析/英文分析/nltk_analysis', exist_ok=True)
    
    # _1_词频词云.py
    freq_code = '''# coding=utf-8
"""
英文文本分析 - 词频分析 (使用NLTK库)
目标：尽可能接近AntConc的分析结果
"""
import nltk
import pandas as pd
import sys
import os
from collections import defaultdict, Counter
import re
from nltk.tokenize import word_tokenize
from nltk.stem import WordNetLemmatizer

sys.stdout.reconfigure(encoding='utf-8')

# 下载必要的NLTK数据
required_data = ['punkt', 'wordnet', 'averaged_perceptron_tagger', 'stopwords']
for data in required_data:
    try:
        nltk.data.find(f'tokenizers/{data}' if data == 'punkt' else f'corpora/{data}' if data in ['wordnet', 'stopwords'] else f'taggers/{data}')
    except LookupError:
        nltk.download(data)

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip().lower() for line in f if line.strip()])
    return stopwords

def clean_text(text):
    """清理文本"""
    text = re.sub(r'[^a-zA-Z\\s]', ' ', text)
    text = re.sub(r'\\s+', ' ', text)
    return text.strip()

def segment_text(text, stopwords):
    """使用NLTK进行英文分词和词形还原"""
    text = clean_text(text)
    tokens = word_tokenize(text.lower())
    
    lemmatizer = WordNetLemmatizer()
    filtered_words = []
    
    for token in tokens:
        if (len(token) >= 2 and 
            token not in stopwords and
            token.isalpha()):
            lemma = lemmatizer.lemmatize(token)
            filtered_words.append(lemma)
    
    return filtered_words

def process_files(folder_path, stopwords_file):
    """处理文件夹中的所有文本文件"""
    stopwords = load_stopwords(stopwords_file)
    word_freq = Counter()
    file_count = 0
    total_words = 0
    
    print(f"开始处理文件夹: {folder_path}")
    
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                words = segment_text(text, stopwords)
                word_freq.update(words)
                total_words += len(words)
                file_count += 1
                
                print(f"已处理: {filename} (词数: {len(words)})")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    print(f"\\n处理完成: 文件数量: {file_count}, 总词数: {total_words}, 不重复词数: {len(word_freq)}")
    return word_freq

def generate_report(word_freq):
    """生成词频分析报告"""
    word_list = [(word, freq) for word, freq in word_freq.most_common()]
    total_words = sum(freq for _, freq in word_list)
    
    report_data = []
    for rank, (word, freq) in enumerate(word_list, 1):
        percentage = (freq / total_words) * 100
        report_data.append({
            'Rank': rank,
            'Word': word,
            'Frequency': freq,
            'Percentage': round(percentage, 4)
        })
    
    return pd.DataFrame(report_data)

def main():
    """主函数"""
    folder_path = "../英文文本"
    stopwords_file = "../stopwords-en.txt"
    output_file = 'nltk_word_frequency_report.csv'
    
    print("=== 英文词频分析 (NLTK库) ===")
    
    word_freq = process_files(folder_path, stopwords_file)
    
    if not word_freq:
        print("未找到有效的词汇数据")
        return
    
    report_df = generate_report(word_freq)
    
    print("\\n=== 前20个高频词 ===")
    print(report_df.head(20).to_string(index=False))
    
    report_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\\n完整分析结果已保存至: {output_file}")

if __name__ == "__main__":
    main()'''
    
    with open('多语言语料库分析/英文分析/nltk_analysis/_1_词频词云.py', 'w', encoding='utf-8') as f:
        f.write(freq_code)
    
    print("已创建 NLTK 英文分析文件")

def main():
    """主函数"""
    print("开始批量生成多语言语料库分析代码...")
    
    create_chinese_spacy_files()
    create_chinese_nltk_files()
    create_english_nltk_files()
    
    print("\\n批量生成完成！")

if __name__ == "__main__":
    main()
