# coding=utf-8
"""
德文文本分析 - 词频分析 (使用NLTK库)
目标：尽可能接近AntConc的分析结果
"""
import nltk
import pandas as pd
import sys
import os
from collections import defaultdict, Counter
import re

sys.stdout.reconfigure(encoding='utf-8')

# 下载必要的NLTK数据
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    print("下载NLTK punkt tokenizer...")
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    print("下载NLTK stopwords...")
    nltk.download('stopwords')

def load_stopwords(stopwords_file):
    """
    加载停用词列表
    Args:
        stopwords_file: 停用词文件路径
    Returns:
        停用词集合
    """
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip().lower() for line in f if line.strip()])
    
    # 添加标点符号作为停用词
    punctuation = set(['.', ',', '!', '?', ';', ':', '(', ')', '[', ']', '{', '}', 
                      '<', '>', '/', '\\', '|', '-', '_', '+', '=', '*', '&', '^', 
                      '%', '$', '#', '@', '~', '`', '"', "'", '„', '"', '»', '«'])
    stopwords.update(punctuation)
    
    return stopwords

def clean_text(text):
    """清理文本，移除特殊字符和多余空白"""
    # 移除特殊字符，保留德文字母、数字和变音符号
    text = re.sub(r'[^a-zA-ZäöüÄÖÜß0-9\s]', ' ', text)
    # 移除多余空白
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def basic_german_segment(text):
    """基础德文分词方法（当专门的德文工具不可用时）"""
    words = []
    # 简单的空格分词
    tokens = text.lower().split()
    
    for token in tokens:
        # 移除标点符号，保留德文字符
        clean_token = re.sub(r'[^a-zA-ZäöüÄÖÜß]', '', token)
        if len(clean_token) >= 2:
            words.append(clean_token)
    
    return words

def segment_text(text, stopwords):
    """
    使用NLTK进行德文分词
    Args:
        text: 输入文本
        stopwords: 停用词集合
    Returns:
        分词后的词汇列表
    """
    # 清理文本
    text = clean_text(text)
    
    try:
        # 使用NLTK的word_tokenize进行分词
        from nltk.tokenize import word_tokenize
        tokens = word_tokenize(text, language='german')
        
        # 过滤停用词和短词
        filtered_words = []
        for token in tokens:
            word = token.strip().lower()
            
            if (len(word) >= 2 and  # 至少2个字符
                word not in stopwords and
                not word.isdigit() and  # 不是纯数字
                not word.isspace() and  # 不是空白
                re.match(r'^[a-zA-ZäöüÄÖÜß]+$', word)):  # 只保留德文字母
                filtered_words.append(word)
                
    except Exception as e:
        print(f"NLTK分词出错: {e}")
        # 降级到基础分词方法
        words = basic_german_segment(text)
        filtered_words = []
        for word in words:
            word = word.strip().lower()
            if (len(word) >= 2 and 
                word not in stopwords and
                not word.isdigit() and
                not word.isspace()):
                filtered_words.append(word)
    
    return filtered_words

def process_files(folder_path, stopwords_file):
    """
    处理文件夹中的所有文本文件
    Args:
        folder_path: 文件夹路径
        stopwords_file: 停用词文件路径
    Returns:
        词频统计字典
    """
    stopwords = load_stopwords(stopwords_file)
    word_freq = Counter()
    file_count = 0
    total_words = 0
    
    print(f"开始处理文件夹: {folder_path}")
    print("使用NLTK库进行德文分词")
    
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                # 分词
                words = segment_text(text, stopwords)
                
                # 统计词频
                word_freq.update(words)
                total_words += len(words)
                file_count += 1
                
                print(f"已处理: {filename} (词数: {len(words)})")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    print(f"\n处理完成:")
    print(f"- 文件数量: {file_count}")
    print(f"- 总词数: {total_words}")
    print(f"- 不重复词数: {len(word_freq)}")
    
    return word_freq

def generate_report(word_freq):
    """
    生成词频分析报告
    Args:
        word_freq: 词频统计Counter对象
    Returns:
        DataFrame格式的报告
    """
    # 转换为列表并排序
    word_list = [(word, freq) for word, freq in word_freq.most_common()]
    
    # 计算百分比
    total_words = sum(freq for _, freq in word_list)
    
    report_data = []
    for rank, (word, freq) in enumerate(word_list, 1):
        percentage = (freq / total_words) * 100
        report_data.append({
            'Rank': rank,
            'Word': word,
            'Frequency': freq,
            'Percentage': round(percentage, 4)
        })
    
    return pd.DataFrame(report_data)

def analyze_german_features(word_freq):
    """
    分析德文特色功能
    Args:
        word_freq: 词频统计Counter对象
    Returns:
        德文特色分析结果
    """
    print("\n=== 德文语言特色分析 ===")
    
    # 分析复合词（长词）
    long_words = [(word, freq) for word, freq in word_freq.items() if len(word) >= 10]
    long_words.sort(key=lambda x: x[1], reverse=True)
    
    print("德文复合词分析 (长度>=10):")
    for i, (word, freq) in enumerate(long_words[:10], 1):
        print(f"{i}. {word} ({freq}次, 长度: {len(word)})")
    
    # 分析变音符号词汇
    umlaut_words = [(word, freq) for word, freq in word_freq.items() 
                    if any(char in word for char in 'äöüß')]
    umlaut_words.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\n德文变音符号词汇分析 (共{len(umlaut_words)}个):")
    for i, (word, freq) in enumerate(umlaut_words[:10], 1):
        print(f"{i}. {word} ({freq}次)")
    
    # 分析词长分布
    length_dist = defaultdict(int)
    for word, freq in word_freq.items():
        length_dist[len(word)] += freq
    
    print(f"\n德文词长分布:")
    for length in sorted(length_dist.keys())[:15]:
        print(f"长度{length}: {length_dist[length]}个词")
    
    return {
        'long_words': long_words[:20],
        'umlaut_words': umlaut_words[:20],
        'length_distribution': dict(length_dist)
    }

def main():
    """主函数"""
    # 配置路径
    folder_path = "../德语文本"  # 德文文本文件夹
    stopwords_file = "../stopwords-de.txt"  # 德文停用词文件
    output_file = 'nltk_german_word_frequency_report.csv'
    
    print("=== 德文词频分析 (NLTK库) ===")
    print("目标：尽可能接近AntConc的分析结果")
    print("特色：德文语言特色分析\n")
    
    # 处理文件并生成报告
    word_freq = process_files(folder_path, stopwords_file)
    
    if not word_freq:
        print("未找到有效的词汇数据")
        return
    
    report_df = generate_report(word_freq)
    
    # 显示前20个结果
    print("\n=== 前20个高频词 ===")
    print(report_df.head(20).to_string(index=False))
    
    # 保存完整结果到CSV
    report_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n完整分析结果已保存至: {output_file}")
    
    # 显示统计信息
    print(f"\n=== 统计信息 ===")
    print(f"总词汇数: {len(report_df)}")
    print(f"总频次: {report_df['Frequency'].sum()}")
    print(f"平均频次: {report_df['Frequency'].mean():.2f}")
    
    # 德文特色分析
    german_features = analyze_german_features(word_freq)
    
    # 显示分词方法说明
    print(f"\n=== NLTK德文分词特点 ===")
    print("1. 使用NLTK的德文分词器")
    print("2. 支持德文变音符号 (ä, ö, ü, ß)")
    print("3. 德文复合词识别")
    print("4. 词长分布分析")
    print("5. 停用词过滤")

if __name__ == "__main__":
    main()
