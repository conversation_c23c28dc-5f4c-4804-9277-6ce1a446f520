# coding=utf-8
"""
英文文本分析 - KWIC分析 (使用TextBlob库)
目标：尽可能接近AntConc的分析结果
"""
from textblob import TextBlob
import os
import sys
import pandas as pd
import re
from collections import defaultdict

sys.stdout.reconfigure(encoding='utf-8')

def load_stopwords(stopwords_file):
    """加载停用词列表"""
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip().lower() for line in f if line.strip()])
    return stopwords

def clean_text(text):
    """清理文本，保留原始结构用于KWIC"""
    # 移除过多的空白，但保留基本结构
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def segment_text_for_kwic(text, stopwords):
    """
    使用TextBlob进行英文分词（保留更多信息用于KWIC）
    Args:
        text: 输入文本
        stopwords: 停用词集合
    Returns:
        分词后的词汇列表
    """
    # 清理文本
    text = clean_text(text)
    
    try:
        # 使用TextBlob处理
        blob = TextBlob(text)
        
        # 保留更多词汇用于KWIC上下文
        words = []
        for word in blob.words:
            word_lower = word.lower()
            
            # 词形还原
            try:
                lemma = word.lemmatize()
                final_word = lemma.lower()
            except:
                final_word = word_lower
            
            if (len(final_word) >= 2 and  # 至少2个字符
                not final_word.isdigit() and  # 不是纯数字
                final_word.isalpha()):  # 只保留字母
                words.append(final_word)
                
    except Exception as e:
        print(f"TextBlob处理出错: {e}")
        # 降级到基础分词
        words = []
        tokens = text.lower().split()
        for token in tokens:
            clean_token = re.sub(r'[^a-zA-Z]', '', token)
            if len(clean_token) >= 2 and clean_token.isalpha():
                words.append(clean_token)
    
    return words

def preprocess_folder(folder_path, stopwords_file):
    """
    预处理文件夹中的所有文件
    Args:
        folder_path: 文件夹路径
        stopwords_file: 停用词文件路径
    Returns:
        处理后的文档字典
    """
    stopwords = load_stopwords(stopwords_file)
    all_docs = {}
    word_freq = defaultdict(int)
    
    print(f"开始预处理文件夹: {folder_path}")
    
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                # 分词用于词频统计
                words = segment_text_for_kwic(text, stopwords)
                for word in words:
                    if word not in stopwords:
                        word_freq[word] += 1
                
                # 保存处理后的词汇列表用于KWIC搜索
                all_docs[filename] = words
                print(f"已处理: {filename} (词数: {len(words)})")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    print(f"预处理完成，共处理 {len(all_docs)} 个文件")
    return all_docs, word_freq

def kwic_search(all_docs, keyword, window_size=5):
    """
    执行KWIC搜索
    Args:
        all_docs: 文档字典
        keyword: 搜索关键词
        window_size: 上下文窗口大小
    Returns:
        KWIC结果列表
    """
    results = []
    
    print(f"搜索关键词: '{keyword}'，窗口大小: {window_size}")
    
    for filename, words in all_docs.items():
        # 查找关键词位置
        for i, word in enumerate(words):
            if keyword.lower() in word or word == keyword.lower():
                # 计算上下文范围
                start_idx = max(0, i - window_size)
                end_idx = min(len(words), i + window_size + 1)
                
                # 提取左上下文
                left_context = ' '.join(words[start_idx:i])
                
                # 提取右上下文
                right_context = ' '.join(words[i+1:end_idx])
                
                # 清理上下文
                left_context = left_context.strip()
                right_context = right_context.strip()
                
                results.append({
                    'file': filename,
                    'left': left_context,
                    'keyword': word,
                    'right': right_context,
                    'position': i
                })
    
    return results

def analyze_keyword_sentiment(all_docs, keyword):
    """
    使用TextBlob分析关键词上下文的情感
    Args:
        all_docs: 文档字典
        keyword: 关键词
    Returns:
        情感分析结果
    """
    print(f"\n=== 关键词 '{keyword}' 情感分析 ===")
    
    sentiment_results = []
    
    for filename, words in all_docs.items():
        # 查找关键词位置
        for i, word in enumerate(words):
            if keyword.lower() in word or word == keyword.lower():
                # 提取较大的上下文用于情感分析
                start_idx = max(0, i - 10)
                end_idx = min(len(words), i + 10)
                context = ' '.join(words[start_idx:end_idx])
                
                try:
                    blob = TextBlob(context)
                    polarity = blob.sentiment.polarity
                    subjectivity = blob.sentiment.subjectivity
                    
                    sentiment_results.append({
                        'file': filename,
                        'context': context[:100] + '...' if len(context) > 100 else context,
                        'polarity': polarity,
                        'subjectivity': subjectivity
                    })
                except:
                    continue
    
    if sentiment_results:
        avg_polarity = sum(s['polarity'] for s in sentiment_results) / len(sentiment_results)
        avg_subjectivity = sum(s['subjectivity'] for s in sentiment_results) / len(sentiment_results)
        
        print(f"关键词 '{keyword}' 的情感分析结果:")
        print(f"- 分析样本数: {len(sentiment_results)}")
        print(f"- 平均极性: {avg_polarity:.3f} ({'正面' if avg_polarity > 0 else '负面' if avg_polarity < 0 else '中性'})")
        print(f"- 平均主观性: {avg_subjectivity:.3f}")
        
        # 显示前5个情感最强的上下文
        sentiment_results.sort(key=lambda x: abs(x['polarity']), reverse=True)
        print(f"\n情感最强的5个上下文:")
        for i, result in enumerate(sentiment_results[:5], 1):
            sentiment = "正面" if result['polarity'] > 0 else "负面" if result['polarity'] < 0 else "中性"
            print(f"{i}. {sentiment} ({result['polarity']:.3f}): {result['context']}")
    
    return sentiment_results

def print_results(results, max_display=50):
    """打印KWIC结果"""
    print(f"\n=== KWIC分析结果 ===")
    print(f"{'文件':<30} {'左上下文':<30} {'关键词':<15} {'右上下文':<30}")
    print("-" * 105)
    
    for i, result in enumerate(results[:max_display]):
        filename = result['file'][:28] + '..' if len(result['file']) > 30 else result['file']
        left = result['left'][-28:] if len(result['left']) > 30 else result['left']
        keyword = result['keyword']
        right = result['right'][:28] if len(result['right']) > 30 else result['right']
        
        print(f"{filename:<30} {left:<30} {keyword:<15} {right:<30}")

def save_results(results, keyword):
    """保存KWIC结果到CSV文件"""
    if not results:
        print("没有结果可保存")
        return
    
    df = pd.DataFrame(results)
    filename = f"textblob_KWIC_{keyword}.csv"
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"结果已保存至: {filename}")

def main():
    """主函数"""
    # 配置路径
    folder_path = "../英文文本"
    stopwords_file = "../stopwords-en.txt"
    keyword = "china"  # 固定关键词用于测试
    window_size = 5
    
    print("=== 英文KWIC分析 (TextBlob库) ===")
    print("目标：尽可能接近AntConc的分析结果")
    print("特色：包含情感分析功能\n")
    
    # 预处理文件
    all_docs, word_freq = preprocess_folder(folder_path, stopwords_file)
    
    if not all_docs:
        print("未找到有效的文档")
        return
    
    # 显示高频词供参考
    print("\n=== 高频词参考 (前20个) ===")
    top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:20]
    for word, freq in top_words:
        print(f"{word}: {freq}")
    
    # 执行搜索
    results = kwic_search(all_docs, keyword, window_size)
    
    # 显示结果
    if results:
        print(f"\n找到 {len(results)} 处匹配")
        print_results(results)
        
        # 保存结果
        save_results(results, keyword)
        
        # TextBlob特色功能：情感分析
        sentiment_results = analyze_keyword_sentiment(all_docs, keyword)
        
        # 显示前10个结果
        print(f"\n前10个匹配结果:")
        for i, result in enumerate(results[:10]):
            print(f"{i+1}. {result['left']} [{result['keyword']}] {result['right']}")
    else:
        print(f"未找到关键词 '{keyword}' 的匹配结果")

if __name__ == "__main__":
    main()
