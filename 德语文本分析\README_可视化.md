# 德语文本分析可视化说明

## 概述
本可视化模块为德语文本分析提供了完整的数据可视化功能，包括词频分析、KWIC分析、词语分布分析、词簇分析和搭配分析的可视化展示。

## 功能特点

### 1. 词频分析可视化
- **词云图**: 直观展示高频词汇，字体大小反映词频高低
- **柱状图**: 显示前20个高频词的精确频率数据
- **数据来源**: `word_analysis_report.csv`

### 2. KWIC分析可视化
- **上下文表格**: 展示关键词的左右上下文
- **去重处理**: 自动去除重复的上下文组合
- **数据来源**: `KWIC_china.csv`

### 3. 词语分布分析可视化
- **频率分布图**: 显示词语在不同文件中的分布情况
- **详细数据表**: 包含序号、文件ID、文件名、总词数、频率、离散度等信息
- **数据来源**: `plot_china.csv`

### 4. 词簇分析可视化
- **频率分布图**: 展示词簇的频率分布
- **详细数据表**: 包含词簇、排名、频率、范围等信息
- **数据来源**: `cluster_china_L_2.csv`

### 5. 搭配分析可视化
- **频率分布图**: 显示搭配词的频率分布
- **详细数据表**: 包含搭配词、排名、左右频率、范围、似然性、效应等信息
- **数据来源**: `collocate_china_5-5.csv`

## 使用方法

### 运行可视化脚本
```bash
cd 德语文本分析
python _7_可视化.py
```

### 查看结果
运行完成后，会在当前目录生成 `德语文本分析可视化.html` 文件，用浏览器打开即可查看所有可视化结果。

## 文件结构
```
德语文本分析/
├── _7_可视化.py              # 可视化主程序
├── word_analysis_report.csv   # 词频分析数据
├── KWIC_china.csv            # KWIC分析数据
├── plot_china.csv            # 词语分布分析数据
├── cluster_china_L_2.csv     # 词簇分析数据
├── collocate_china_5-5.csv   # 搭配分析数据
└── 德语文本分析可视化.html    # 生成的可视化报告
```

## 技术特点

### 多页面设计
- 使用Tab组件实现多页面切换
- 每个分析功能独立展示
- 包含导航首页

### 交互功能
- 支持图表缩放和平移
- 提供数据提示框
- 支持数据筛选和查看

### 响应式设计
- 自适应不同屏幕尺寸
- 优化的字体和颜色配置
- 清晰的数据展示

## 依赖库
- pandas: 数据处理
- pyecharts: 图表生成
- os: 文件操作

## 注意事项
1. 确保所有CSV数据文件存在于同一目录
2. 如果某个数据文件不存在，对应的可视化页面将不会显示
3. 生成的HTML文件可以直接在浏览器中打开，无需网络连接

## 自定义配置
可以通过修改 `_7_可视化.py` 中的参数来调整：
- 词云图显示的词汇数量
- 柱状图显示的数据条数
- 图表主题和颜色
- 输出文件名称
