<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts-wordcloud.min.js"></script>

    
</head>
<body >
            <style>
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
        }

        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 12px 16px;
            transition: 0.3s;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #ccc;
        }

        .chart-container {
            display: block;
        }

        .chart-container:nth-child(n+2) {
            display: none;
        }
    </style>
    <div class="tab">
            <button class="tablinks" onclick="showChart(event, 'd6dfea136cb34c2f8ddfeabdaefc5dc3')">首页</button>
            <button class="tablinks" onclick="showChart(event, '10d1beb967de4ff8b562b9b804963533')">词频分析-词云图</button>
            <button class="tablinks" onclick="showChart(event, '5aadc49e70d14962a3c3e957708e6e5b')">词频分析-柱状图</button>
            <button class="tablinks" onclick="showChart(event, '7fa2d7e3d1c04b64a49a668e5ddf73e9')">KWIC分析</button>
            <button class="tablinks" onclick="showChart(event, '69e2936cb37545e1b2527c1c5ad78ae8')">词语分布-频率图</button>
            <button class="tablinks" onclick="showChart(event, '7954c700c59f4e1d88618bcec850d1a5')">词语分布-离散度图</button>
            <button class="tablinks" onclick="showChart(event, '3990fa6f4330431bba798704ec714837')">词语分布-详细数据</button>
            <button class="tablinks" onclick="showChart(event, 'c1122931b5e34300bec01b5fae27cbf7')">词簇分析-频率图</button>
            <button class="tablinks" onclick="showChart(event, '679b5440a90c4a0986c39f8f65ab9778')">词簇分析-详细数据</button>
            <button class="tablinks" onclick="showChart(event, 'ec7a1fae98864e939eeaa16671f32305')">搭配分析-频率图</button>
            <button class="tablinks" onclick="showChart(event, 'b2ac10619c724f659ee6fa259dd52456')">搭配分析-互信息图</button>
            <button class="tablinks" onclick="showChart(event, 'ddddfc31df9f460f8cb50d46b3df8bb9')">搭配分析-详细数据</button>
    </div>

    <div class="box">
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="d6dfea136cb34c2f8ddfeabdaefc5dc3" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>分析类型</th>
            <th>说明 (TextBlob库实现)</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>词频分析</td>
            <td>使用TextBlob库进行英文分词和词形还原</td>
        </tr>
        <tr>
            <td>KWIC分析</td>
            <td>关键词上下文索引，基于TextBlob分词结果</td>
        </tr>
        <tr>
            <td>词语分布分析</td>
            <td>分析词语在不同文件中的分布情况</td>
        </tr>
        <tr>
            <td>词簇分析</td>
            <td>分析词语簇的分布和频率</td>
        </tr>
        <tr>
            <td>搭配分析</td>
            <td>分析词语的常见搭配，计算互信息和似然比</td>
        </tr>
        <tr>
            <td>特色功能</td>
            <td>简单易用的API，内置情感分析功能</td>
        </tr>
        <tr>
            <td>技术特点</td>
            <td>基于NLTK的高级封装，适合快速原型开发</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="10d1beb967de4ff8b562b9b804963533" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('10d1beb967de4ff8b562b9b804963533').style.width = document.getElementById('10d1beb967de4ff8b562b9b804963533').parentNode.clientWidth + 'px';
        var chart_10d1beb967de4ff8b562b9b804963533 = echarts.init(
            document.getElementById('10d1beb967de4ff8b562b9b804963533'), 'white', {renderer: 'canvas'});
        var option_10d1beb967de4ff8b562b9b804963533 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "wordCloud",
            "shape": "circle",
            "rotationRange": [
                -90,
                90
            ],
            "rotationStep": 45,
            "girdSize": 20,
            "sizeRange": [
                15,
                80
            ],
            "data": [
                {
                    "name": "china",
                    "value": 45,
                    "textStyle": {
                        "color": "rgb(47,72,146)"
                    }
                },
                {
                    "name": "trump",
                    "value": 33,
                    "textStyle": {
                        "color": "rgb(59,25,50)"
                    }
                },
                {
                    "name": "chinese",
                    "value": 25,
                    "textStyle": {
                        "color": "rgb(62,132,160)"
                    }
                },
                {
                    "name": "global",
                    "value": 19,
                    "textStyle": {
                        "color": "rgb(37,62,154)"
                    }
                },
                {
                    "name": "tariff",
                    "value": 15,
                    "textStyle": {
                        "color": "rgb(12,73,93)"
                    }
                },
                {
                    "name": "president",
                    "value": 14,
                    "textStyle": {
                        "color": "rgb(37,80,134)"
                    }
                },
                {
                    "name": "ha",
                    "value": 14,
                    "textStyle": {
                        "color": "rgb(123,96,8)"
                    }
                },
                {
                    "name": "tiktok",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(90,39,30)"
                    }
                },
                {
                    "name": "beijing",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(30,114,136)"
                    }
                },
                {
                    "name": "washington",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(41,116,13)"
                    }
                },
                {
                    "name": "empire",
                    "value": 12,
                    "textStyle": {
                        "color": "rgb(157,136,133)"
                    }
                },
                {
                    "name": "trade",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(110,47,92)"
                    }
                },
                {
                    "name": "cent",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(22,8,121)"
                    }
                },
                {
                    "name": "power",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(76,126,77)"
                    }
                },
                {
                    "name": "war",
                    "value": 10,
                    "textStyle": {
                        "color": "rgb(125,89,56)"
                    }
                },
                {
                    "name": "donald",
                    "value": 9,
                    "textStyle": {
                        "color": "rgb(54,151,145)"
                    }
                },
                {
                    "name": "american",
                    "value": 9,
                    "textStyle": {
                        "color": "rgb(156,60,76)"
                    }
                },
                {
                    "name": "america",
                    "value": 9,
                    "textStyle": {
                        "color": "rgb(122,125,43)"
                    }
                },
                {
                    "name": "wa",
                    "value": 8,
                    "textStyle": {
                        "color": "rgb(102,12,132)"
                    }
                },
                {
                    "name": "country",
                    "value": 8,
                    "textStyle": {
                        "color": "rgb(110,27,50)"
                    }
                },
                {
                    "name": "deal",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(24,6,66)"
                    }
                },
                {
                    "name": "life",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(123,94,81)"
                    }
                },
                {
                    "name": "month",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(32,56,37)"
                    }
                },
                {
                    "name": "house",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(98,5,145)"
                    }
                },
                {
                    "name": "economic",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(22,150,97)"
                    }
                },
                {
                    "name": "firm",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(81,49,100)"
                    }
                },
                {
                    "name": "call",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(146,157,16)"
                    }
                },
                {
                    "name": "economy",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(40,150,132)"
                    }
                },
                {
                    "name": "return",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(10,160,29)"
                    }
                },
                {
                    "name": "day",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(159,13,93)"
                    }
                },
                {
                    "name": "policy",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(127,154,77)"
                    }
                },
                {
                    "name": "administration",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(41,24,123)"
                    }
                },
                {
                    "name": "white",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(91,43,35)"
                    }
                },
                {
                    "name": "united",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(83,152,142)"
                    }
                },
                {
                    "name": "foreign",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(27,6,45)"
                    }
                },
                {
                    "name": "tech",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(151,58,21)"
                    }
                },
                {
                    "name": "company",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(56,134,53)"
                    }
                },
                {
                    "name": "military",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(32,23,124)"
                    }
                },
                {
                    "name": "diplomatic",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(160,69,126)"
                    }
                },
                {
                    "name": "century",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(108,119,15)"
                    }
                },
                {
                    "name": "time",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(66,46,149)"
                    }
                },
                {
                    "name": "bank",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(133,128,113)"
                    }
                },
                {
                    "name": "price",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(155,25,80)"
                    }
                },
                {
                    "name": "wang",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(82,3,46)"
                    }
                },
                {
                    "name": "security",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(56,149,15)"
                    }
                },
                {
                    "name": "told",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(158,15,52)"
                    }
                },
                {
                    "name": "investment",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(129,73,125)"
                    }
                },
                {
                    "name": "people",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(134,117,105)"
                    }
                },
                {
                    "name": "international",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(107,11,44)"
                    }
                },
                {
                    "name": "top",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(40,98,157)"
                    }
                },
                {
                    "name": "sale",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(33,4,101)"
                    }
                },
                {
                    "name": "range",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(5,97,145)"
                    }
                },
                {
                    "name": "canada",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(10,54,158)"
                    }
                },
                {
                    "name": "union",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(66,154,47)"
                    }
                },
                {
                    "name": "threat",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(79,108,63)"
                    }
                },
                {
                    "name": "talk",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(28,110,151)"
                    }
                },
                {
                    "name": "export",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(3,78,57)"
                    }
                },
                {
                    "name": "including",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(98,113,96)"
                    }
                },
                {
                    "name": "product",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(29,61,158)"
                    }
                },
                {
                    "name": "maga",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(21,41,159)"
                    }
                },
                {
                    "name": "retreat",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(4,88,86)"
                    }
                },
                {
                    "name": "xi",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(2,18,129)"
                    }
                },
                {
                    "name": "photo",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(84,101,75)"
                    }
                },
                {
                    "name": "border",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(82,118,76)"
                    }
                },
                {
                    "name": "dollar",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(113,134,61)"
                    }
                },
                {
                    "name": "expectancy",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(9,3,3)"
                    }
                },
                {
                    "name": "trap",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(43,157,133)"
                    }
                },
                {
                    "name": "mineral",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(142,64,133)"
                    }
                },
                {
                    "name": "bytedance",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(145,12,153)"
                    }
                },
                {
                    "name": "sell",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(58,158,55)"
                    }
                },
                {
                    "name": "set",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(53,86,33)"
                    }
                },
                {
                    "name": "ground",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(148,22,13)"
                    }
                },
                {
                    "name": "reporter",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(132,91,54)"
                    }
                },
                {
                    "name": "late",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(141,93,75)"
                    }
                },
                {
                    "name": "remain",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(104,30,22)"
                    }
                },
                {
                    "name": "operation",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(66,105,120)"
                    }
                },
                {
                    "name": "held",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(102,91,156)"
                    }
                },
                {
                    "name": "wednesday",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(100,28,93)"
                    }
                },
                {
                    "name": "appeared",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(17,120,140)"
                    }
                },
                {
                    "name": "largest",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(74,8,71)"
                    }
                },
                {
                    "name": "import",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(4,38,142)"
                    }
                },
                {
                    "name": "week",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(31,118,18)"
                    }
                },
                {
                    "name": "counterpart",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(128,128,49)"
                    }
                },
                {
                    "name": "statement",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(137,158,150)"
                    }
                },
                {
                    "name": "common",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(36,52,11)"
                    }
                },
                {
                    "name": "european",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(128,54,21)"
                    }
                },
                {
                    "name": "control",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(75,47,41)"
                    }
                },
                {
                    "name": "analyst",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(41,101,148)"
                    }
                },
                {
                    "name": "tuesday",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(151,22,61)"
                    }
                },
                {
                    "name": "entity",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(37,25,126)"
                    }
                },
                {
                    "name": "list",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(81,76,60)"
                    }
                },
                {
                    "name": "trading",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(5,96,11)"
                    }
                },
                {
                    "name": "data",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(91,92,160)"
                    }
                },
                {
                    "name": "service",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(109,152,18)"
                    }
                },
                {
                    "name": "windmill",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(140,104,79)"
                    }
                },
                {
                    "name": "history",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(136,83,96)"
                    }
                },
                {
                    "name": "decline",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(36,10,87)"
                    }
                },
                {
                    "name": "major",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(94,134,154)"
                    }
                },
                {
                    "name": "challenge",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(118,22,86)"
                    }
                },
                {
                    "name": "financial",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(41,123,90)"
                    }
                }
            ],
            "drawOutOfBound": false,
            "textStyle": {
                "normal": {
                    "fontFamily": "Arial"
                },
                "emphasis": {}
            }
        }
    ],
    "legend": [
        {
            "data": [],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u8bcd\u9891\u5206\u6790 - \u8bcd\u4e91\u56fe (TextBlob\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_10d1beb967de4ff8b562b9b804963533.setOption(option_10d1beb967de4ff8b562b9b804963533);
    </script>
                <div id="5aadc49e70d14962a3c3e957708e6e5b" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('5aadc49e70d14962a3c3e957708e6e5b').style.width = document.getElementById('5aadc49e70d14962a3c3e957708e6e5b').parentNode.clientWidth + 'px';
        var chart_5aadc49e70d14962a3c3e957708e6e5b = echarts.init(
            document.getElementById('5aadc49e70d14962a3c3e957708e6e5b'), 'white', {renderer: 'canvas'});
        var option_5aadc49e70d14962a3c3e957708e6e5b = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u8bcd\u9891",
            "legendHoverLink": true,
            "data": [
                45,
                33,
                25,
                19,
                15,
                14,
                14,
                12,
                12,
                12,
                12,
                11,
                11,
                11,
                10,
                9,
                9,
                9,
                8,
                8
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u8bcd\u9891"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "china",
                "trump",
                "chinese",
                "global",
                "tariff",
                "president",
                "ha",
                "tiktok",
                "beijing",
                "washington",
                "empire",
                "trade",
                "cent",
                "power",
                "war",
                "donald",
                "american",
                "america",
                "wa",
                "country"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u8bcd\u9891\u5206\u6790 - \u524d20\u9ad8\u9891\u8bcd (TextBlob\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_5aadc49e70d14962a3c3e957708e6e5b.setOption(option_5aadc49e70d14962a3c3e957708e6e5b);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="7fa2d7e3d1c04b64a49a668e5ddf73e9" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>左上下文</th>
            <th>关键词</th>
            <th>右上下文</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>chinese import and demand that</td>
            <td>china</td>
            <td>sell the us arm of</td>
        </tr>
        <tr>
            <td>social medium platform unit of</td>
            <td>china</td>
            <td>bytedance without sale tiktok face</td>
        </tr>
        <tr>
            <td>in tariff in return for</td>
            <td>china</td>
            <td>agreeing to tiktok sale trump</td>
        </tr>
        <tr>
            <td>they can agree on is</td>
            <td>china</td>
            <td>and the threat they see</td>
        </tr>
        <tr>
            <td>trump tech export control against</td>
            <td>china</td>
            <td>us president donald trump could</td>
        </tr>
        <tr>
            <td>of buying product to support</td>
            <td>china</td>
            <td>development of quantum and hypersonic</td>
        </tr>
        <tr>
            <td>retreat now confronted by resurgent</td>
            <td>china</td>
            <td>it most significant economic diplomatic</td>
        </tr>
        <tr>
            <td>of power or retreat xi</td>
            <td>china</td>
            <td>emerges major global player photo</td>
        </tr>
        <tr>
            <td>of the people republic of</td>
            <td>china</td>
            <td>and the subsequent material contribution</td>
        </tr>
        <tr>
            <td>sweeping reform the return of</td>
            <td>china</td>
            <td>after two century of subjugation</td>
        </tr>
        <tr>
            <td>to the us technically speaking</td>
            <td>china</td>
            <td>is no longer merely country</td>
        </tr>
        <tr>
            <td>fact explain at length in</td>
            <td>china</td>
            <td>galaxy empire is that china</td>
        </tr>
        <tr>
            <td>china galaxy empire is that</td>
            <td>china</td>
            <td>is rapidly becoming an empire</td>
        </tr>
        <tr>
            <td>orientalist ignorance and denial of</td>
            <td>china</td>
            <td>imperial ambition are the underbelly</td>
        </tr>
        <tr>
            <td>in the world are chinese</td>
            <td>china</td>
            <td>ha outflanked body such the</td>
        </tr>
        <tr>
            <td>the asian infrastructure investment bank</td>
            <td>china</td>
            <td>is spearheading the global rebellion</td>
        </tr>
        <tr>
            <td>topped the us dollar in</td>
            <td>china</td>
            <td>transaction with nearly us trillion</td>
        </tr>
        <tr>
            <td>hasn enjoyed trade surplus since</td>
            <td>china</td>
            <td>is the largest trading country</td>
        </tr>
        <tr>
            <td>despite effort to decouple from</td>
            <td>china</td>
            <td>by applying tariff penalty boycotting</td>
        </tr>
        <tr>
            <td>zte and other chinese company</td>
            <td>china</td>
            <td>economy unlike the former soviet</td>
        </tr>
        <tr>
            <td>and battery manufacturer durapower holdings</td>
            <td>china</td>
            <td>meanwhile produce of the world</td>
        </tr>
        <tr>
            <td>south korea and britain combined</td>
            <td>china</td>
            <td>is the european union and</td>
        </tr>
        <tr>
            <td>are actively drawing closer to</td>
            <td>china</td>
            <td>in the renminbi surpassed the</td>
        </tr>
        <tr>
            <td>surpassed the us dollar in</td>
            <td>china</td>
            <td>transaction for the first time</td>
        </tr>
        <tr>
            <td>shift are also happening in</td>
            <td>china</td>
            <td>in matter of everyday life</td>
        </tr>
        <tr>
            <td>expectancy is even higher among</td>
            <td>china</td>
            <td>strong middle class how are</td>
        </tr>
        <tr>
            <td>during the past two decade</td>
            <td>china</td>
            <td>now produce more stem graduate</td>
        </tr>
        <tr>
            <td>for war can america and</td>
            <td>china</td>
            <td>escape thucydides trap doe not</td>
        </tr>
        <tr>
            <td>to rewrite the pattern that</td>
            <td>china</td>
            <td>economy achieved per cent growth</td>
        </tr>
        <tr>
            <td>per cent of americans view</td>
            <td>china</td>
            <td>unfavourably trump praised chinese president</td>
        </tr>
        <tr>
            <td>president donald trump could visit</td>
            <td>china</td>
            <td>early next month according to</td>
        </tr>
        <tr>
            <td>have been around trump visiting</td>
            <td>china</td>
            <td>according to source it is</td>
        </tr>
        <tr>
            <td>is going through drastic change</td>
            <td>china</td>
            <td>hit back at trump with</td>
        </tr>
        <tr>
            <td>strategic mineral have soared in</td>
            <td>china</td>
            <td>over the past year wave</td>
        </tr>
        <tr>
            <td>with price rising rapidly since</td>
            <td>china</td>
            <td>began restricting export of the</td>
        </tr>
        <tr>
            <td>more than per cent in</td>
            <td>china</td>
            <td>while price in rotterdam have</td>
        </tr>
        <tr>
            <td>tight and many country including</td>
            <td>china</td>
            <td>and the united states have</td>
        </tr>
        <tr>
            <td>nan</td>
            <td>china</td>
            <td>top diplomat ha launched direct</td>
        </tr>
        <tr>
            <td>america an irresponsible big power</td>
            <td>china</td>
            <td>top diplomat wang yi lay</td>
        </tr>
        <tr>
            <td>during the ongoing two session</td>
            <td>china</td>
            <td>annual parliamentary gathering has it</td>
        </tr>
        <tr>
            <td>people got better or worse</td>
            <td>china</td>
            <td>will definitely take countermeasure in</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="69e2936cb37545e1b2527c1c5ad78ae8" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('69e2936cb37545e1b2527c1c5ad78ae8').style.width = document.getElementById('69e2936cb37545e1b2527c1c5ad78ae8').parentNode.clientWidth + 'px';
        var chart_69e2936cb37545e1b2527c1c5ad78ae8 = echarts.init(
            document.getElementById('69e2936cb37545e1b2527c1c5ad78ae8'), 'white', {renderer: 'canvas'});
        var option_69e2936cb37545e1b2527c1c5ad78ae8 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u8bcd\u9891",
            "legendHoverLink": true,
            "data": [
                3,
                1,
                2,
                23,
                5,
                3,
                4,
                4
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u8bcd\u9891"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "10.txt",
                "2.txt",
                "3.txt",
                "4.txt",
                "5.txt",
                "6.txt",
                "7.txt",
                "8.txt"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u8bcd\u8bed\u5206\u5e03\u5206\u6790 - \u6587\u4ef6\u9891\u7387\u5206\u5e03 (TextBlob\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_69e2936cb37545e1b2527c1c5ad78ae8.setOption(option_69e2936cb37545e1b2527c1c5ad78ae8);
    </script>
                <div id="7954c700c59f4e1d88618bcec850d1a5" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('7954c700c59f4e1d88618bcec850d1a5').style.width = document.getElementById('7954c700c59f4e1d88618bcec850d1a5').parentNode.clientWidth + 'px';
        var chart_7954c700c59f4e1d88618bcec850d1a5 = echarts.init(
            document.getElementById('7954c700c59f4e1d88618bcec850d1a5'), 'white', {renderer: 'canvas'});
        var option_7954c700c59f4e1d88618bcec850d1a5 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "line",
            "name": "\u79bb\u6563\u5ea6",
            "connectNulls": false,
            "xAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "10.txt",
                    0.7407407407407408
                ],
                [
                    "2.txt",
                    0.0
                ],
                [
                    "3.txt",
                    0.5555555555555555
                ],
                [
                    "4.txt",
                    0.9619827767275784
                ],
                [
                    "5.txt",
                    0.8888888888888888
                ],
                [
                    "6.txt",
                    0.7407407407407408
                ],
                [
                    "7.txt",
                    0.8333333333333334
                ],
                [
                    "8.txt",
                    0.8333333333333334
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        }
    ],
    "legend": [
        {
            "data": [
                "\u79bb\u6563\u5ea6"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "10.txt",
                "2.txt",
                "3.txt",
                "4.txt",
                "5.txt",
                "6.txt",
                "7.txt",
                "8.txt"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "min": 0,
            "max": 1,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u8bcd\u8bed\u5206\u5e03\u5206\u6790 - \u79bb\u6563\u5ea6\u5206\u5e03 (TextBlob\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_7954c700c59f4e1d88618bcec850d1a5.setOption(option_7954c700c59f4e1d88618bcec850d1a5);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="3990fa6f4330431bba798704ec714837" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>序号</th>
            <th>文件ID</th>
            <th>文件名</th>
            <th>总词数</th>
            <th>频率</th>
            <th>离散度</th>
            <th>分布图</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>1</td>
            <td>2</td>
            <td>10.txt</td>
            <td>114</td>
            <td>3</td>
            <td>0.7407</td>
            <td>-----------------|---|----------|-----------------</td>
        </tr>
        <tr>
            <td>2</td>
            <td>3</td>
            <td>2.txt</td>
            <td>107</td>
            <td>1</td>
            <td>0.0000</td>
            <td>-------------------------------------------|------</td>
        </tr>
        <tr>
            <td>3</td>
            <td>4</td>
            <td>3.txt</td>
            <td>88</td>
            <td>2</td>
            <td>0.5556</td>
            <td>-------|----------------------|-------------------</td>
        </tr>
        <tr>
            <td>4</td>
            <td>5</td>
            <td>4.txt</td>
            <td>605</td>
            <td>23</td>
            <td>0.9620</td>
            <td>--|----------||----||||---||-|||||--||--|||--|---|</td>
        </tr>
        <tr>
            <td>5</td>
            <td>6</td>
            <td>5.txt</td>
            <td>167</td>
            <td>5</td>
            <td>0.8889</td>
            <td>--|---------|--------------|--|-------------|-----</td>
        </tr>
        <tr>
            <td>6</td>
            <td>7</td>
            <td>6.txt</td>
            <td>74</td>
            <td>3</td>
            <td>0.7407</td>
            <td>--|----------------|-------------|----------------</td>
        </tr>
        <tr>
            <td>7</td>
            <td>8</td>
            <td>7.txt</td>
            <td>100</td>
            <td>4</td>
            <td>0.8333</td>
            <td>--|--------------------------|-----|-----------|--</td>
        </tr>
        <tr>
            <td>8</td>
            <td>9</td>
            <td>8.txt</td>
            <td>125</td>
            <td>4</td>
            <td>0.8333</td>
            <td>|-------------------|--------|-----|--------------</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="c1122931b5e34300bec01b5fae27cbf7" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('c1122931b5e34300bec01b5fae27cbf7').style.width = document.getElementById('c1122931b5e34300bec01b5fae27cbf7').parentNode.clientWidth + 'px';
        var chart_c1122931b5e34300bec01b5fae27cbf7 = echarts.init(
            document.getElementById('c1122931b5e34300bec01b5fae27cbf7'), 'white', {renderer: 'canvas'});
        var option_c1122931b5e34300bec01b5fae27cbf7 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u9891\u7387",
            "legendHoverLink": true,
            "data": [
                3,
                2,
                2
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u9891\u7387"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "dollar china",
                "return china",
                "xi china"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u8bcd\u7c07\u5206\u6790 - \u9891\u7387\u5206\u5e03 (TextBlob\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_c1122931b5e34300bec01b5fae27cbf7.setOption(option_c1122931b5e34300bec01b5fae27cbf7);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="679b5440a90c4a0986c39f8f65ab9778" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>词簇</th>
            <th>排名</th>
            <th>频率</th>
            <th>范围</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>dollar china</td>
            <td>1</td>
            <td>3</td>
            <td>1</td>
        </tr>
        <tr>
            <td>return china</td>
            <td>2</td>
            <td>2</td>
            <td>2</td>
        </tr>
        <tr>
            <td>xi china</td>
            <td>3</td>
            <td>2</td>
            <td>1</td>
        </tr>
    </tbody>
</table>
        </div>

                <div id="ec7a1fae98864e939eeaa16671f32305" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('ec7a1fae98864e939eeaa16671f32305').style.width = document.getElementById('ec7a1fae98864e939eeaa16671f32305').parentNode.clientWidth + 'px';
        var chart_ec7a1fae98864e939eeaa16671f32305 = echarts.init(
            document.getElementById('ec7a1fae98864e939eeaa16671f32305'), 'white', {renderer: 'canvas'});
        var option_ec7a1fae98864e939eeaa16671f32305 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u603b\u9891\u7387",
            "legendHoverLink": true,
            "data": [
                8,
                7,
                6,
                4,
                4,
                4,
                4,
                4,
                4,
                4,
                4,
                4,
                4,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                3,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2,
                2
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8,
                "valueAnimation": false
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u603b\u9891\u7387"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "trump",
                "empire",
                "global",
                "chinese",
                "tariff",
                "power",
                "photo",
                "border",
                "ha",
                "mid",
                "renminbi",
                "dollar",
                "cent",
                "president",
                "retreat",
                "diplomatic",
                "maga",
                "country",
                "rapidly",
                "time",
                "cross",
                "transaction",
                "surplus",
                "economy",
                "surpassed",
                "life",
                "war",
                "price",
                "popular",
                "social",
                "sale",
                "tiktok",
                "return",
                "threat",
                "international",
                "export",
                "control",
                "donald",
                "product",
                "restoration",
                "xi",
                "emerges",
                "major",
                "player",
                "people",
                "contribution",
                "sweeping",
                "reform",
                "prominence",
                "explain",
                "length",
                "galaxy",
                "bank",
                "investment",
                "trade",
                "trading",
                "half",
                "union",
                "produce",
                "japan",
                "germany",
                "south",
                "india",
                "images",
                "destined",
                "thucydides",
                "trap",
                "allison",
                "month",
                "america",
                "predict",
                "source",
                "american",
                "discussion",
                "strategic",
                "mineral",
                "metal",
                "top",
                "diplomat",
                "pressure"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u642d\u914d\u5206\u6790 - \u9891\u7387\u5206\u5e03 (TextBlob\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_ec7a1fae98864e939eeaa16671f32305.setOption(option_ec7a1fae98864e939eeaa16671f32305);
    </script>
                <div id="b2ac10619c724f659ee6fa259dd52456" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
            document.getElementById('b2ac10619c724f659ee6fa259dd52456').style.width = document.getElementById('b2ac10619c724f659ee6fa259dd52456').parentNode.clientWidth + 'px';
        var chart_b2ac10619c724f659ee6fa259dd52456 = echarts.init(
            document.getElementById('b2ac10619c724f659ee6fa259dd52456'), 'white', {renderer: 'canvas'});
        var option_b2ac10619c724f659ee6fa259dd52456 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "line",
            "name": "\u4e92\u4fe1\u606f",
            "connectNulls": false,
            "xAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "trump",
                    3.0487
                ],
                [
                    "empire",
                    4.3155
                ],
                [
                    "global",
                    3.4301
                ],
                [
                    "chinese",
                    2.4493
                ],
                [
                    "tariff",
                    3.1862
                ],
                [
                    "power",
                    3.6337
                ],
                [
                    "photo",
                    5.0931
                ],
                [
                    "border",
                    5.0931
                ],
                [
                    "ha",
                    3.2858
                ],
                [
                    "mid",
                    5.5081
                ],
                [
                    "renminbi",
                    5.5081
                ],
                [
                    "dollar",
                    5.0931
                ],
                [
                    "cent",
                    3.6337
                ],
                [
                    "president",
                    2.8707
                ],
                [
                    "retreat",
                    4.6781
                ],
                [
                    "diplomatic",
                    4.3561
                ],
                [
                    "maga",
                    4.6781
                ],
                [
                    "country",
                    3.6781
                ],
                [
                    "rapidly",
                    5.6781
                ],
                [
                    "time",
                    4.3561
                ],
                [
                    "cross",
                    5.0931
                ],
                [
                    "transaction",
                    5.0931
                ],
                [
                    "surplus",
                    5.6781
                ],
                [
                    "economy",
                    4.3561
                ],
                [
                    "surpassed",
                    5.6781
                ],
                [
                    "life",
                    3.8707
                ],
                [
                    "war",
                    3.3561
                ],
                [
                    "price",
                    4.3561
                ],
                [
                    "popular",
                    6.0931
                ],
                [
                    "social",
                    5.0931
                ],
                [
                    "sale",
                    4.0931
                ],
                [
                    "tiktok",
                    2.5081
                ],
                [
                    "return",
                    3.7712
                ],
                [
                    "threat",
                    4.0931
                ],
                [
                    "international",
                    4.0931
                ],
                [
                    "export",
                    4.0931
                ],
                [
                    "control",
                    4.5081
                ],
                [
                    "donald",
                    2.9232
                ],
                [
                    "product",
                    4.0931
                ],
                [
                    "restoration",
                    5.0931
                ],
                [
                    "xi",
                    4.0931
                ],
                [
                    "emerges",
                    5.0931
                ],
                [
                    "major",
                    4.5081
                ],
                [
                    "player",
                    5.0931
                ],
                [
                    "people",
                    4.0931
                ],
                [
                    "contribution",
                    6.0931
                ],
                [
                    "sweeping",
                    6.0931
                ],
                [
                    "reform",
                    6.0931
                ],
                [
                    "prominence",
                    6.0931
                ],
                [
                    "explain",
                    6.0931
                ],
                [
                    "length",
                    6.0931
                ],
                [
                    "galaxy",
                    6.0931
                ],
                [
                    "bank",
                    3.7712
                ],
                [
                    "investment",
                    4.0931
                ],
                [
                    "trade",
                    2.6337
                ],
                [
                    "trading",
                    4.5081
                ],
                [
                    "half",
                    6.0931
                ],
                [
                    "union",
                    4.0931
                ],
                [
                    "produce",
                    5.0931
                ],
                [
                    "japan",
                    5.0931
                ],
                [
                    "germany",
                    4.5081
                ],
                [
                    "south",
                    6.0931
                ],
                [
                    "india",
                    5.0931
                ],
                [
                    "images",
                    5.0931
                ],
                [
                    "destined",
                    5.0931
                ],
                [
                    "thucydides",
                    4.5081
                ],
                [
                    "trap",
                    4.0931
                ],
                [
                    "allison",
                    4.5081
                ],
                [
                    "month",
                    3.5081
                ],
                [
                    "america",
                    2.9232
                ],
                [
                    "predict",
                    6.0931
                ],
                [
                    "source",
                    4.5081
                ],
                [
                    "american",
                    2.9232
                ],
                [
                    "discussion",
                    5.0931
                ],
                [
                    "strategic",
                    5.0931
                ],
                [
                    "mineral",
                    4.0931
                ],
                [
                    "metal",
                    4.5081
                ],
                [
                    "top",
                    4.0931
                ],
                [
                    "diplomat",
                    4.5081
                ],
                [
                    "pressure",
                    5.0931
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        }
    ],
    "legend": [
        {
            "data": [
                "\u4e92\u4fe1\u606f"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "interval": 0,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "trump",
                "empire",
                "global",
                "chinese",
                "tariff",
                "power",
                "photo",
                "border",
                "ha",
                "mid",
                "renminbi",
                "dollar",
                "cent",
                "president",
                "retreat",
                "diplomatic",
                "maga",
                "country",
                "rapidly",
                "time",
                "cross",
                "transaction",
                "surplus",
                "economy",
                "surpassed",
                "life",
                "war",
                "price",
                "popular",
                "social",
                "sale",
                "tiktok",
                "return",
                "threat",
                "international",
                "export",
                "control",
                "donald",
                "product",
                "restoration",
                "xi",
                "emerges",
                "major",
                "player",
                "people",
                "contribution",
                "sweeping",
                "reform",
                "prominence",
                "explain",
                "length",
                "galaxy",
                "bank",
                "investment",
                "trade",
                "trading",
                "half",
                "union",
                "produce",
                "japan",
                "germany",
                "south",
                "india",
                "images",
                "destined",
                "thucydides",
                "trap",
                "allison",
                "month",
                "america",
                "predict",
                "source",
                "american",
                "discussion",
                "strategic",
                "mineral",
                "metal",
                "top",
                "diplomat",
                "pressure"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u6587\u642d\u914d\u5206\u6790 - \u4e92\u4fe1\u606f\u5206\u5e03 (TextBlob\u5e93)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_b2ac10619c724f659ee6fa259dd52456.setOption(option_b2ac10619c724f659ee6fa259dd52456);
    </script>
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="ddddfc31df9f460f8cb50d46b3df8bb9" class="chart-container" style="">
            <p class="title" > </p>
            <p class="subtitle" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>搭配词</th>
            <th>排名</th>
            <th>左频率</th>
            <th>右频率</th>
            <th>总频率</th>
            <th>范围</th>
            <th>互信息</th>
            <th>似然比</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>trump</td>
            <td>1</td>
            <td>4</td>
            <td>4</td>
            <td>8</td>
            <td>4</td>
            <td>3.0487</td>
            <td>33.8113</td>
        </tr>
        <tr>
            <td>empire</td>
            <td>2</td>
            <td>2</td>
            <td>5</td>
            <td>7</td>
            <td>1</td>
            <td>4.3155</td>
            <td>41.8779</td>
        </tr>
        <tr>
            <td>global</td>
            <td>3</td>
            <td>1</td>
            <td>5</td>
            <td>6</td>
            <td>1</td>
            <td>3.4301</td>
            <td>28.5311</td>
        </tr>
        <tr>
            <td>chinese</td>
            <td>4</td>
            <td>3</td>
            <td>1</td>
            <td>4</td>
            <td>3</td>
            <td>2.4493</td>
            <td>13.5815</td>
        </tr>
        <tr>
            <td>tariff</td>
            <td>5</td>
            <td>1</td>
            <td>3</td>
            <td>4</td>
            <td>3</td>
            <td>3.1862</td>
            <td>17.6681</td>
        </tr>
        <tr>
            <td>power</td>
            <td>6</td>
            <td>3</td>
            <td>1</td>
            <td>4</td>
            <td>2</td>
            <td>3.6337</td>
            <td>20.1494</td>
        </tr>
        <tr>
            <td>photo</td>
            <td>7</td>
            <td>0</td>
            <td>4</td>
            <td>4</td>
            <td>1</td>
            <td>5.0931</td>
            <td>28.2422</td>
        </tr>
        <tr>
            <td>border</td>
            <td>8</td>
            <td>1</td>
            <td>3</td>
            <td>4</td>
            <td>1</td>
            <td>5.0931</td>
            <td>28.2422</td>
        </tr>
        <tr>
            <td>ha</td>
            <td>9</td>
            <td>2</td>
            <td>2</td>
            <td>4</td>
            <td>3</td>
            <td>3.2858</td>
            <td>18.2201</td>
        </tr>
        <tr>
            <td>mid</td>
            <td>10</td>
            <td>3</td>
            <td>1</td>
            <td>4</td>
            <td>1</td>
            <td>5.5081</td>
            <td>30.5437</td>
        </tr>
        <tr>
            <td>renminbi</td>
            <td>11</td>
            <td>3</td>
            <td>1</td>
            <td>4</td>
            <td>1</td>
            <td>5.5081</td>
            <td>30.5437</td>
        </tr>
        <tr>
            <td>dollar</td>
            <td>12</td>
            <td>3</td>
            <td>1</td>
            <td>4</td>
            <td>1</td>
            <td>5.0931</td>
            <td>28.2422</td>
        </tr>
        <tr>
            <td>cent</td>
            <td>13</td>
            <td>2</td>
            <td>2</td>
            <td>4</td>
            <td>2</td>
            <td>3.6337</td>
            <td>20.1494</td>
        </tr>
        <tr>
            <td>president</td>
            <td>14</td>
            <td>1</td>
            <td>2</td>
            <td>3</td>
            <td>3</td>
            <td>2.8707</td>
            <td>11.9390</td>
        </tr>
        <tr>
            <td>retreat</td>
            <td>15</td>
            <td>3</td>
            <td>0</td>
            <td>3</td>
            <td>1</td>
            <td>4.6781</td>
            <td>19.4556</td>
        </tr>
        <tr>
            <td>diplomatic</td>
            <td>16</td>
            <td>1</td>
            <td>2</td>
            <td>3</td>
            <td>2</td>
            <td>4.3561</td>
            <td>18.1167</td>
        </tr>
        <tr>
            <td>maga</td>
            <td>17</td>
            <td>3</td>
            <td>0</td>
            <td>3</td>
            <td>1</td>
            <td>4.6781</td>
            <td>19.4556</td>
        </tr>
        <tr>
            <td>country</td>
            <td>18</td>
            <td>1</td>
            <td>2</td>
            <td>3</td>
            <td>2</td>
            <td>3.6781</td>
            <td>15.2967</td>
        </tr>
        <tr>
            <td>rapidly</td>
            <td>19</td>
            <td>1</td>
            <td>2</td>
            <td>3</td>
            <td>2</td>
            <td>5.6781</td>
            <td>23.6144</td>
        </tr>
        <tr>
            <td>time</td>
            <td>20</td>
            <td>1</td>
            <td>2</td>
            <td>3</td>
            <td>1</td>
            <td>4.3561</td>
            <td>18.1167</td>
        </tr>
        <tr>
            <td>cross</td>
            <td>21</td>
            <td>0</td>
            <td>3</td>
            <td>3</td>
            <td>1</td>
            <td>5.0931</td>
            <td>21.1816</td>
        </tr>
        <tr>
            <td>transaction</td>
            <td>22</td>
            <td>0</td>
            <td>3</td>
            <td>3</td>
            <td>1</td>
            <td>5.0931</td>
            <td>21.1816</td>
        </tr>
        <tr>
            <td>surplus</td>
            <td>23</td>
            <td>2</td>
            <td>1</td>
            <td>3</td>
            <td>1</td>
            <td>5.6781</td>
            <td>23.6144</td>
        </tr>
        <tr>
            <td>economy</td>
            <td>24</td>
            <td>0</td>
            <td>3</td>
            <td>3</td>
            <td>2</td>
            <td>4.3561</td>
            <td>18.1167</td>
        </tr>
        <tr>
            <td>surpassed</td>
            <td>25</td>
            <td>2</td>
            <td>1</td>
            <td>3</td>
            <td>1</td>
            <td>5.6781</td>
            <td>23.6144</td>
        </tr>
        <tr>
            <td>life</td>
            <td>26</td>
            <td>2</td>
            <td>1</td>
            <td>3</td>
            <td>2</td>
            <td>3.8707</td>
            <td>16.0979</td>
        </tr>
        <tr>
            <td>war</td>
            <td>27</td>
            <td>3</td>
            <td>0</td>
            <td>3</td>
            <td>2</td>
            <td>3.3561</td>
            <td>13.9578</td>
        </tr>
        <tr>
            <td>price</td>
            <td>28</td>
            <td>2</td>
            <td>1</td>
            <td>3</td>
            <td>1</td>
            <td>4.3561</td>
            <td>18.1167</td>
        </tr>
        <tr>
            <td>popular</td>
            <td>29</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0931</td>
            <td>16.8937</td>
        </tr>
        <tr>
            <td>social</td>
            <td>30</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>5.0931</td>
            <td>14.1211</td>
        </tr>
        <tr>
            <td>sale</td>
            <td>31</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.0931</td>
            <td>11.3485</td>
        </tr>
        <tr>
            <td>tiktok</td>
            <td>32</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>2.5081</td>
            <td>6.9541</td>
        </tr>
        <tr>
            <td>return</td>
            <td>33</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>3.7712</td>
            <td>10.4559</td>
        </tr>
        <tr>
            <td>threat</td>
            <td>34</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>2</td>
            <td>4.0931</td>
            <td>11.3485</td>
        </tr>
        <tr>
            <td>international</td>
            <td>35</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>2</td>
            <td>4.0931</td>
            <td>11.3485</td>
        </tr>
        <tr>
            <td>export</td>
            <td>36</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>2</td>
            <td>4.0931</td>
            <td>11.3485</td>
        </tr>
        <tr>
            <td>control</td>
            <td>37</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>4.5081</td>
            <td>12.4992</td>
        </tr>
        <tr>
            <td>donald</td>
            <td>38</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>2</td>
            <td>2.9232</td>
            <td>8.1048</td>
        </tr>
        <tr>
            <td>product</td>
            <td>39</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>2</td>
            <td>4.0931</td>
            <td>11.3485</td>
        </tr>
        <tr>
            <td>restoration</td>
            <td>40</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>5.0931</td>
            <td>14.1211</td>
        </tr>
        <tr>
            <td>xi</td>
            <td>41</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>4.0931</td>
            <td>11.3485</td>
        </tr>
        <tr>
            <td>emerges</td>
            <td>42</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>5.0931</td>
            <td>14.1211</td>
        </tr>
        <tr>
            <td>major</td>
            <td>43</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.5081</td>
            <td>12.4992</td>
        </tr>
        <tr>
            <td>player</td>
            <td>44</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>5.0931</td>
            <td>14.1211</td>
        </tr>
        <tr>
            <td>people</td>
            <td>45</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>4.0931</td>
            <td>11.3485</td>
        </tr>
        <tr>
            <td>contribution</td>
            <td>46</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0931</td>
            <td>16.8937</td>
        </tr>
        <tr>
            <td>sweeping</td>
            <td>47</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0931</td>
            <td>16.8937</td>
        </tr>
        <tr>
            <td>reform</td>
            <td>48</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0931</td>
            <td>16.8937</td>
        </tr>
        <tr>
            <td>prominence</td>
            <td>49</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0931</td>
            <td>16.8937</td>
        </tr>
        <tr>
            <td>explain</td>
            <td>50</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>6.0931</td>
            <td>16.8937</td>
        </tr>
        <tr>
            <td>length</td>
            <td>51</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>6.0931</td>
            <td>16.8937</td>
        </tr>
        <tr>
            <td>galaxy</td>
            <td>52</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0931</td>
            <td>16.8937</td>
        </tr>
        <tr>
            <td>bank</td>
            <td>53</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>3.7712</td>
            <td>10.4559</td>
        </tr>
        <tr>
            <td>investment</td>
            <td>54</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>4.0931</td>
            <td>11.3485</td>
        </tr>
        <tr>
            <td>trade</td>
            <td>55</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>2</td>
            <td>2.6337</td>
            <td>7.3021</td>
        </tr>
        <tr>
            <td>trading</td>
            <td>56</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.5081</td>
            <td>12.4992</td>
        </tr>
        <tr>
            <td>half</td>
            <td>57</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0931</td>
            <td>16.8937</td>
        </tr>
        <tr>
            <td>union</td>
            <td>58</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.0931</td>
            <td>11.3485</td>
        </tr>
        <tr>
            <td>produce</td>
            <td>59</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>5.0931</td>
            <td>14.1211</td>
        </tr>
        <tr>
            <td>japan</td>
            <td>60</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>5.0931</td>
            <td>14.1211</td>
        </tr>
        <tr>
            <td>germany</td>
            <td>61</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>4.5081</td>
            <td>12.4992</td>
        </tr>
        <tr>
            <td>south</td>
            <td>62</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0931</td>
            <td>16.8937</td>
        </tr>
        <tr>
            <td>india</td>
            <td>63</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>5.0931</td>
            <td>14.1211</td>
        </tr>
        <tr>
            <td>images</td>
            <td>64</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>5.0931</td>
            <td>14.1211</td>
        </tr>
        <tr>
            <td>destined</td>
            <td>65</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>5.0931</td>
            <td>14.1211</td>
        </tr>
        <tr>
            <td>thucydides</td>
            <td>66</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>4.5081</td>
            <td>12.4992</td>
        </tr>
        <tr>
            <td>trap</td>
            <td>67</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>4.0931</td>
            <td>11.3485</td>
        </tr>
        <tr>
            <td>allison</td>
            <td>68</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>4.5081</td>
            <td>12.4992</td>
        </tr>
        <tr>
            <td>month</td>
            <td>69</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>2</td>
            <td>3.5081</td>
            <td>9.7266</td>
        </tr>
        <tr>
            <td>america</td>
            <td>70</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>2.9232</td>
            <td>8.1048</td>
        </tr>
        <tr>
            <td>predict</td>
            <td>71</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>6.0931</td>
            <td>16.8937</td>
        </tr>
        <tr>
            <td>source</td>
            <td>72</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.5081</td>
            <td>12.4992</td>
        </tr>
        <tr>
            <td>american</td>
            <td>73</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>2</td>
            <td>2.9232</td>
            <td>8.1048</td>
        </tr>
        <tr>
            <td>discussion</td>
            <td>74</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>5.0931</td>
            <td>14.1211</td>
        </tr>
        <tr>
            <td>strategic</td>
            <td>75</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>5.0931</td>
            <td>14.1211</td>
        </tr>
        <tr>
            <td>mineral</td>
            <td>76</td>
            <td>2</td>
            <td>0</td>
            <td>2</td>
            <td>1</td>
            <td>4.0931</td>
            <td>11.3485</td>
        </tr>
        <tr>
            <td>metal</td>
            <td>77</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.5081</td>
            <td>12.4992</td>
        </tr>
        <tr>
            <td>top</td>
            <td>78</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.0931</td>
            <td>11.3485</td>
        </tr>
        <tr>
            <td>diplomat</td>
            <td>79</td>
            <td>0</td>
            <td>2</td>
            <td>2</td>
            <td>1</td>
            <td>4.5081</td>
            <td>12.4992</td>
        </tr>
        <tr>
            <td>pressure</td>
            <td>80</td>
            <td>1</td>
            <td>1</td>
            <td>2</td>
            <td>1</td>
            <td>5.0931</td>
            <td>14.1211</td>
        </tr>
    </tbody>
</table>
        </div>

    </div>

    <script>
    </script>
    <script>
        (function() {
            containers = document.getElementsByClassName("chart-container");
            if(containers.length > 0) {
                containers[0].style.display = "block";
            }
        })()

        function showChart(evt, chartID) {
            let containers = document.getElementsByClassName("chart-container");
            for (let i = 0; i < containers.length; i++) {
                containers[i].style.display = "none";
            }

            let tablinks = document.getElementsByClassName("tablinks");
            for (let i = 0; i < tablinks.length; i++) {
                tablinks[i].className = "tablinks";
            }

            document.getElementById(chartID).style.display = "block";
            evt.currentTarget.className += " active";
        }
    </script>
</body>
</html>
