# coding=utf-8
"""
中文文本分析 - 词频分析 (使用spaCy库)
目标：尽可能接近AntConc的分析结果
"""
import spacy
import pandas as pd
import sys
import os
from collections import defaultdict, Counter
import re

sys.stdout.reconfigure(encoding='utf-8')

# 加载中文模型
try:
    nlp = spacy.load("zh_core_web_sm")
except OSError:
    print("请先安装中文模型: python -m spacy download zh_core_web_sm")
    sys.exit(1)

def load_stopwords(stopwords_file):
    """
    加载停用词列表
    Args:
        stopwords_file: 停用词文件路径
    Returns:
        停用词集合
    """
    stopwords = set()
    if stopwords_file and os.path.exists(stopwords_file):
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            stopwords = set([line.strip() for line in f if line.strip()])
    
    # 添加标点符号和数字作为停用词
    punctuation = set(['，', '。', '！', '？', '、', '；', '：', '"', '"', ''', ''', 
                      '（', '）', '【', '】', '.', ',', '!', '?', ';', ':', '(', ')', 
                      '[', ']', '{', '}', '<', '>', '/', '\\', '|', '-', '_', '+', 
                      '=', '*', '&', '^', '%', '$', '#', '@', '~', '`'])
    stopwords.update(punctuation)
    
    return stopwords

def clean_text(text):
    """清理文本，移除特殊字符和多余空白"""
    # 移除特殊字符，保留中文、英文、数字
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)
    # 移除多余空白
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def segment_text(text, stopwords):
    """
    使用spaCy进行中文分词
    Args:
        text: 输入文本
        stopwords: 停用词集合
    Returns:
        分词后的词汇列表
    """
    # 清理文本
    text = clean_text(text)
    
    # spaCy处理
    doc = nlp(text)
    
    # 过滤停用词和短词
    filtered_words = []
    for token in doc:
        word = token.text.strip()
        lemma = token.lemma_.strip()
        
        # 使用词元形式，但如果词元为空则使用原词
        final_word = lemma if lemma and lemma != '-PRON-' else word
        
        if (len(final_word) >= 2 and  # 至少2个字符
            final_word not in stopwords and
            not token.is_stop and  # spaCy内置停用词检查
            not token.is_punct and  # 不是标点
            not token.is_space and  # 不是空白
            not final_word.isdigit() and  # 不是纯数字
            token.pos_ not in ['PUNCT', 'SPACE', 'X']):  # 过滤特定词性
            filtered_words.append(final_word)
    
    return filtered_words

def process_files(folder_path, stopwords_file):
    """
    处理文件夹中的所有文本文件
    Args:
        folder_path: 文件夹路径
        stopwords_file: 停用词文件路径
    Returns:
        词频统计字典
    """
    stopwords = load_stopwords(stopwords_file)
    word_freq = Counter()
    file_count = 0
    total_words = 0
    
    print(f"开始处理文件夹: {folder_path}")
    
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            filepath = os.path.join(folder_path, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                # 分词
                words = segment_text(text, stopwords)
                
                # 统计词频
                word_freq.update(words)
                total_words += len(words)
                file_count += 1
                
                print(f"已处理: {filename} (词数: {len(words)})")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    print(f"\n处理完成:")
    print(f"- 文件数量: {file_count}")
    print(f"- 总词数: {total_words}")
    print(f"- 不重复词数: {len(word_freq)}")
    
    return word_freq

def generate_report(word_freq):
    """
    生成词频分析报告
    Args:
        word_freq: 词频统计Counter对象
    Returns:
        DataFrame格式的报告
    """
    # 转换为列表并排序
    word_list = [(word, freq) for word, freq in word_freq.most_common()]
    
    # 计算百分比
    total_words = sum(freq for _, freq in word_list)
    
    report_data = []
    for rank, (word, freq) in enumerate(word_list, 1):
        percentage = (freq / total_words) * 100
        report_data.append({
            'Rank': rank,
            'Word': word,
            'Frequency': freq,
            'Percentage': round(percentage, 4)
        })
    
    return pd.DataFrame(report_data)

def main():
    """主函数"""
    # 配置路径
    folder_path = "../中文文本"  # 中文文本文件夹
    stopwords_file = "../stopwords-zh.txt"  # 中文停用词文件
    output_file = 'spacy_word_frequency_report.csv'
    
    print("=== 中文词频分析 (spaCy库) ===")
    print("目标：尽可能接近AntConc的分析结果\n")
    
    # 处理文件并生成报告
    word_freq = process_files(folder_path, stopwords_file)
    
    if not word_freq:
        print("未找到有效的词汇数据")
        return
    
    report_df = generate_report(word_freq)
    
    # 显示前20个结果
    print("\n=== 前20个高频词 ===")
    print(report_df.head(20).to_string(index=False))
    
    # 保存完整结果到CSV
    report_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n完整分析结果已保存至: {output_file}")
    
    # 显示统计信息
    print(f"\n=== 统计信息 ===")
    print(f"总词汇数: {len(report_df)}")
    print(f"总频次: {report_df['Frequency'].sum()}")
    print(f"平均频次: {report_df['Frequency'].mean():.2f}")

if __name__ == "__main__":
    main()
